# 爱用宝物流服务系统

## 项目简介

爱用宝物流服务系统是一个基于 Spring Boot 的微服务架构项目，提供完整的物流信息查询、跟踪和异常处理功能。系统采用模块化设计，支持多平台、多店铺的物流业务场景。

## 项目架构

本项目采用 Maven 多模块架构，包含以下核心模块：

### 核心模块

- **logistics-service** - 物流对外服务模块，提供 REST API 接口
- **logistics-api** - 二方 API 接口定义模块，包含所有对外接口规范
- **logistics-common** - 公共模块，包含通用工具类、服务实现和外部服务调用
- **logistics-onsconsumer** - 物流信息消费者模块，处理物流跟踪消息
- **logistics-abnormal-consumer** - 异常物流消费者模块，处理物流异常通知

### 技术栈

- **Java**: JDK 8+ (推荐 JDK 24)
- **框架**: Spring Boot 2.x
- **构建工具**: Maven 3.x
- **消息队列**: RocketMQ
- **数据库**: MongoDB
- **缓存**: Caffeine
- **服务发现**: Apollo 配置中心
- **监控**: Micrometer

## JDK 24 环境配置

### 系统要求

- **JDK 版本**: JDK 24 或更高版本
- **Maven 版本**: 3.6.0 或更高版本
- **内存要求**: 最小 2GB，推荐 4GB 或更多

### JDK 24 安装配置

1. **下载并安装 JDK 24**
   ```bash
   # 验证 Java 版本
   java -version
   javac -version
   ```

2. **设置环境变量**
   ```bash
   # 在 ~/.bashrc 或 ~/.zshrc 中添加
   export JAVA_HOME=/path/to/jdk-24
   export PATH=$JAVA_HOME/bin:$PATH
   ```

3. **Maven 配置**

   确保 Maven 使用 JDK 24，在项目根目录的 `pom.xml` 中已配置：
   ```xml
   <properties>
       <maven.compiler.source>24</maven.compiler.source>
       <maven.compiler.target>24</maven.compiler.target>
       <java.version>24</java.version>
   </properties>
   ```

### JDK 24 兼容性说明

- **模块系统**: 项目已适配 Java 模块系统，支持 JDK 24 的模块化特性
- **垃圾收集器**: 推荐使用 ZGC 或 G1GC 以获得更好的性能
- **JVM 参数优化**:
  ```bash
  -XX:+UseZGC
  -XX:+UnlockExperimentalVMOptions
  -Xmx2g
  -Xms2g
  ```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd logistics-services-group

# 验证 JDK 版本
java -version
```

### 2. 编译项目

```bash
# 使用 Maven 编译整个项目
mvn clean compile

# 打包项目
mvn clean package -DskipTests
```

### 3. 配置文件

在启动服务前，需要配置以下关键参数：

#### Apollo 配置中心
```properties
# application.properties
loveapp.apollo.enabled=true
app.id=cn.loveapp.logistics
apollo.bootstrap.enabled=true
```

#### 数据库配置
```properties
# MongoDB 配置
spring.data.mongodb.repositories.type=none
```

#### 缓存配置
```properties
# Caffeine 缓存配置
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=10000,expireAfterAccess=5m
```

### 4. 启动服务

#### 启动主服务
```bash
# 启动物流服务
cd logistics-service
mvn spring-boot:run

# 或使用 jar 包启动
java -jar target/logistics-service-1.1-SNAPSHOT.jar
```

#### 启动消费者服务
```bash
# 启动物流信息消费者
cd logistics-onsconsumer
mvn spring-boot:run

# 启动异常物流消费者
cd logistics-abnormal-consumer
mvn spring-boot:run
```

## 服务端口

- **logistics-service**: 18080 (生产环境)
- **logistics-onsconsumer**: 配置文件指定
- **logistics-abnormal-consumer**: 配置文件指定

## 主要功能

### 物流查询服务
- 物流信息实时查询
- 多平台物流数据整合
- 物流轨迹跟踪

### 异常处理
- 物流异常自动检测
- 异常通知推送
- 异常处理工作流

### 用户权限
- 多店铺权限管理
- VIP 用户功能限制
- 用户信息缓存

## 开发指南

### 代码规范
- 使用 Lombok 简化代码
- 遵循阿里巴巴 Java 开发手册
- 使用 LoggerHelper 进行日志记录

### 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

### 调试
```bash
# 开启调试模式启动
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar target/logistics-service-1.1-SNAPSHOT.jar
```

## 部署说明

### 生产环境部署

1. **构建生产包**
   ```bash
   mvn clean package -Pprod -DskipTests
   ```

2. **JVM 参数配置**
   ```bash
   java -server \
        -Xmx4g \
        -Xms4g \
        -XX:+UseZGC \
        -XX:+UnlockExperimentalVMOptions \
        -Dspring.profiles.active=prod \
        -jar logistics-service-1.1-SNAPSHOT.jar
   ```

3. **健康检查**
   ```bash
   curl http://localhost:18080/actuator/health
   ```

## 监控和运维

### 日志配置
- 日志文件位置: `./logs/info.log`
- 支持按日期滚动
- 集成 ELK 日志收集

### 性能监控
- 集成 Micrometer 指标收集
- 支持 Prometheus 监控
- 提供 JVM 性能指标

## 常见问题

### Q: JDK 24 下编译失败？
A: 确保 Maven 版本 >= 3.6.0，并检查 JAVA_HOME 环境变量设置。

### Q: 服务启动时连接 Apollo 失败？
A: 检查网络连接和 Apollo 配置中心地址是否正确。

### Q: MongoDB 连接异常？
A: 确认 MongoDB 服务已启动，并检查连接配置。

## 联系方式

- **开发团队**: Loveapp Inc.
- **官网**: http://www.aiyongbao.com
- **技术支持**: <EMAIL>

## 版本历史

- **v1.1-SNAPSHOT**: 当前开发版本
- **v2.0.0-SNAPSHOT**: API 版本

## 许可证

本项目为 Loveapp Inc. 内部项目，版权所有。
