package cn.loveapp.logistics.abnormal;

import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableScheduling
@EnableCaching
@EnableFeignClients(basePackages = {"cn.loveapp.uac", "cn.loveapp.logistics.common.service.external"})
@SpringBootApplication(exclude = {MybatisAutoConfiguration.class, DataSourceHealthContributorAutoConfiguration.class},
    scanBasePackages = {"cn.loveapp.logistics"})
public class LogisticsAbnormalConsumeApplication {
    /**
     * Description 程序主入口
     *
     * <AUTHOR>
     * @date 2018-09-21 23:37
     */
    public static void main(String[] args) {
        new SpringApplicationBuilder(LogisticsAbnormalConsumeApplication.class).application().run(args);
    }
}
