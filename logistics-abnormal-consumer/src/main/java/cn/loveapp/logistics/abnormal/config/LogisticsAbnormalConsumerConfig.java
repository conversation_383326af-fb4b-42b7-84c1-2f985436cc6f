package cn.loveapp.logistics.abnormal.config;

import cn.loveapp.common.mq.rocketmq.CommonDefaultMQPushConsumer;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.abnormal.consumer.LogisticsAbnormalConsumer;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.config.rocketmq.RocketMQLogisticsAbnormalConfig;
import lombok.Setter;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * LogisticsAbnormalConsumerConfig 异常物流队列消费配置
 *
 * <AUTHOR>
 * @date 2023/6/28
 */
@Configuration
public class LogisticsAbnormalConsumerConfig {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsAbnormalConsumerConfig.class);

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private RocketMQLogisticsAbnormalConfig rocketMQLogisticsAbnormalConfig;

    @Bean(destroyMethod = "shutdown")
    public DefaultMQPushConsumer logisticsRocketMqConsumer() {
        DefaultMQPushConsumer logisticsConsumer = new CommonDefaultMQPushConsumer(rocketMQLogisticsAbnormalConfig.getConsumerId());
        logisticsConsumer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
        logisticsConsumer.setConsumeThreadMax(rocketMQLogisticsAbnormalConfig.getMaxThreadNum());
        logisticsConsumer.setConsumeThreadMin(rocketMQLogisticsAbnormalConfig.getMaxThreadNum());
        logisticsConsumer.setAwaitTerminationMillisWhenShutdown(60_000);
        return logisticsConsumer;
    }

    @Bean
    public RocketMqLifeCycleManager rocketMqLifeCycleManager() {
        return new RocketMqLifeCycleManager();
    }

    /**
     * Ons 生命周期管理
     *
     * <AUTHOR>
     * @date 2018/11/9
     */
    @Setter
    public static class RocketMqLifeCycleManager implements CommandLineRunner {
        private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RocketMqLifeCycleManager.class);

        @Autowired
        private DefaultMQPushConsumer logisticsRocketMqConsumer;

        @Autowired
        private LogisticsAbnormalConsumer logisticConsumer;

        @Autowired
        private RocketMQLogisticsAbnormalConfig rocketMQLogisticsAbnormalConfig;

        @Override
        public void run(String... args) throws Exception {
            // 启动物流ONS消费者
            if (logisticsRocketMqConsumer != null) {
                logisticsRocketMqConsumer.subscribe(rocketMQLogisticsAbnormalConfig.getTopic(), "*");
                logisticsRocketMqConsumer.setMessageListener(logisticConsumer);
                logisticsRocketMqConsumer.start();
                LOGGER.logInfo("Logistic Abnormal Consumer is started, topic:" + rocketMQLogisticsAbnormalConfig.getTopic());
            }
        }
    }

}
