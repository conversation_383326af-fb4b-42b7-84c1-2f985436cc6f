package cn.loveapp.logistics.abnormal.consumer;

import cn.loveapp.common.serialization.MessageDeserializationResult;
import cn.loveapp.common.mq.AbstractCommonMQBaseConsumer;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.dto.proto.LogisticsAbnormalRequestProto;
import cn.loveapp.logistics.common.service.LogisticsAbnormalHandleService;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * LogisticsAbnormalConsumer 异常物流消费consumer
 *
 * <AUTHOR>
 * @date 2023/6/21
 */
@Component
public class LogisticsAbnormalConsumer extends AbstractCommonMQBaseConsumer {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsAbnormalConsumer.class);

    /**
     * ONS限流配置Key
     */
    protected static final String LIMIT_CONFIG_KEY = "logistics.abnormal.consumer.ratelimit";


    @Autowired
    private LogisticsAbnormalHandleService logisticsAbnormalHandleService;


    public LogisticsAbnormalConsumer(MeterRegistry registry, Environment environment) {
        super(registry, "异常物流消息消费.QPS", LIMIT_CONFIG_KEY,
            environment, false, true);
    }

    @Override
    protected ConsumeConcurrentlyStatus execute(MessageExt message, MessageDeserializationResult messageDeserializationResult) {
        LogisticsAbnormalRequestProto proto = (LogisticsAbnormalRequestProto) messageDeserializationResult.getContent();

        String sellerId = proto.getSellerId();
        String storeId = proto.getStoreId();
        String appName = proto.getAppName();
        String outSid = proto.getOutSid();
        String companyCode = proto.getCompanyCode();
        String companyName = proto.getCompanyName();
        List<String> sendAbnormalList = proto.getAbnormalTypeNotifyList();

        if (StringUtils.isAnyEmpty(sellerId, storeId, appName, outSid)) {
            LOGGER.logError("缺少参数" + proto);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }

        LogisticsHandleBo logisticsHandleBo = new LogisticsHandleBo();
        logisticsHandleBo.setSellerId(sellerId);
        logisticsHandleBo.setStoreId(storeId);
        logisticsHandleBo.setAppName(appName);
        logisticsHandleBo.setOutSid(outSid);
        logisticsHandleBo.setCompanyCode(companyCode);
        logisticsHandleBo.setCompanyName(companyName);
        logisticsHandleBo.setAbnormalCheckMessageId(message.getMsgId());

        if (CollectionUtils.isNotEmpty(sendAbnormalList)) {
            logisticsAbnormalHandleService.sendAbnormalLogisticsNotify(logisticsHandleBo, sendAbnormalList);
        } else {
            logisticsAbnormalHandleService.checkAbnormalLogistics(logisticsHandleBo, proto.isCheckAbnormal());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    @Override
    protected Class<?> getClassForDeserialization(MessageExt msg) {
        return LogisticsAbnormalRequestProto.class;
    }

}
