package cn.loveapp.logistics.abnormal;

import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Import;

/**
 * TestConfig
 *
 * <AUTHOR>
 * @date 2019-01-24
 */
@Import(value = {MetricsAutoConfiguration.class, CompositeMeterRegistryAutoConfiguration.class,
    ConfigurationPropertiesAutoConfiguration.class})
@TestConfiguration
public class TestConfig {}
