package cn.loveapp.logistics.api.constant;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 物流状态
 * 多平台对齐状态
 *
 * <AUTHOR>
 * @date 2023/6/27
 */
public enum AyLogisticsStatus {

    UNKNOWN("-1"),// 未找到映射
    NO_TRACE_INFO("0"), // 暂无轨迹信息
    PICKED_UP("1"), // 已揽收
    IN_TRANSIT("2"), // 在途中
    ARRIVAL_AT_DELIVERY_CITY("201"), // 到达派件城市
    ARRIVAL_AT_TRANSFER_CENTER("204"), // 到达转运中心
    ARRIVAL_AT_DELIVERY_BRANCH("205"), // 到达派件网点
    SENDING_FROM_SENDING_BRANCH("206"), // 寄件网点发件
    DELIVERING("202"), // 派件中
    PLACED_IN_MAILBOX_OR_SERVICE_POString("211"), // 已放入快递柜或驿站
    DELIVERED("3"), // 已签收
    NORMAL_SIGNATURE("301"), // 正常签收
    FINAL_SIGNATURE_AFTER_DELIVERY_EXCEPTION("302"), // 派件异常后最终签收
    SIGNATURE_ON_BEHALF("304"), // 代收签收
    MAILBOX_OR_SERVICE_POString_SIGNATURE("311"), // 快递柜或驿站签收
    PROBLEM_PACKAGE("4"), // 问题件
    NO_INFORMATION_ON_SHIPMENT("401"), // 发货无信息
    TIMEOUT_WITHOUT_SIGNATURE("402"), // 超时未签收
    TIMEOUT_WITHOUT_UPDATE("403"), // 超时未更新
    REFUSED_OR_RETURNED("404"), // 拒收(退件)
    DELIVERY_EXCEPTION("405"), // 派件异常
    RETURNED_AND_SIGNED("406"), // 退货签收
    RETURNED_WITHOUT_SIGNATURE("407"), // 退货未签收
    TIMEOUT_AT_MAILBOX_OR_SERVICE_POString("412"), // 快递柜或驿站超时未取
    INTERCEPTED("413"), // 单号已拦截
    DAMAGED("414"), // 破损
    CANCELED_BY_CUSTOMER("415"), // 客户取消发货
    UNABLE_TO_CONTACT("416"), // 无法联系
    DELIVERY_DELAYED("417"), // 配送延迟
    PACKAGE_TAKEN_OUT("418"), // 快件取出
    REDIRECTED_DELIVERY("419"), // 重新派送
    INCOMPLETE_ADDRESS("420"), // 收货地址不详细
    WRONG_PHONE_NUMBER("421"), // 收件人电话错误
    MISROUTED_PACKAGE("422"), // 错分件
    OUT_OF_AREA_PACKAGE("423"), // 超区件
    FORWARDING("5"), // 转寄
    CUSTOMS_CLEARANCE("6"), // 清关
    AWAITING_CUSTOMS_CLEARANCE("601"), // 待清关
    IN_CUSTOMS_CLEARANCE("602"), // 清关中
    CLEARED_CUSTOMS("603"), // 已清关
    CUSTOMS_CLEARANCE_EXCEPTION("604"), // 清关异常
    AWAITING_PICKUP("10"), // 待揽件
    OTHER("其他"); // 其他(用于物流单搜索)

    private String var;

    public String value() {
        return var;
    }

    AyLogisticsStatus(String var) {
        this.var = var;
    }

    /**
     * 揽收后派件前状态前缀
     */
    public static final String AFTER_PICK_BEFORE_DELIVER_STATUS_PREFIX = "2";

    /**
     * 已签收状态前缀
     */
    public static final String DELIVERED_STATUS_PREFIX = "3";

    /**
     * 待揽收列表
     */
    public static final List<String> AWAITING_PICKUP_LIST = Lists.newArrayList(AWAITING_PICKUP.var);

    /**
     * 已揽收列表
     */
    public static final List<String> PICKED_UP_LIST = Lists.newArrayList(PICKED_UP.var);

    /**
     * 在途中列表
     */
    public static final List<String> IN_TRANSIT_LIST = Lists.newArrayList(IN_TRANSIT.var, ARRIVAL_AT_DELIVERY_CITY.var, ARRIVAL_AT_TRANSFER_CENTER.var, ARRIVAL_AT_DELIVERY_BRANCH.var, SENDING_FROM_SENDING_BRANCH.var);

    /**
     * 派件中列表
     */
    public static final List<String> DELIVERING_LIST = Lists.newArrayList(DELIVERING.var, PLACED_IN_MAILBOX_OR_SERVICE_POString.var);

    /**
     * 签收列表（最终状态）
     */
    public static final List<String> DELIVERED_LIST = Lists.newArrayList(DELIVERED.var, NORMAL_SIGNATURE.var, FINAL_SIGNATURE_AFTER_DELIVERY_EXCEPTION.var, SIGNATURE_ON_BEHALF.var, MAILBOX_OR_SERVICE_POString_SIGNATURE.var);

    /**
     * 异常列表-多店 (除指定识别出的异常类型)
     */
    public static final List<String> EXCLUDE_OTHER_ABNORMAL_LIST = Lists.newArrayList(DELIVERY_EXCEPTION.var, REFUSED_OR_RETURNED.var, TIMEOUT_WITHOUT_SIGNATURE.var, TIMEOUT_AT_MAILBOX_OR_SERVICE_POString.var);

    /**
     * 异常列表-交易 (除指定识别出的异常类型)
     */
    public static final List<String> EXCLUDE_OTHER_ABNORMAL_LIST_TRADE = Lists.newArrayList();

    public static final List<String> OTHER_LIST = Lists.newArrayList();
    static {
        OTHER_LIST.addAll(AWAITING_PICKUP_LIST);
        OTHER_LIST.addAll(PICKED_UP_LIST);
        OTHER_LIST.addAll(IN_TRANSIT_LIST);
        OTHER_LIST.addAll(DELIVERING_LIST);
        OTHER_LIST.addAll(DELIVERED_LIST);
    }

    /**
     * 相同含义的状态值组合:key为状态名, value为代表该状态状态值列表
     */
    private static final Map<String, List<String>> STATUS_GROUP_MAP = Maps.newHashMap();
    static {
        STATUS_GROUP_MAP.put(AWAITING_PICKUP.name(), AWAITING_PICKUP_LIST);
        STATUS_GROUP_MAP.put(PICKED_UP.name(), PICKED_UP_LIST);
        STATUS_GROUP_MAP.put(IN_TRANSIT.name(), IN_TRANSIT_LIST);
        STATUS_GROUP_MAP.put(DELIVERING.name(), DELIVERING_LIST);
        STATUS_GROUP_MAP.put(DELIVERED.name(), DELIVERED_LIST);
        STATUS_GROUP_MAP.put(OTHER.name(), OTHER_LIST);
    }

    /**
     * 根据状态名获取代表该状态的状态值列表
     * @param status
     * @return
     */
    public static List<String> getStatusGroupList(String status) {
        if (StringUtils.isEmpty(status)) {
            return null;
        }
        return STATUS_GROUP_MAP.getOrDefault(status, null);
    }


}
