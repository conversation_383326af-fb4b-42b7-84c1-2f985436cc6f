package cn.loveapp.logistics.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * ONS通知的物流信息内容
 *
 * <AUTHOR>
 * @date 2018/11/27
 */
@Data
public class LogisticsInfoDTO {

    /**
     * 卖家nick
     */
    @JSONField(name = "nick")
    private String sellerNick;

    @JSONField(name = "seller_nick")
    private String sellerNickV2;
    /**
     * 订单id
     */
    private String tid;

    /**
     * 卖家id
     */
    @JSONField(name = "user_id")
    private String sellerId;

    @JSONField(name = "seller_id")
    private String sellerIdV2;

    /**
     * 物流单号
     */
    @JSONField(name = "out_sid")
    private String outSid;

    /**
     * 物流动作
     */
    private String action;

    /**
     * 更新时间
     */
    @JSONField(name = "time")
    private Date modified;

    @JSONField(name = "modified")
    private Date modifiedV2;

    /**
     * 物流状态描述
     */
    private String desc;

    /**
     * 快递公司
     */
    @JSONField(name = "company_name")
    private String companyName;

    /**
     * 快递公司
     */
    @JSONField(name = "company_code")
    private String companyCode;

    /**
     * 目的省份
     */
    private String province;


    /**
     * 应用名称 如 guanDian
     */
    @JSONField(name = "app_name")
    private String appName;

    @JSONField(name = "appName")
    private String appNameV2;

    /**
     * 平台 如 TAO PDD
     */
    @JSONField(name = "platform_id")
    private String platformId;

    /**
     * 物流轨迹平台（快递鸟、拼多多、淘宝）
     */
    @JSONField(name = "logistics_store_id")
    private String logisticsStoreId;

    /**
     * 是否需要重新订阅
     */
    @JSONField(name = "is_need_subscribe")
    private Boolean isNeedReSubscribe;

    /**
     * 多平台拉平的物流轨迹状态
     */
    private String status;

    @JSONField(name = "store_id")
    private String storeId;

    @JSONField(name = "tp_code")
    private String tpCode;

    @JSONField(name = "topic")
    private String topic;

    @JSONField(name = "tag")
    private String tag;

    @JSONField(name = "reason")
    private String reason;

    @JSONField(name = "remark")
    private String remark;

    public void setCompanyName(String companyName) {
        if (companyName == null) {
            this.companyName = StringUtils.EMPTY;
        } else {
            this.companyName = companyName;
        }
    }

}
