package cn.loveapp.logistics.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 物流信息变更传输DTO
 *
 * <AUTHOR>
 * @Date 2023/6/25 17:18
 */
@Data
@ApiModel
public class LogisticsOrderChangeDTO {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = true)
    @NotNull
    private String sellerId;

    /**
     * 用户nick
     */
    @ApiModelProperty(value = "用户nick", required = true)
    private String sellerNick;

    /**
     * 用户平台
     */
    @ApiModelProperty(value = "用户平台", required = true)
    @NotNull
    private String storeId;

    /**
     * 应用
     */
    @ApiModelProperty(value = "应用", required = true)
    @NotNull
    private String appName;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号", required = true)
    @NotNull
    private String outSid;

    /**
     * 拦截标记
     */
    @ApiModelProperty(value = "拦截标记")
    private Boolean isTagIntercepted;

    /**
     * 拦截标记时间
     */
    @ApiModelProperty(value = "拦截标记时间")
    private Date tagInterceptedModified;

    /**
     * 处理状态
     */
    @ApiModelProperty(value = "处理状态")
    private String processStatus;
}
