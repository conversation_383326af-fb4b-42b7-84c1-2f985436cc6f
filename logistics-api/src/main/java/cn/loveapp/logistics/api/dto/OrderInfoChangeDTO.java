package cn.loveapp.logistics.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-18 18:20
 * @description: 同步订单信息DTO
 */
@Data
public class OrderInfoChangeDTO {

    @ApiModelProperty(value = "用户id")
    private String sellerId;

    @ApiModelProperty(value = "用户nick")
    private String sellerNick;

    @ApiModelProperty(value = "平台")
    private String storeId;

    @ApiModelProperty(value = "应用")
    private String appName;

    @ApiModelProperty(value = "运单号列表")
    private List<String> outSids;

    @ApiModelProperty(value = "订单信息")
    private OrderInfoDTO orderInfo;

}
