package cn.loveapp.logistics.api.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-19 09:42
 * @description: 订单信息DTO
 */
@Data
public class OrderInfoDTO {

    /**
     * 订单号
     */
    private String tid;

    /**
     * 订单旗帜
     */
    private Integer sellerFlag;

    /**
     * 订单自定义旗帜
     */
    private Integer orderAyCustomFlag;

    /**
     * 订单备注
     */
    private String sellerMemo;

    /**
     * 卖家留言
     */
    private String buyerMessage;

    /**
     * 退款创建时间
     */
    private LocalDateTime refundCreatedTime;

    /**
     * 是否退款
     */
    private Boolean isRefund;

    /**
     * sku信息列表
     */
    private List<OrderSkuInfoDTO> skuInfoList;
}
