package cn.loveapp.logistics.api.dto.proto;

import cn.loveapp.logistics.api.dto.LogisticsOrderChangeDTO;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.dto.OrderInfoChangeDTO;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 物流单消息传说request
 *
 * <AUTHOR>
 * @Date 2023/7/11 12:19
 */
@Data
public class LogisticsOrderRequestProto {

    /**
     * 物流列表
     */
    @JsonProperty("logistics_handles")
    @JSONField(name = "logistics_handles")
    private List<LogisticsOrderSubscribeDTO> logisticsHandles;

    /**
     * 物流单打标
     */
    @JsonProperty("logistics_update_infos")
    @JSONField(name = "logistics_update_infos")
    private List<LogisticsOrderChangeDTO> logisticsUpdateInfos;

    /**
     * 订单信息变更
     */
    @JsonProperty("order_info_change")
    @JSONField(name = "order_info_change")
    private OrderInfoChangeDTO orderInfoChange;

}
