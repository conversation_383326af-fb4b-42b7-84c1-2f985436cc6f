package cn.loveapp.logistics.api.dto.proto;

import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

/**
 * LogisticsTraceRequestProto
 *
 * <AUTHOR>
 * @date 2022/1/23
 */
@Data
public class LogisticsTraceRequestProto {

    @JSONField(name = "notify_logistics")
    private List<LogisticsInfoDTO> notifyLogistics;

    /**
     * 物流平台
     */
    @JSONField(name = "logistics_store_id")
    private String logisticsStoreId;

    /**
     * 物流应用
     */
    @JSONField(name = "logistics_app_name")
    private String logisticsAppName;

    /**
     * 是否比较Modified
     */
    @JSONField(name = "check_modified")
    private boolean checkModified = true;

    /**
     * 转换为 JSONObject
     * @param logisticsTraceRequestProto
     * @return
     */
    public static JSONObject toOriginalJson(LogisticsTraceRequestProto logisticsTraceRequestProto) {
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        List<LogisticsInfoDTO> notifyLogistics = logisticsTraceRequestProto.getNotifyLogistics();
        stringObjectHashMap.put("notify_logistics", (notifyLogistics.size() == 1 ? notifyLogistics.get(0) : notifyLogistics));
        stringObjectHashMap.put("logistics_store_id", logisticsTraceRequestProto.getLogisticsStoreId());
        stringObjectHashMap.put("logistics_app_name", logisticsTraceRequestProto.getLogisticsAppName());
        stringObjectHashMap.put("check_modified", logisticsTraceRequestProto.isCheckModified());
        return (JSONObject) JSON.toJSON(stringObjectHashMap);
    }

}
