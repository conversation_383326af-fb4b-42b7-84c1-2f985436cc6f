package cn.loveapp.logistics.api.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 物流action 转换request
 *
 * <AUTHOR>
 * @Date 2023/7/10 16:29
 */
@Data
@ApiModel
public class LogisticsActionTransformRequest {

    /**
     * 来源物流平台
     */
    @ApiModelProperty(value = "来源物流平台")
    private String sourceLogisticsStoreId;


    /**
     * 物流状态列表
     */
    @ApiModelProperty(value = "物流状态列表")
    private List<String> actionList;

}
