package cn.loveapp.logistics.api.request;

import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 物流订阅Rpc接口request
 *
 * <AUTHOR>
 * @Date 2023/5/31 18:30
 */
@Data
@ApiModel
public class LogisticsTraceSubscribeRequest {

    /**
     * 物流列表
     */
    @ApiModelProperty(value = "物流列表", required = true)
    @NotNull
    private List<LogisticsOrderSubscribeDTO> logisticsHandles;

}
