package cn.loveapp.logistics.api.request;

import cn.loveapp.logistics.api.dto.LogisticsOrderChangeDTO;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 物流变更同步request
 *
 * <AUTHOR>
 * @Date 2023/6/25 16:07
 */
@Data
public class LogisticsUpdateRequest {

    /**
     * 物流变更列表
     */
    @ApiModelProperty(value = "物流变更列表", required = true)
    @NotNull
    private List<LogisticsOrderChangeDTO> logisticsUpdates;
}
