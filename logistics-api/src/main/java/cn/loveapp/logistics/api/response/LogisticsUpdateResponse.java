package cn.loveapp.logistics.api.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 物流变更同步response
 *
 * <AUTHOR>
 * @Date 2023/6/25 16:06
 */
@Data
@ApiModel
public class LogisticsUpdateResponse {

    /**
     * 更新结果
     */
    @ApiModelProperty("更新结果")
    private boolean success;

    /**
     * 更新失败运单列表
     */
    @ApiModelProperty("更新失败运单列表")
    private List<String> errorOutSidList;


    public void addErrorOutSid(String outSid) {
        if (errorOutSidList == null) {
            errorOutSidList = new ArrayList<>();
        }
        errorOutSidList.add(outSid);
    }

}
