package cn.loveapp.logistics.api.service;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.logistics.api.request.LogisticsActionTransformRequest;
import cn.loveapp.logistics.api.request.LogisticsTraceSubscribeRequest;
import cn.loveapp.logistics.api.request.LogisticsUpdateRequest;
import cn.loveapp.logistics.api.response.LogisticsActionTransformResponse;
import cn.loveapp.logistics.api.response.LogisticsTraceSubscribeResponse;
import cn.loveapp.logistics.api.response.LogisticsUpdateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 物流相关RPC接口
 *
 * <AUTHOR>
 * @Date 2023/5/31 18:02
 */
@FeignClient(name = "logisticsService", url = "${loveapp.service.rpc.logisticsService.host:http://127.0.0.1:8080}",
    path = LogisticsRpcApiService.PATH)
public interface LogisticsRpcApiService {
    String PATH = "/export/logistics";

    /**
     * 物流轨迹订阅
     *
     * @param request
     * @return
     */
    @RequestMapping("/logistics.trace.subscribe")
    CommonApiResponse<LogisticsTraceSubscribeResponse> logisticsTraceSubscribe(@RequestBody LogisticsTraceSubscribeRequest request);


    /**
     * 物流变更同步
     *
     * @param request
     * @return
     */
    @RequestMapping("/logistics.update")
    CommonApiResponse<LogisticsUpdateResponse> logisticsUpdate(@RequestBody LogisticsUpdateRequest request);


    /**
     * 物流状态映射关系获取
     *
     * @param request
     * @return
     */
    @RequestMapping("/logistics.action.transform.get")
    CommonApiResponse<LogisticsActionTransformResponse> logisticsActionTransformGet(@RequestBody LogisticsActionTransformRequest request);

}
