<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>logistics-services-group</artifactId>
        <groupId>cn.loveapp.logistics</groupId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>logistics-common</artifactId>
    <name>爱用宝基础服务-物流服务-通用工具</name>
    <description>爱用宝基础服务-物流服务-通用工具</description>

    <dependencies>
        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-spring-boot-web-starter</artifactId>
        </dependency>

        <!--调用uac接口-->
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.orders</groupId>
            <artifactId>orders-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.logistics</groupId>
            <artifactId>logistics-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-platformsdk-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-tools</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

    </dependencies>
</project>
