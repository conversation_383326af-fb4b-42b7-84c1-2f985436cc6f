package cn.loveapp.logistics.common.bo;

import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.api.dto.*;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 物流入库前的信息
 *
 * <AUTHOR>
 * @Date 2023/6/8 14:52
 */
@Data
public class LogisticsHandleBo {

    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 卖家nick
     */
    private String sellerNick;

    /**
     * 平台id
     */
    private String storeId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 物流单号(分片键)
     */
    private String outSid;

    /**
     * 物流单关联物流平台（异常校验、消息转发，只根据该平台处理）
     */
    private String associatedLogisticsStoreId;

    /**
     * 物流公司名称
     */
    private String companyCode;

    /**
     * 物流公司名称
     */
    private String companyName;

    /**
     * 订单号列表
     */
    private List<String> tids;

    /**
     * 运单号列表
     */
    private List<String> refundIds;

    /**
     * 是否订阅成功
     */
    private Boolean isSubscribeSuccess;

    /**
     * 订阅失败原因
     */
    private String errorMsg;

    /**
     * 异常校验消息标识
     */
    private boolean checkAbnormal = false;

    /**
     * 订单发货时间
     */
    private Date consignTime;

    /**
     * 拦截标记
     */
    private Boolean isTagIntercepted;

    /**
     * 拦截标记时间
     */
    private Date tagInterceptedModified;

    /**
     * 处理状态
     */
    private String processStatus;

    /**
     * 物流单是否第一次更新（判断异常消息是否转发）
     */
    private Boolean isOrderInfoFirstUpdate;

    /**
     * 物流单
     */
    private LogisticsOrderInfo logisticsOrderInfo;

    /**
     * 消息推送的物流轨迹（部分物流平台每次都推送全部的物流轨迹）
     */
    private List<LogisticsTraceInfo> msgLogisticsTraceInfos;

    /**
     * 新物流轨迹信息（当前消息新增的，和库内已存在去重）
     */
    private List<LogisticsTraceInfo> newLogisticsTraceInfos;

    /**
     * 全部物流轨迹信息(包括当前最新入库的)
     */
    private List<LogisticsTraceInfo> allLogisticsTraceInfos;

    /**
     * 当前发送中的异常物流校验队列的消息ID
     */
    private String abnormalCheckMessageId;

    /**
     * 业务类型
     */
    private BusinessType businessType;

    /**
     * 是否比较Modified
     */
    private boolean checkModified = true;

    /**
     * 买家昵称
     */
    private String buyerNick;

    /**
     * 买家OpenUid
     */
    private String buyerOpenUid;

    /**
     * 是否异常打标用户
     */
    private Boolean isAbnormalAutoCheckUser;

    /**
     * 订单号
     */
    private String tid;

    /**
     * 订单变更信息 (每次修改是全量 部分字段变更可变为空)
     */
    private List<OrderInfoDTO> orderInfoList;


    /**
     * 轨迹入库时生成LogisticsHandleBo
     *
     * @param logisticsOrderInfo
     * @param logisticsTraceInfos
     * @return
     */
    public static LogisticsHandleBo generalLogisticsHandleBo(LogisticsOrderInfo logisticsOrderInfo,
        List<LogisticsTraceInfo> logisticsTraceInfos, Boolean isAbnormalAutoCheckUser) {
        LogisticsHandleBo logisticsHandleBo = new LogisticsHandleBo();
        BeanUtils.copyProperties(logisticsOrderInfo, logisticsHandleBo);
        if (!CollectionUtils.isEmpty(logisticsTraceInfos)) {
            logisticsTraceInfos.forEach(logisticsTraceInfo -> {
                // 物流轨迹推送selleId可能不准，从物流单表取
                logisticsTraceInfo.setSellerId(logisticsOrderInfo.getSellerId());
                logisticsTraceInfo.setPlatformId(logisticsOrderInfo.getStoreId());
                logisticsTraceInfo.setAppName(logisticsOrderInfo.getAppName());
            });
            logisticsHandleBo.setMsgLogisticsTraceInfos(logisticsTraceInfos);
        }
        if (!Objects.isNull(logisticsOrderInfo.getBusinessInfo())) {
            LogisticsOrderInfo.BusinessInfo businessInfo = logisticsOrderInfo.getBusinessInfo();
            if (CollectionUtils.isNotEmpty(businessInfo.getTidList())) {
                logisticsHandleBo.setTids(Lists.newArrayList(businessInfo.getTidList()));
            }
            if (CollectionUtils.isNotEmpty(businessInfo.getRefundIdList())) {
                logisticsHandleBo.setRefundIds(Lists.newArrayList(businessInfo.getRefundIdList()));
            }
        }
        // 物流单关联平台
        logisticsHandleBo.setAssociatedLogisticsStoreId(logisticsOrderInfo.getSaveLogisticsStoreId());
        logisticsHandleBo.setIsAbnormalAutoCheckUser(isAbnormalAutoCheckUser);
        return logisticsHandleBo;
    }


    /**
     * 订阅物流时生成LogisticsHandleBo
     *
     * @param logisticsHandle
     * @param isSubscribeSuccess 是否订阅成功
     * @param errorMsg           订阅异常信息
     * @return
     */
    public static LogisticsHandleBo generalLogisticsHandleBo(LogisticsOrderSubscribeDTO logisticsHandle, Boolean isSubscribeSuccess, String errorMsg) {
        LogisticsHandleBo logisticsHandleBo = new LogisticsHandleBo();
        logisticsHandleBo.setSellerId(logisticsHandle.getSellerId());
        logisticsHandleBo.setSellerNick(logisticsHandle.getSellerNick());
        logisticsHandleBo.setStoreId(logisticsHandle.getStoreId());
        logisticsHandleBo.setAppName(logisticsHandle.getAppName());

        logisticsHandleBo.setOutSid(logisticsHandle.getOutSid());
        logisticsHandleBo.setCompanyCode(logisticsHandle.getLogisticsCompanyCode());
        logisticsHandleBo.setCompanyName(logisticsHandle.getLogisticsCompanyName());
        logisticsHandleBo.setConsignTime(logisticsHandle.getConsignTime());

        logisticsHandleBo.setIsSubscribeSuccess(isSubscribeSuccess);
        logisticsHandleBo.setErrorMsg(errorMsg);

        // 入库规则、业务信息
        if (BusinessType.refund.equals(logisticsHandle.getBusinessType())) {
            logisticsHandleBo.setRefundIds(logisticsHandle.getBusinessIds());
        } else {
            logisticsHandleBo.setTids(logisticsHandle.getBusinessIds());
        }
        logisticsHandleBo.setBusinessType(logisticsHandle.getBusinessType());
        logisticsHandleBo.setAssociatedLogisticsStoreId(logisticsHandle.getLogisticsStoreId());

        logisticsHandleBo.setBuyerNick(logisticsHandle.getBuyerNick());
        logisticsHandleBo.setBuyerOpenUid(logisticsHandle.getBuyerOpenUid());
        return logisticsHandleBo;
    }

    public static LogisticsHandleBo generalLogisticsHandleBo(LogisticsOrderSubscribeDTO logisticsHandle,
                                                             Boolean isSubscribeSuccess, String errorMsg, List<OrderInfoDTO> orderInfoList) {
        LogisticsHandleBo logisticsHandleBo = new LogisticsHandleBo();
        logisticsHandleBo.setSellerId(logisticsHandle.getSellerId());
        logisticsHandleBo.setSellerNick(logisticsHandle.getSellerNick());
        logisticsHandleBo.setStoreId(logisticsHandle.getStoreId());
        logisticsHandleBo.setAppName(logisticsHandle.getAppName());

        logisticsHandleBo.setOutSid(logisticsHandle.getOutSid());
        logisticsHandleBo.setCompanyCode(logisticsHandle.getLogisticsCompanyCode());
        logisticsHandleBo.setCompanyName(logisticsHandle.getLogisticsCompanyName());
        logisticsHandleBo.setConsignTime(logisticsHandle.getConsignTime());

        logisticsHandleBo.setIsSubscribeSuccess(isSubscribeSuccess);
        logisticsHandleBo.setErrorMsg(errorMsg);

        // 入库规则、业务信息
        if (BusinessType.refund.equals(logisticsHandle.getBusinessType())) {
            logisticsHandleBo.setRefundIds(logisticsHandle.getBusinessIds());
        } else {
            logisticsHandleBo.setTids(logisticsHandle.getBusinessIds());
        }
        logisticsHandleBo.setBusinessType(logisticsHandle.getBusinessType());
        logisticsHandleBo.setAssociatedLogisticsStoreId(logisticsHandle.getLogisticsStoreId());

        logisticsHandleBo.setBuyerNick(logisticsHandle.getBuyerNick());
        logisticsHandleBo.setBuyerOpenUid(logisticsHandle.getBuyerOpenUid());
        logisticsHandleBo.setOrderInfoList(orderInfoList);
        return logisticsHandleBo;
    }

    /**
     * 物流单变更时生成LogisticsHandleBo
     *
     * @param lastLogisticsOrderInfo
     * @param logisticsChange
     * @return
     */
    public static LogisticsHandleBo generalLogisticsHandleBo(LogisticsOrderInfo lastLogisticsOrderInfo, LogisticsOrderChangeDTO logisticsChange) throws LogisticsHandlesException {
        String appName = logisticsChange.getAppName();
        LogisticsHandleBo logisticsHandleBo = new LogisticsHandleBo();
        logisticsHandleBo.setSellerId(logisticsChange.getSellerId());
        logisticsHandleBo.setSellerNick(logisticsChange.getSellerNick());
        logisticsHandleBo.setStoreId(logisticsChange.getStoreId());
        logisticsHandleBo.setAppName(appName);
        logisticsHandleBo.setOutSid(logisticsChange.getOutSid());
        logisticsHandleBo.setCompanyCode(lastLogisticsOrderInfo.getCompanyCode());
        logisticsHandleBo.setCompanyName(logisticsHandleBo.getCompanyName());

        boolean isExistsChange = false;

        // 拦截标记
        if (!Objects.isNull(logisticsChange.getIsTagIntercepted()) && !logisticsChange.getIsTagIntercepted().equals(lastLogisticsOrderInfo.getIsTagIntercepted())) {
            logisticsHandleBo.setIsTagIntercepted(logisticsChange.getIsTagIntercepted());
            isExistsChange = true;
        }

        // 标记拦截更新时间
        if (!Objects.isNull(logisticsChange.getTagInterceptedModified()) && !logisticsChange.getTagInterceptedModified().equals(lastLogisticsOrderInfo.getTagInterceptedModified())) {
            logisticsHandleBo.setTagInterceptedModified(logisticsChange.getTagInterceptedModified());
            isExistsChange = true;
        }

        // 处理状态
        if (!Objects.isNull(logisticsChange.getProcessStatus())) {
            LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = lastLogisticsOrderInfo.getLogisticsAbnormalInfo();
            if (logisticsAbnormalInfo == null) {
                throw new LogisticsHandlesException("该订单不存在异常，无需处理");
            }
            String processStatus = logisticsChange.getProcessStatus();
            String lastProcessStatus = logisticsAbnormalInfo.getProcessStatus();
            if (checkProcessStatusUpdate(lastProcessStatus, processStatus, appName)) {
                logisticsHandleBo.setProcessStatus(processStatus);
                isExistsChange = true;
            } else {
                throw new LogisticsHandlesException("物流单: " + logisticsHandleBo.getOutSid() + "状态为：" + lastProcessStatus + ", 不允许更新为：" + processStatus);
            }
        }



        if (isExistsChange) {
            // 存在变更才返回
            return logisticsHandleBo;
        }

        return null;
    }

    public static LogisticsHandleBo generalLogisticsHandleBo(LogisticsOrderInfo lastLogisticsOrderInfo,
        OrderInfoChangeDTO orderInfoChangeDTO) {
        // 处理订单同步信息
        if (lastLogisticsOrderInfo == null) {
            return null;
        }

        LogisticsOrderInfo.BusinessInfo businessInfo = lastLogisticsOrderInfo.getBusinessInfo();
        if (businessInfo == null) {
            // 如果为空说明创建物流单的时候有问题
            return null;
        }

        LogisticsHandleBo logisticsHandleBo = new LogisticsHandleBo();
        logisticsHandleBo.setSellerId(orderInfoChangeDTO.getSellerId());
        logisticsHandleBo.setSellerNick(orderInfoChangeDTO.getSellerNick());
        logisticsHandleBo.setStoreId(orderInfoChangeDTO.getStoreId());
        logisticsHandleBo.setAppName(orderInfoChangeDTO.getAppName());
        logisticsHandleBo.setOutSid(lastLogisticsOrderInfo.getOutSid());
        logisticsHandleBo.setCompanyCode(lastLogisticsOrderInfo.getCompanyCode());
        logisticsHandleBo.setCompanyName(logisticsHandleBo.getCompanyName());

        OrderInfoDTO orderChangeInfo = orderInfoChangeDTO.getOrderInfo();
        boolean isOrderChange = false;
        List<LogisticsOrderInfo.OrderInfo> orderInfoList = businessInfo.getOrderInfoList();
        if (CollectionUtils.isNotEmpty(orderInfoList)) {
            for (LogisticsOrderInfo.OrderInfo dbOrderInfo : orderInfoList) {
                if (orderChangeInfo != null) {
                    // 更新订单信息
                    if (Objects.equals(dbOrderInfo.getTid(), orderChangeInfo.getTid())) {
                        if (!isOrderChange
                            && !Objects.equals(orderChangeInfo.getSellerFlag(), dbOrderInfo.getSellerFlag())) {
                            isOrderChange = true;
                            logisticsHandleBo.setOrderInfoList(Lists.newArrayList(orderChangeInfo));
                        }

                        if (!isOrderChange
                            && !Objects.equals(orderChangeInfo.getSellerMemo(), dbOrderInfo.getSellerMemo())) {
                            isOrderChange = true;
                            logisticsHandleBo.setOrderInfoList(Lists.newArrayList(orderChangeInfo));
                        }

                        if (!isOrderChange
                            && !Objects.equals(orderChangeInfo.getBuyerMessage(), dbOrderInfo.getBuyerMessage())) {
                            isOrderChange = true;
                            logisticsHandleBo.setOrderInfoList(Lists.newArrayList(orderChangeInfo));
                        }

                        if (!isOrderChange
                            && !Objects.equals(orderChangeInfo.getOrderAyCustomFlag(), dbOrderInfo.getOrderAyCustomFlag())) {
                            isOrderChange = true;
                            logisticsHandleBo.setOrderInfoList(Lists.newArrayList(orderChangeInfo));
                        }

                        if (!isOrderChange && orderChangeInfo.getRefundCreatedTime() != null
                            && (dbOrderInfo.getRefundCreatedTime() == null || orderChangeInfo.getRefundCreatedTime()
                                .isBefore(dbOrderInfo.getRefundCreatedTime()))) {
                            isOrderChange = true;
                            logisticsHandleBo.setOrderInfoList(Lists.newArrayList(orderChangeInfo));
                        }

                    }
                }
            }
        }

        if (isOrderChange) {
            return logisticsHandleBo;
        }

        return null;
    }

    /**
     * 判断物流单处理状态是否允许更新(ERP应用支持状态回退)
     *
     * @param lastProcessStatus
     * @param newProcessStatus
     * @return
     */
    public static boolean checkProcessStatusUpdate(String lastProcessStatus, String newProcessStatus, String appName) {
        if (StringUtils.isEmpty(newProcessStatus)) {
            return false;
        }

        if (StringUtils.isEmpty(lastProcessStatus)) {
            // 不存在，直接更新
            return true;
        } else if (lastProcessStatus.equals(newProcessStatus)) {
            // 与上次相同，跳过
            return false;
        } else if (!LogisticsUtil.isTradeERP(appName)
            && AbnormalProcessStatus.PROCESSING.value().equals(lastProcessStatus)
            && !AbnormalProcessStatus.PROCESSED.value().equals(newProcessStatus)) {
            // 物流单为处理中，只能更新为已处理, 跳过
            return false;
        }
        return true;
    }
}
