package cn.loveapp.logistics.common.config;

import cn.loveapp.common.utils.LoggerHelper;
import com.ctrip.framework.apollo.ConfigFile;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.core.enums.ConfigFileFormat;
import com.ctrip.framework.apollo.model.ConfigFileChangeEvent;
import jakarta.annotation.PostConstruct;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.yaml.snakeyaml.Yaml;

import java.util.List;
import java.util.Map;

/**
 * 物流多平台配置
 *
 * <AUTHOR>
 * @Date 2023/6/29 10:17
 */
@Configuration
public class AyLogisticsConfiguration {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyLogisticsConfiguration.class);

    private AyLogisticsStatusConfig ayLogisticsStatusConfig = new AyLogisticsStatusConfig();

    private Yaml yaml = new Yaml();
    private final static String NAMESPACE = "logistics_action";


    @PostConstruct
    private void init() {
        // 从apollo获取json格式的配置
        ConfigFile configFile = ConfigService.getConfigFile(NAMESPACE, ConfigFileFormat.YML);
        configFile.addChangeListener((ConfigFileChangeEvent changeEvent) -> {
            try {
                // 监听配置变更
                initStatusConfig(changeEvent.getNewValue());
            } catch (Exception e) {
                LOGGER.logError("物流action映射配置变更失败: " + e.getMessage(), e);
            }
        });
        initStatusConfig(configFile.getContent());
    }

    @Bean
    public AyLogisticsStatusConfig ayLogisticsStatusConfig() {
        return ayLogisticsStatusConfig;
    }

    private void initStatusConfig(String content) {
        try {
            Map<String, Map<String, List<String>>> statusMapping = yaml.load(content);;
            ayLogisticsStatusConfig.generalActionToStatus(statusMapping);
        } catch (Exception e) {
            LOGGER.logError("配置物流action映射失败: " + e.getMessage(), e);
            throw new IllegalArgumentException(e);
        }
    }

}
