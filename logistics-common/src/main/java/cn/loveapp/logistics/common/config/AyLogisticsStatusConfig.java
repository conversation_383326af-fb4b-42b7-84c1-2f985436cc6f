package cn.loveapp.logistics.common.config;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 物流流转action
 *
 * <AUTHOR>
 * @date 2018/12/13
 */
@Data
public class AyLogisticsStatusConfig {
    private static LoggerHelper LOGGER = LoggerHelper.getLogger(AyLogisticsStatusConfig.class);

    /**
     * 物流状态映射
     */
    private Map<String, Map<String, AyLogisticsStatus>> actionToStatus = new HashMap<>();


    public void generalActionToStatus(Map<String, Map<String, List<String>>> statusMapping) {
        if (statusMapping == null) {
            return;
        }
        if (actionToStatus == null) {
            actionToStatus = new HashMap<>();
        }

        // 遍历每个键值对
        for (Map.Entry<String, Map<String, List<String>>> entry : statusMapping.entrySet()) {
            String status = entry.getKey();
            Map<String, List<String>> storeIdToActions = entry.getValue();

            // 遍历每个平台的action
            for (Map.Entry<String, List<String>> secondEntry : storeIdToActions.entrySet()) {
                String storeId = secondEntry.getKey();
                List<String> actions = secondEntry.getValue();
                Map<String, AyLogisticsStatus> secondLevelMap = actionToStatus.get(storeId);
                if (secondLevelMap == null) {
                    secondLevelMap = new HashMap<>();
                }
                for (String action : actions) {
                    secondLevelMap.put(action, AyLogisticsStatus.valueOf(status));
                    actionToStatus.put(storeId, secondLevelMap);
                }
            }
        }
    }


    /**
     * 物流action转换为对应的status
     * @param action
     * @param logisticsStoreId
     * @return
     */
    public AyLogisticsStatus getStatus(String action, String logisticsStoreId) {

        if (StringUtils.isAnyEmpty(action, logisticsStoreId)) {
            return AyLogisticsStatus.UNKNOWN;
        }

        if (actionToStatus == null || CollectionUtils.isEmpty(actionToStatus.keySet())) {
            LOGGER.logError("物流转换初始化失败");
            return AyLogisticsStatus.UNKNOWN;
        }

        Map<String, AyLogisticsStatus> ayLogisticsStatusMap = actionToStatus.get(logisticsStoreId);
        if (Objects.isNull(ayLogisticsStatusMap)) {
            LOGGER.logError("该平台未配置，logisticsStoreId：" + logisticsStoreId);
            return AyLogisticsStatus.UNKNOWN;
        }

        AyLogisticsStatus ayLogisticsStatus = ayLogisticsStatusMap.get(action);
        if (Objects.isNull(ayLogisticsStatus)) {
            LOGGER.logError("该action未配置，logisticsStoreId：" + logisticsStoreId + "，action：" + action);
            return AyLogisticsStatus.UNKNOWN;
        }

        return ayLogisticsStatus;
    }

}
