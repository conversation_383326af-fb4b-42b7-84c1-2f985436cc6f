package cn.loveapp.logistics.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 物流异常校验配置时间
 *
 * <AUTHOR>
 * @Date 2023/8/2 16:09
 */
@Data
@Configuration
public class LogisticsAbnormalCheckConfig {

    /**
     * 发货N小时后未揽件最大判断时间
     */
    @Value("${logistics.abnormal.check.notPickedUpAfterSendMaxCheckTime:20}")
    private int notPickedUpAfterSendMaxCheckTime;

    /**
     * 发货N小时后未揽件最小判断时间
     */
    @Value("${logistics.abnormal.check.notPickedUpAfterSendMinCheckTime:12}")
    private int notPickedUpAfterSendMinCheckTime;

    /**
     * 标记拦截后N小时未截返最大判断时间
     */
    @Value("${logistics.abnormal.check.notReturnedOfInterceptionMaxCheckTime:48}")
    private int notReturnedOfInterceptionMaxCheckTime;

    /**
     * 标记拦截后N小时未截返最小判断时间
     */
    @Value("${logistics.abnormal.check.notReturnedOfInterceptionMinCheckTime:12}")
    private int notReturnedOfInterceptionMinCheckTime;

    /**
     * 揽收后签收前轨迹超N小时未更新最大判断时间
     */
    @Value("${logistics.abnormal.check.trackingNotUpdatedAfterOfPickupMaxCheckTime:48}")
    private int trackingNotUpdatedAfterOfPickupMaxCheckTime;

    /**
     * 揽收后N小时未更新第一次物流轨迹最小判断时间
     */
    @Value("${logistics.abnormal.check.firstTraceAfterPickedUpMinCheckTime:12}")
    private int firstTraceAfterPickedUpTimeoutMinCheckTime;
    /**
     * 揽收后N小时未更新第一次物流轨迹最大判断时间
     */
    @Value("${logistics.abnormal.check.firstTraceAfterPickedUpMaxCheckTime:48}")
    private int firstTraceAfterPickedUpTimeoutMaxCheckTime;

    /**
     * 揽收后N小时未更新第一次物流轨迹最小判断时间
     */
    @Value("${logistics.abnormal.check.firstTraceAfterPickedUpWillTimeoutMinCheckTime:12}")
    private int firstTraceAfterPickedUpWillTimeoutMinCheckTime;
    /**
     * 揽收后N小时未更新第一次物流轨迹最大判断时间
     */
    @Value("${logistics.abnormal.check.firstTraceAfterPickedUpWillTimeoutMaxCheckTime:48}")
    private int firstTraceAfterPickedUpWillTimeoutMaxCheckTime;

    /**
     * 揽收并且第一次物流更新后，签收前，物流N小时未更新最小判断时间
     */
    @Value("${logistics.abnormal.check.transferTimeoutMinCheckTime:12}")
    private int transferTimeoutMinCheckTime;

    /**
     * 揽收并且第一次物流更新后，签收前，物流N小时未更新最大判断时间
     */
    @Value("${logistics.abnormal.check.transferTimeoutMaxCheckTime:48}")
    private int transferTimeoutMaxCheckTime;

    /**
     * 派送中物流N小时未更新最小判断时间
     */
    @Value("${logistics.abnormal.check.deliverySignTimeOutMinCheckTime:12}")
    private int deliverySignTimeOutMinCheckTime;

    /**
     * 派送中物流N小时未更新最大判断时间
     */
    @Value("${logistics.abnormal.check.deliverySignTimeOutMaxCheckTime:48}")
    private int deliverySignTimeOutMaxCheckTime;

    /**
     * 揽收后签收前轨迹超N小时未更新最小判断时间
     */
    @Value("${logistics.abnormal.check.trackingNotUpdatedAfterOfPickupMinCheckTime:12}")
    private int trackingNotUpdatedAfterOfPickupMinCheckTime;

    /**
     * 异常包裹和关键字异常物流轨迹最小判断时间
     */
    @Value("${logistics.abnormal.check.parcelAbnormalAndCustomKeywordMinCheckTime:12}")
    private int parcelAbnormalAndCustomKeywordMinCheckTime;
    /**
     * 异常包裹和关键字异常物流轨迹最大判断时间
     */
    @Value("${logistics.abnormal.check.parcelAbnormalAndCustomKeywordMaxCheckTime:48}")
    private int parcelAbnormalAndCustomKeywordMaxCheckTime;
}
