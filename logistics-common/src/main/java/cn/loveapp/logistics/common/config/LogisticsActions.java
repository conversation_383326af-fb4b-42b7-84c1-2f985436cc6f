package cn.loveapp.logistics.common.config;

import java.util.HashSet;
import java.util.Set;

import cn.loveapp.logistics.common.dto.LogisticsStatus;
import lombok.Data;

/**
 * 物流流转action
 *
 * <AUTHOR>
 * @date 2018/12/13
 */
@Data
public class LogisticsActions {
    /**
     * 不知如何分类需要忽略的action
     */
    private Set<String> unknowns = new HashSet<>();
    /**
     * 成功或关闭的action
     */
    private Set<String> ends = new HashSet<>();
    /**
     * 已发货未揽收的action
     */
    private Set<String> noPackages = new HashSet<>();
    /**
     * 运输中的action
     */
    private Set<String> transportations = new HashSet<>();
    /**
     * 已签收的action
     */
    private Set<String> signeds = new HashSet<>();
    /**
     * 需要发送短信的action
     */
    private Set<String> sms = new HashSet<>();

    /**
     * 物流action转换为对应的status
     *
     * @param action
     * @return
     * <AUTHOR>
     * @date 2018年10月11日 下午8:43:27
     */
    public LogisticsStatus getStatus(String action) {
        // status 已发货未揽收、运输途中、已签收
        // 0、订单已完成或关闭 —— TRADE_SUCCESS
        // 1、已发货未揽件 —— CREATE CONSIGN
        // 2、运输途中 —— GOT ARRIVAL DEPARTURE SENT_CITY
        // 3、已签收 —— SIGNED SENT_SCAN TRADE_SUCCESS
        // 9、未知物流 —— 不处理异常物流

        // 默认运输中
        // 所有订单自身异常都不再算异常订单, 只有监控超时才算异常订单
        LogisticsStatus status = LogisticsStatus.TRANSPORTATION;

        if (ends.contains(action)) {
            status = LogisticsStatus.CLOSE;
        } else if (noPackages.contains(action)) {
            status = LogisticsStatus.NOPACKAGE;
        } else if (signeds.contains(action)) {
            status = LogisticsStatus.SIGNED;
        } else if (unknowns.contains(action)) {
            status = LogisticsStatus.UNKNOWN;
        }

        // todo 兼容快递鸟action拉平
        return status;
    }
}
