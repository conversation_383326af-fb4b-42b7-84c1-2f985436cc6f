package cn.loveapp.logistics.common.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.google.common.collect.Lists;

import lombok.Data;

/**
 * 物流配置类
 *
 * <AUTHOR>
 * @Date 2023/6/6 17:28
 */
@Data
@Configuration
public class LogisticsConfig {

    @Value("${logistics.taobao.ons.logistics.topic}")
    private String topic;

    @Value("${logistics.taobao.ons.logistics.producerId}")
    private String producerId;

    @Value("${logistics.taobao.ons.sms.producerid}")
    private String smsProducerId;

    @Value("${logistics.taobao.ons.logistics.ConsumeThreadNums:300}")
    private int logisticsConsumeThreadNums;

    @Value("${logistics.taobao.ons.logistics.group-id}")
    private String groupId;

    @Value("${logistics.taobao.ons.logistics.namesrv-addr}")
    private String namesrvAddr;

    /**
     * 运单锁超时时间 (秒)
     */
    @Value("${logistics.batch.outSid.lock.timeout:10}")
    private int outSidLockTimeout;

    /**
     * mcRouter转发topic
     * todo 改为通用配置项, 支持向多个topic分发指定的action
     */
    @Value("${logistics.multi.mc.router.topic}")
    private String mcRouterTopic;

    /**
     * 是否保存全部物流单的es记录（true：全保存， false：只保存异常）
     */
    @Value("${logistics.es.saveAll.enable:false}")
    private Boolean saveAllEsEnable;

    /**
     * 异常物流统计缓存超时时间（天）
     */
    @Value("${logistics.abnormal.redis.timeoutDays:1}")
    private int abnormalRedisTimeoutDays;

    /**
     * 降级配置
     */
    @Value("${logistics.api.return-empty.enable:false}")
    private boolean returnEmpty;

    /**
     * 自动转发消息topic
     */
    @Value("${logistics.ons.custom.topic:iycustom}")
    private String customTopic;

    @Value("${logistics.user.vipLevel.professional:6,8}")
    private List<Integer> professionalVipLevel = Lists.newArrayList();

    /**
     * 交易专业版物流开关
     */
    @Value("${logistics.trade.professional.enabled:true}")
    private boolean logisticsTradeProfessionalEnable;

    /**
     * 物流最长未更新天数
     */
    @Value("${logistics.overdueUpdate.timeoutDays:7}")
    private int overdueUpdateTimeoutDays;

    /**
     * 校验异常物流时需要拉取api获取物流轨迹的物流平台
     */
    @Value("${logistics.abnormal.pullApi.logisticsStoreIdList:TAO}")
    private List<String> abnormalPullApuLogisticsStoreIdList = Lists.newArrayList();

    /**
     * 淘宝订单 mcRouter 转发topic
     */
    @Value("${logistics.ons.order-tao-mc-router.topic:iytmctradenotify}")
    private String taobaoMcRouterTopic;

    /**
     * tradePc host(PHP)
     */
    @Value("${loveapp.service.rpc.tradepcService.host:}")
    private String tradePcHost;
}
