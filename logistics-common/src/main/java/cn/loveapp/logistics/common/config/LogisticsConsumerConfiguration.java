package cn.loveapp.logistics.common.config;

import java.io.IOException;
import java.io.InputStream;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.yaml.snakeyaml.Yaml;

/**
 * LogisticsConfig
 *
 * <AUTHOR>
 * @date 2018/12/13
 */
@Configuration
@EnableConfigurationProperties(LogisticsConsumerProperties.class)
public class LogisticsConsumerConfiguration {
    /**
     * 读取物流action配置
     *
     * @return
     * @throws IOException
     */
    @Bean
    public LogisticsActions logisticsActions() throws IOException {
        Yaml yaml = new Yaml();
        LogisticsActions actions;
        try (InputStream inputStream = new ClassPathResource("logisitics_actions.yml").getInputStream()) {
            actions = yaml.loadAs(inputStream, LogisticsActions.class);
        }
        return actions;
    }
}
