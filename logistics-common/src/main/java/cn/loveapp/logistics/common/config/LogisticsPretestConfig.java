package cn.loveapp.logistics.common.config;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 灰度配置
 *
 * <AUTHOR>
 * @Date 2023/6/9 17:45
 */
@Data
@Configuration
public class LogisticsPretestConfig {

    @Value("${logistics.pretest.users:}")
    private List<String> pretestUsers = Lists.newArrayList();

    /**
     * 预发物流消费消息队列
     */
    @Value("${orders.pretest.logistics.topic:iylogistics2_pretest}")
    private String logisticsTopic;


}
