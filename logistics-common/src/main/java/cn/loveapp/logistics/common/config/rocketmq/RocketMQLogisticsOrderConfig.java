package cn.loveapp.logistics.common.config.rocketmq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 物流单队列 rocketMQ配置
 *
 * <AUTHOR>
 * @Date 2023/6/20 15:22
 */
@Component
@ConfigurationProperties(prefix = "rocketmq.logistics.order.config")
@Data
public class RocketMQLogisticsOrderConfig extends RocketMQDefaultConfig {

    /**
     * 消息存活最大时间
     */
    private long maxCheckDays = 20;


    /**
     * 消息延迟等级（默认30分钟，等级15）
     */
    private int delayTimeLevel = 15;
}
