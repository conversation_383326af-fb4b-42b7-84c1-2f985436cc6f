package cn.loveapp.logistics.common.config.rocketmq;

import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * RocketMQ 多平台 mc路由配置
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
@Component
@ConfigurationProperties("rocketmq.logistics.router.config")
@Data
public class RocketMQLogisticsRouterConfig {

    private Map<String, Config> targets = Maps.newHashMap();

    @Data
    public static class Config {

        /**
         * 转发目的topic
         */
        private String toTopic;

        /**
         * 转发目的tag
         */
        private String toTag = "*";

        /**
         * 延时消费等级
         */
        private int delayTimeLevel = 0;
    }
}
