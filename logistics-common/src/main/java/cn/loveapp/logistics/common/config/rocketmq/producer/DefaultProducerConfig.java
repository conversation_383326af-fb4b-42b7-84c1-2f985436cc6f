package cn.loveapp.logistics.common.config.rocketmq.producer;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.core.Ordered;

/**
 * mq生产者配置
 * @program: orders-services-group
 * @description: defaultProducerConfig
 * @author: Jason
 * @create: 2018-12-07 17:51
 **/
@Configuration
public class DefaultProducerConfig {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DefaultProducerConfig.class);

	private DefaultMQProducer producer = null;

    @Autowired
    private LogisticsConfig logisticsConfig;

	@Bean(destroyMethod = "", name = "defaultProducer")
	public DefaultMQProducer defaultProducer(){
		//启动ONS消息队列
		try{
			producer = new DefaultMQProducer(logisticsConfig.getProducerId());
			producer.setSendMsgTimeout(5000);
			producer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
		}catch(Exception e){
			LOGGER.logError("create defaultProducer failed", e);
		}
		return producer;
	}


	@Bean(name = "defaultProducerLifeCycleManager")
	public OnsLifeCycleManager onsLifeCycleManager(){
		return new OnsLifeCycleManager();
	}

	/**
	 * Ons 生命周期管理
	 *
	 * <AUTHOR>
	 * @date 2018/11/9
	 */
	public static class OnsLifeCycleManager implements CommandLineRunner, ApplicationListener<ContextClosedEvent>,
		Ordered {
		private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsLifeCycleManager.class);

		@Autowired(required = false)
		@Qualifier("defaultProducer")
		private DefaultMQProducer defaultMQProducer;

		@Override
		public void run(String... args) throws Exception {
			//启动订单ONS生产者
			if (defaultMQProducer != null) {
				defaultMQProducer.start();
				LOGGER.logInfo("defaultProducer startted");
			}
		}

		@Override
		public void onApplicationEvent(ContextClosedEvent event) {
			if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
				return;
			}
			if(defaultMQProducer != null){
				LOGGER.logInfo("正在关闭defaultProducer...");
				try {
					defaultMQProducer.shutdown();
				} catch (IllegalStateException e) {
				} catch (Exception e) {
					LOGGER.logError(e.getMessage(), e);
				}
				LOGGER.logInfo("defaultProducer已关闭");
			}

		}

		@Override
		public int getOrder() {
			return Ordered.LOWEST_PRECEDENCE;
		}
	}
}
