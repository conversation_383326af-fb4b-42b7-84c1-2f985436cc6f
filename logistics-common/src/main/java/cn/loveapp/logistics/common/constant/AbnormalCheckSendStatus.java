package cn.loveapp.logistics.common.constant;

/**
 * 异常物流校验发送状态状态
 *
 * <AUTHOR>
 * @Date 2023/6/25 11:54
 */
public enum AbnormalCheckSendStatus {

    /**
     * 未发送
     */
    NOT_SEND,
    /**
     * 发送中
     */
    SENDING,
    /**
     * 发送结束
     */
    SEND_END;

    /**
     * 判断是否未发送消息
     * @param abnormalCheckSendStatus
     * @return
     */
    public static boolean checkIsNotSend(AbnormalCheckSendStatus abnormalCheckSendStatus) {
        if (abnormalCheckSendStatus == null) {
            return true;
        }
        return NOT_SEND.equals(abnormalCheckSendStatus);
    }
}
