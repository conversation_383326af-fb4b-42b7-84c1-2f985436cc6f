package cn.loveapp.logistics.common.constant;

/**
 * 物流单ES搜索常量
 *
 * <AUTHOR>
 * @Date 2023/6/25 11:54
 */
public class EsFields {

    public static final String KEYWORD = ".keyword";
    public static final String ANALYZER_IK_SMART = "ik_smart";

    public static final String id = "id";

    public static final String gmtCreate = "gmtCreate";

    public static final String gmtModified = "gmtModified";

    /**
     * 卖家nick
     */
    public static final String sellerNick = "sellerNick";

    /**
     * 企业id
     */
    public static final String corpId = "corpId";

    /**
     * 卖家id
     */
    public static final String sellerId = "sellerId";


    /**
     * 平台 TAO JD。
     */
    public static final String storeId = "storeId";

    /**
     * 应用
     */
    public static final String appName = "appName";

    /**
     * 物流单创建时间
     */
    public static final String created = "created";

    /**
     * 是否已经被删除
     */
    public static final String isDeleted = "isDeleted";

    /**
     * 物流单最后更新时间
     */
    public static final String modified = "modified";

    /**
     * 运单号
     */
    public static final String outSid = "outSid";

    /**
     * 异常物流信息.发货后N小时未揽件标识
     */
    public static final String notPickedUpAfterSendIsExists = "logisticsAbnormalInfo.notPickedUpAfterSend.isExists";

    /**
     * 异常物流信息.揽收将要超时
     */
    public static final String collectionWillTimeoutExists = "logisticsAbnormalInfo.collectionWillTimeout.isExists";

    /**
     * 异常物流信息.标记拦截未截返标识
     */
    public static final String notReturnedOfInterceptionIsExists = "logisticsAbnormalInfo.notReturnedOfInterception.isExists";

    /**
     * 异常物流信息.揽收后签收前轨迹超N小时未更新标识
     */
    public static final String trackingNotUpdatedAfterOfPickupIsExists = "logisticsAbnormalInfo.trackingNotUpdatedAfterOfPickup.isExists";

    /**
     * 异常物流信息.揽收后一次更新超时N小时未更新标识
     */
    public static final String firstTraceAfterPickedUpTimeoutIsExists = "logisticsAbnormalInfo.firstTraceAfterPickedUpTimeout.isExists";

    /**
     * 异常物流信息.揽收后一次更新将要超时N小时未更新标识
     */
    public static final String firstTraceAfterPickedUpWillTimeoutIsExists = "logisticsAbnormalInfo.firstTraceAfterPickedUpWillTimeout.isExists";

    /**
     * 异常物流信息.揽收后第一次更新物流后签收前N小时未更新标识
     */
    public static final String transferTimeoutIsExists = "logisticsAbnormalInfo.transferTimeout.isExists";

    /**
     * 异常物流信息.派签超时标识
     */
    public static final String deliverySignTimeOutIsExists = "logisticsAbnormalInfo.deliverySignTimeOut.isExists";

    /**
     * 异常物流信息.派件异常标识
     */
    public static final String deliveryExceptionIsExists = "logisticsAbnormalInfo.deliveryException.isExists";

    /**
     * 异常物流信息.拒收标识
     */
    public static final String refusedAcceptIsExists = "logisticsAbnormalInfo.refusedAccept.isExists";

    /**
     * 异常物流信息.超时未签收标识
     */
    public static final String notDeliveredOnTimeIsExists = "logisticsAbnormalInfo.notDeliveredOnTime.isExists";

    /**
     * 异常物流信息.其他异常标识
     */
    public static final String otherAbnormalIsExists = "logisticsAbnormalInfo.otherAbnormal.isExists";

    /**
     * 异常物流信息.其他异常标识(交易)
     */
    public static final String otherAbnormalOfTradeAppIsExists = "logisticsAbnormalInfo.otherAbnormalOfTradeApp.isExists";

    /**
     * 异常包裹和关键字异常
     */
    public static final String parcelAbnormalAndCustomKeywordIsExists = "logisticsAbnormalInfo.parcelAbnormalAndCustomKeyword.isExists";


    /**
     * 异常物流信息.发货后N小时未揽件处理状态
     */
    public static final String notPickedUpAfterSendProcessStatus = "logisticsAbnormalInfo.notPickedUpAfterSend.processStatus";

    /**
     * 异常物流信息.揽收将要处理状态
     */
    public static final String collectionWillTimeoutProcessStatus = "logisticsAbnormalInfo.collectionWillTimeout.processStatus";

    /**
     * 异常物流信息.揽收后一次更新超时N小时处理状态
     */
    public static final String firstTraceAfterPickedUpTimeoutProcessStatus = "logisticsAbnormalInfo.firstTraceAfterPickedUpTimeout.processStatus";

    /**
     * 异常物流信息.揽收后一次更新超时N小时处理状态
     */
    public static final String firstTraceAfterPickedUpWillTimeoutProcessStatus = "logisticsAbnormalInfo.firstTraceAfterPickedUpWillTimeout.processStatus";


    /**
     * 异常物流信息.揽收后第一次更新物流后签收前N小时处理状态
     */
    public static final String transferTimeoutProcessStatus = "logisticsAbnormalInfo.transferTimeout.processStatus";

    /**
     * 异常物流信息.派签超时处理状态
     */
    public static final String deliverySignTimeOutProcessStatus = "logisticsAbnormalInfo.deliverySignTimeOut.processStatus";

    /**
     * 异常物流信息.标记拦截未截返处理状态
     */
    public static final String notReturnedOfInterceptionProcessStatus = "logisticsAbnormalInfo.notReturnedOfInterception.processStatus";

    /**
     * 异常物流信息.揽收后签收前轨迹超N小时未更新处理状态
     */
    public static final String trackingNotUpdatedAfterOfPickupProcessStatus = "logisticsAbnormalInfo.trackingNotUpdatedAfterOfPickup.processStatus";

    /**
     * 异常物流信息.派件异常处理状态
     */
    public static final String deliveryExceptionProcessStatus = "logisticsAbnormalInfo.deliveryException.processStatus";

    /**
     * 异常物流信息.拒收处理状态
     */
    public static final String refusedAcceptProcessStatus = "logisticsAbnormalInfo.refusedAccept.processStatus";

    /**
     * 异常物流信息.超时未签收处理状态
     */
    public static final String notDeliveredOnTimeProcessStatus = "logisticsAbnormalInfo.notDeliveredOnTime.processStatus";

    /**
     * 异常物流信息.其他异常处理状态
     */
    public static final String otherAbnormalProcessStatus = "logisticsAbnormalInfo.otherAbnormal.processStatus";

    /**
     * 异常物流信息.其他异常处理状态(交易)
     */
    public static final String otherAbnormalOfTradeAppProcessStatus = "logisticsAbnormalInfo.otherAbnormalOfTradeApp.processStatus";

    /**
     * 异常包裹和关键字异常处理状态
     */
    public static final String parcelAbnormalAndCustomKeywordProcessStatus = "logisticsAbnormalInfo.parcelAbnormalAndCustomKeyword.processStatus";

    /**
     * 是否标记已拦截
     */
    public static final String isTagIntercepted = "isTagIntercepted";

    /**
     * 售后单列表
     */
    public static final String businessInfoRefundIdList = "businessInfo.refundIdList";

    /**
     * 发货时间
     */
    public static final String consignTime = "businessInfo.consignTime";

    /**
     * 订单列表
     */
    public static final String businessInfoTIdList = "businessInfo.tidList";

    /**
     * 买家openUid
     */
    public static final String buyerOpenUid = "businessInfo.buyerOpenUid";

    /**
     * 买家nick
     */
    public static final String buyerNick = "businessInfo.buyerNick";
    public static final String buyerNickKeyword = "businessInfo.buyerNick.keyword";

    /**
     * 订单旗帜列表
     */
    public static final String businessInfoSellerFlagList = "businessInfo.orderSellerFlag";

    /**
     * 订单自定义旗帜
     */
    public static final String businessInfoOrderAyCustomFlag = "businessInfo.orderAyCustomFlag";

    /**
     * 订单备注列表
     */
    public static final String businessInfoSellerMemo = "businessInfo.orderSellerMemo";
    public static final String businessInfoSellerMemoKeyword = "businessInfo.orderSellerMemo.keyword";

    /**
     * 买家留言列表
     */
    public static final String businessInfoBuyerMessage = "businessInfo.orderBuyerMessage";

    /**
     * 买家留言列表
     */
    public static final String businessInfoBuyerMessageKeyword = "businessInfo.orderBuyerMessage.keyword";

    /**
     * sku名称列表
     */
    public static final String businessInfoSkuNameList = "businessInfo.skuNameList";

    /**
     * sku外部编码列表
     */
    public static final String businessInfoOuterSkuIdList = "businessInfo.outerSkuIdList";
    public static final String businessInfoOuterSkuIdListKeyword = "businessInfo.outerSkuIdList.keyword";

    /**
     * 订单是否退款
     */
    public static final String businessInfoIsRefund = "businessInfo.isRefund";

    /**
     * 退款创建时间
     */
    public static final String businessInfoRefundCreatedTime= "businessInfo.refundCreatedTime";


    /**
     * 物流最新状态
     */
    public static final String lastAction = "lastAction";

    /**
     * 物流公司code
     */
    public static final String companyCode = "companyCode";

    /**
     * 物流公司Name
     */
    public static final String companyName = "companyName";

    /**
     * 物流单处理状态
     */
    public static final String logisticsAbnormalInfoProcessStatus = "logisticsAbnormalInfo.processStatus";

}
