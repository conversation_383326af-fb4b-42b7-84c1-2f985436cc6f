package cn.loveapp.logistics.common.constant;

/**
 * 物流异常类型枚举
 *
 * <AUTHOR>
 * @Date 2023/6/20 12:07
 */
public enum LogisticsAbnormal {

    /**
     * 发货N小时后未揽件
     */
    NO_PICKED_UP_AFTER_SEND("notPickedUpAfterSend"),

    /**
     * 揽收后更新超时
     */
    FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT("firstTraceAfterPickedUpTimeout"),

    /**
     * 揽收后更新将要超时
     */
    FIRST_TRACE_AFTER_PICKED_UP_WILL_TIMEOUT("firstTraceAfterPickedUpWillTimeout"),

    /**
     * 标记拦截后N小时未截返
     */
    NOT_RETURNED_OF_INTERCEPTION("notReturnedOfInterception"),

    /**
     * 中转超时
     */
    TRANSFER_TIMEOUT("transferTimeout"),

    /**
     * 揽收后签收前轨迹超N小时未更新
     */
    TRACKING_NOT_UPDATE_AFTER_OF_PICKUP("trackingNotUpdatedAfterOfPickup"),

    /**
     * 其他异常
     */
    OTHER_ABNORMAL("otherAbnormal"),

    /**
     * 交易其他异常
     */
    OTHER_ABNORMAL_Of_TRADE_APP("otherAbnormalOfTradeApp"),

    /**
     * 派件异常
     */
    DELIVERY_ABNORMAL("deliveryException"),

    /**
     * 拒收
     */
    REFUSED_ACCEPT("refusedAccept"),

    /**
     * 派签超时
     */
    DELIVERY_SIGN_TIMEOUT("deliverySignTimeOut"),


    /**
     * 超时未签收
     */
    NOT_DELIVERED_ON_TIME("notDeliveredOnTime"),

    /**
     * 异常包裹（丢失、拒收、退回）和自定义关键字异常
     */
    ABNORMAL_PARCEL_AND_CUSTOM_KEYWORD("abnormalParcelAndCustomKeyword"),

    /**
     * 揽收将超时
     */
    COLLECTION_WILL_TIMEOUT("collectionWillTimeout"),

    ;


    private String value;

    public String  value() {
        return value;
    }

    LogisticsAbnormal(String value) {
        this.value = value;
    }

}
