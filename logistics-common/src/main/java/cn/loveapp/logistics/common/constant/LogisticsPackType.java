package cn.loveapp.logistics.common.constant;

import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import org.apache.commons.lang3.BooleanUtils;

/**
 * 包裹类型枚举
 *
 * <AUTHOR>
 * @Date 2023/6/8 11:55
 */
public enum LogisticsPackType {

    /**
     * 正常发货件
     */
    NORMAL_DELIVERY,

    /**
     * 商家拦截件
     */
    MERCHANT_INTERCEPT,

    /**
     * 买家退货件
     */
    BUYER_RETURN;


    /**
     * 获取包裹类型
     * @param orderInfo
     */
    public static LogisticsPackType getPackType(LogisticsOrderInfo orderInfo) {
        if (orderInfo == null) {
            return null;
        }
        LogisticsOrderInfo.BusinessInfo businessInfo = orderInfo.getBusinessInfo();
        if (businessInfo != null) {
            if (businessInfo.getRefundIdList() != null) {
                return LogisticsPackType.BUYER_RETURN;
            } else if (BooleanUtils.isTrue(orderInfo.getIsTagIntercepted())) {
                return LogisticsPackType.MERCHANT_INTERCEPT;
            }
        }
        return LogisticsPackType.NORMAL_DELIVERY;
    }

    /**
     * 获取包裹类型
     *
     * @param logisticsHandle
     */
    public static LogisticsPackType getPackType(LogisticsOrderSubscribeDTO logisticsHandle) {
        if (logisticsHandle == null) {
            return null;
        }
        BusinessType businessType = logisticsHandle.getBusinessType();
        if (BusinessType.refund.equals(businessType)) {
            return LogisticsPackType.BUYER_RETURN;
        }
        return LogisticsPackType.NORMAL_DELIVERY;
    }
}
