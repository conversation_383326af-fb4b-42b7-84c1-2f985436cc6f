package cn.loveapp.logistics.common.constant;

import cn.loveapp.common.constant.CommonAppConstants;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户异常配置项常量
 *
 * <AUTHOR>
 * @Date 2023/7/13 14:18
 */
public class UserAbnormalSettingConstant {

    /**
     * 发货后xx小时未揽件提醒时间(揽收超时)
     */
    public static final String LOGISTICS_UN_COLLECT_TIMEOUT = "abnormal.logistics.uncollectTimeout";

    /**
     * 发货后xx小时未揽件提醒时间(揽收超时)开关
     */
    public static final String LOGISTICS_UN_COLLECT_TIMEOUT_ENABLE = "abnormal.logistics.uncollectTimeout.enable";

    /**
     * 揽收将超时
     */
    public static final String LOGISTICS_COLLECTION_WILL_TIMEOUT = "abnormal.logistics.collectionWillTimeout";

    /**
     * 揽收将超时开关
     */
    public static final String LOGISTICS_COLLECTION_WILL_TIMEOUT_ENABLE = "abnormal.logistics.collectionWillTimeout.enable";


    /**
     * 揽收后xx小时未更新第一次物流开关
     */
    public static final String LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT_ENABLE = "abnormal.logistics.firstTraceAfterPickedUpTimeout.enable";

    /**
     * 揽收后xx小时未更新第一次物流
     */
    public static final String LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT = "abnormal.logistics.firstTraceAfterPickedUpTimeout";

    /**
     * 揽收后xx小时未更新第一次物流开关（将要超时）
     */
    public static final String LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_WILL_TIMEOUT_ENABLE = "abnormal.logistics.firstTraceAfterPickedUpWillTimeout.enable";

    /**
     * 揽收后xx小时未更新第一次物流（将要超时）
     */
    public static final String LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_WILL_TIMEOUT = "abnormal.logistics.firstTraceAfterPickedUpWillTimeout";

    /**
     * 中转超时开关
     */
    public static final String LOGISTICS_TRANSFER_TIMEOUT_ENABLE = "abnormal.logistics.transferTimeout.enable";

    /**
     * 揽收后第一次物流更新后签收前xx小时未更新物流(中转超时)
     */
    public static final String LOGISTICS_TRANSFER_TIMEOUT = "abnormal.logistics.transferTimeout";

    /**
     * 派签xx小时未更新物流
     */
    public static final String LOGISTICS_DELIVERY_SIGN_TIME_OUT = "abnormal.logistics.deliverySignTimeout";

    /**
     * 已标记拦截后xx小时未实际截返提醒时间
     */
    public static final String LOGISTICS_UN_RETURN_TIMEOUT = "abnormal.logistics.unreturnTimeout";

    /**
     * 揽收后签收前物流轨迹超xx小时未更新提醒时间
     */
    public static final String LOGISTICS_UN_UPDATE_TIMEOUT = "abnormal.logistics.unupdateTimeout";

    /**
     * 异常包裹关键字开关(erp)
     */
    public static final String ABNORMAL_LOGISTICS_ABNORMAL_PARCEL_ENABLE = "abnormal.logistics.abnormalParcel.enable";

    /**
     * 异常包裹关键字
     */
    public static final String ABNORMAL_LOGISTICS_ABNORMAL_PARCEL_CUSTOM_KEYWORD = "abnormal.logistics.abnormalParcelCustomKeyword";

    /**
     * 发货后xx小时未揽件提醒弹窗开关
     */
    public static final String LOGISTICS_UN_COLLECT_TIMEOUT_PROMPT = "abnormal.logistics.uncollectTimeout.prompt";

    /**
     *已标记拦截后xx小时未实际截返弹窗开关
     */
    public static final String LOGISTICS_UN_RETURN_TIMEOUT_PROMPT = "abnormal.logistics.unreturnTimeout.prompt";

    /**
     * 揽收后签收前物流轨迹超xx小时未更新弹窗开关
     */
    public static final String LOGISTICS_UN_UPDATE_TIMEOUT_PROMPT = "abnormal.logistics.unupdateTimeout.prompt";

    /**
     * 其他异常弹窗开关
     */
    public static final String LOGISTICS_OTHER_PROMPT = "abnormal.logistics.other.prompt";

    /**
     * 派件异常弹窗开关
     */
    public static final String LOGISTICS_DELIVERY_EXCEPTION_PROMPT = "abnormal.logistics.deliveryException.prompt";

    /**
     * 拒收弹窗开关
     */
    public static final String LOGISTICS_REFUSED_ACCEPT_PROMPT = "abnormal.logistics.refusedAccept.prompt";

    /**
     * 其他异常弹窗开关
     */
    public static final String LOGISTICS_NOT_DELIVERED_ON_TIME_PROMPT = "abnormal.logistics.notDeliveredOnTime.prompt";

    /**
     * 正向订单物流监控开关
     */
    public static final String LOGISTICS_ORDER_MONITORING_ENABLE = "abnormal.logistics.order.monitoring.enable";

    /**
     * 退货物流监控开关
     */
    public static final String LOGISTICS_REFUND_MONITORING_ENABLE = "abnormal.logistics.refund.monitoring.enable";

    /**
     * 爱用（线下）订单监控开关
     */
    public static final String LOGISTICS_AY_CUSTOM_MONITORING_ENABLE = "abnormal.logistics.ay.custom.monitoring.enable";

    /**
     * 交易正向订单物流监控开关
     */
    public static final String LOGISTICS_TRADE_ORDER_MONITORING_ENABLE = "abnormal.logistics.trade.order.monitoring.enable";

    /**
     * 退款订单物流自动标记已处理开关（售中退款成功）
     */
    public static final String LOGISTICS_REFUND_ORDER_AUTO_MARK_PROCESSED_ENABLE = "abnormal.logistics.refund.order.auto.mark.processed.enable";

    /**
     * 导航栏提醒开关（ERP）
     */
    public static final String ABNORMAL_LOGISTICS_NAVIGATION_REMIND_ENABLE = "abnormal.logistics.navigationRemind.enable";


    /**
     * erp物流运营开关（白名单）
     */
    public static final String TRADE_ERP_LOGISTICS_WARNING_WHITE = "logistics.warning.white";

    /**
     * 交易全部异常配置列表（批量调用配置使用）
     */
    public static final List<String> TRADE_SETTING_STR_LIST = Lists.newArrayList(LOGISTICS_UN_COLLECT_TIMEOUT,
        LOGISTICS_UN_RETURN_TIMEOUT, LOGISTICS_UN_UPDATE_TIMEOUT,  LOGISTICS_DELIVERY_SIGN_TIME_OUT,
        LOGISTICS_TRANSFER_TIMEOUT, LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT, LOGISTICS_UN_COLLECT_TIMEOUT_PROMPT,
        LOGISTICS_UN_RETURN_TIMEOUT_PROMPT, LOGISTICS_UN_UPDATE_TIMEOUT_PROMPT, LOGISTICS_OTHER_PROMPT,
            LOGISTICS_DELIVERY_EXCEPTION_PROMPT, LOGISTICS_REFUSED_ACCEPT_PROMPT, LOGISTICS_NOT_DELIVERED_ON_TIME_PROMPT
    );

    /**
     * ERP全部异常配置列表（批量调用配置使用）
     */
    public static final List<String> TRADE_ERP_SETTING_STR_LIST = Lists.newArrayList(
        LOGISTICS_COLLECTION_WILL_TIMEOUT, // 揽收即将超时
        LOGISTICS_UN_COLLECT_TIMEOUT, LOGISTICS_UN_COLLECT_TIMEOUT_ENABLE, // 揽收超时
        LOGISTICS_TRANSFER_TIMEOUT, LOGISTICS_TRANSFER_TIMEOUT_ENABLE, // 中转超时
        LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT, //揽收后xx小时未更新第一次物流
        LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT_ENABLE, //揽收后xx小时未更新第一次物流
        ABNORMAL_LOGISTICS_ABNORMAL_PARCEL_ENABLE,ABNORMAL_LOGISTICS_ABNORMAL_PARCEL_CUSTOM_KEYWORD // 关键字超时
    );


    /**
     * 平台和异常配置列表
     */
    public static final Map<String, List<String>> appNameAndSettingStrListMap = new HashMap<>();
    static {
        appNameAndSettingStrListMap.put(CommonAppConstants.APP_TRADE, TRADE_SETTING_STR_LIST);
        appNameAndSettingStrListMap.put(CommonAppConstants.APP_GUANDIAN, TRADE_SETTING_STR_LIST);
        appNameAndSettingStrListMap.put(CommonAppConstants.APP_TRADE_ERP, TRADE_ERP_SETTING_STR_LIST);
    }


    /**
     * 全部物流监控开关列表（批量调用配置使用）
     */
    public static final List<String> MONITORING_ENABLE_SETTING_STR_LIST = Lists.newArrayList(LOGISTICS_ORDER_MONITORING_ENABLE, LOGISTICS_REFUND_MONITORING_ENABLE, LOGISTICS_AY_CUSTOM_MONITORING_ENABLE, LOGISTICS_TRADE_ORDER_MONITORING_ENABLE, TRADE_ERP_LOGISTICS_WARNING_WHITE);

}
