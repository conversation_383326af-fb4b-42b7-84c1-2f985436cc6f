package cn.loveapp.logistics.common.controller;

import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionCommands;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;

import com.google.common.collect.Lists;

import cn.loveapp.common.utils.LoggerHelper;

/**
 * 探活Controller
 *
 * <AUTHOR>
 * @date 2019-04-16
 */
public abstract class BaseCloudNativeController {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseCloudNativeController.class);
    public static final String PONG = "PONG";

    @Autowired
    protected StringRedisTemplate template;

    protected List<DataSource> dataSources = Lists.newArrayList();

    public BaseCloudNativeController(ObjectProvider<List<DataSource>> provider) {
        List<DataSource> list = provider.getIfAvailable();
        if (list == null) {
            return;
        }
        dataSources.addAll(list);
    }

    @RequestMapping("/liveness")
    public ResponseEntity liveness() {
        return ResponseEntity.ok().build();
    }

    @RequestMapping("/readness")
    public ResponseEntity readness() {
        return ResponseEntity.status(checkReadNess()).build();
    }

    protected HttpStatus checkReadNess() {
        try {
            if (!PONG.equalsIgnoreCase(template.execute(RedisConnectionCommands::ping, true))) {
                return HttpStatus.INTERNAL_SERVER_ERROR;
            }
            for (DataSource dataSource : dataSources) {
                try (Connection connection = dataSource.getConnection()) {
                    try (Statement statement = connection.createStatement()) {
                        statement.executeQuery("SELECT 1").close();
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.logError("readness异常: " + e.getMessage(), e);
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return HttpStatus.OK;
    }
}
