package cn.loveapp.logistics.common.dao.es;

import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.common.entity.es.ElasticsearchEntity;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryVariant;
import co.elastic.clients.json.JsonpMapper;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.rest_client.RestClientOptions;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.data.elasticsearch.ResourceNotFoundException;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.convert.ElasticsearchConverter;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateResponse;
import org.springframework.data.elasticsearch.core.routing.RoutingResolver;
import org.springframework.util.Assert;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * AbstractESDao
 *
 * <AUTHOR>
 * @date 2020/2/18
 */
public abstract class BaseElasticsearchDao<T extends ElasticsearchEntity> {
    public static final String GMT_FORMATER = "yyyy-MM-dd'T'HH:mm:ss.SSS";
    public static final String MINUTE_SECOND_FORMATER = "yyyy-MM-dd'T'HH:mm:ss";

    protected final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(GMT_FORMATER);
    protected final DateTimeFormatter minuteSecondFormatter = DateTimeFormatter.ofPattern(MINUTE_SECOND_FORMATER);


    protected ElasticsearchTemplate operations;

    protected ElasticsearchConverter converter;

    protected ElasticsearchTemplate operationsWithNull;

    public BaseElasticsearchDao(ElasticsearchTemplate elasticsearchTemplate, ElasticsearchConverter converter,
                                RestClient restClient, ObjectProvider<RestClientOptions> restClientOptions) {
        this.operations = elasticsearchTemplate;
        this.converter = converter;
        // 创建一个不忽略 null 值的 template, 用于指定字段更新
        JsonpMapper jsonpMapperWithNull = new JacksonJsonpMapper(new ObjectMapper()
            .configure(SerializationFeature.INDENT_OUTPUT, false)
            .setSerializationInclusion(JsonInclude.Include.ALWAYS)
            .setTimeZone(TimeZone.getTimeZone("GMT+8"))
        );
        RestClientTransport transportWithNull = new RestClientTransport(restClient, jsonpMapperWithNull, restClientOptions.getIfAvailable());
        ElasticsearchClient clientWithNull = new ElasticsearchClient(transportWithNull);
        this.operationsWithNull = new ElasticsearchTemplate(clientWithNull, converter);
    }


    public abstract String getIndexName(@NotNull T entity);

    public abstract String getIndexName();

    public abstract String[] getIndexName(@NotNull List<TargetSellerInfo> sellerInfoList);

    protected String getRouting(@NotNull List<TargetSellerInfo> sellerInfoList) {
        Set<String> routerArr = new HashSet<>();
        String router;
        for (TargetSellerInfo sellerInfo : sellerInfoList) {
            router = getHashRoutingKey(sellerInfo.getTargetSellerId());
            if (StringUtils.isNotEmpty(router)) {
                routerArr.add(router);
            }
        }
        String join = StringUtils.join(routerArr, ",");
        return StringUtils.isEmpty(join) ? null : join;
    }

    protected String getHashRoutingKey(@NotNull String sellerId) {
        return sellerId;
    }


    public long count(T entity, QueryVariant query) {
        NativeQuery nativeQuery = NativeQuery.builder().withQuery(query._toQuery())
            .withRoute(operations.getEntityRouting(entity)).build();
        return operations.count(nativeQuery, entity.getClass());
    }

    public long count(T entity, List<TargetSellerInfo> sellerInfoList, QueryVariant query) {
        if (CollectionUtils.isEmpty(sellerInfoList)) {
            return count(entity, query);
        }
        String[] indexNameArr = getIndexName(sellerInfoList);
        String routing = getRouting(sellerInfoList);
        NativeQuery nativeQuery = NativeQuery.builder().withQuery(query._toQuery()).withRoute(routing).build();
        return operations.count(nativeQuery, IndexCoordinates.of(indexNameArr));
    }

    /**
     * 不指定索引和router
     *
     * @param query
     * @return
     */
    public long count(NativeQuery query) {
        return operations.count(query, IndexCoordinates.of(getIndexName()));
    }

    public void save(@NotNull T entity) {
        // 复制一个, 防止原始信息修改
        entity = (T) entity.clone();
        // 初始化默认值
        entity.initDefault();
        Date now = new Date();
        entity.setGmtCreate(now);
        entity.setGmtModified(now);
        operations.save(entity);
    }

    /**
     * 依据entity的内容修改索引内容, 为null的属性不修改
     *
     * @param entity
     * @return
     */
    public int updateByIdWithNotNull(@NotNull T entity) {
        Assert.notNull(entity, "Cannot update 'null' entity.");
        // 复制一个, 防止原始信息修改
        entity = (T) entity.clone();
        // 只能初始化特殊值, 不能初始化默认值, 否则默认值会更新进去
        entity.initSpecial();
        entity.setGmtCreate(null);
        entity.setGmtModified(new Date());
        Document doc = operations.getElasticsearchConverter().mapObject(entity);
        // 去除doc中所有value为null的key
        doc.entrySet().removeIf(entry -> entry.getValue() == null);
        UpdateQuery updateQuery = UpdateQuery.builder(entity.getId())
            .withDocument(doc)
            .withRouting(operations.getEntityRouting(entity)).build();

        try {
            UpdateResponse response = operations.update(updateQuery, IndexCoordinates.of(getIndexName(entity)));
            return response.getResult() == UpdateResponse.Result.UPDATED ? 1 : 0;
        } catch (ResourceNotFoundException _) {
            return 0;
        }
    }

    /**
     * 依据entity的内容和fields指定的字段修改索引内容, fields指定的字段即使为null也修改
     *
     * @param entity
     * @param fields 要更新的字段
     * @return
     */
    public int updateByIdWithNull(@NotNull T entity, @NotNull List<String> fields) {
        Assert.notNull(entity, "Cannot update 'null' entity.");
        // 复制一个, 防止原始信息修改
        entity = (T) entity.clone();
        // 只能初始化特殊值, 不能初始化默认值, 否则默认值会更新进去
        entity.initSpecial();
        entity.setGmtCreate(null);
        entity.setGmtModified(new Date());
        Document doc = operations.getElasticsearchConverter().mapObject(entity);
        // 保留指定的字段
        Document newDoc = Document.create();
        for (String field : fields) {
            // 不存在的字段为 null, 存在则保留
            newDoc.put(field, doc.get(field));
        }
        doc = newDoc;
        UpdateQuery updateQuery = UpdateQuery.builder(entity.getId())
            .withDocument(doc)
            .withRouting(operations.getEntityRouting(entity)).build();

        try {
            UpdateResponse response = operationsWithNull.update(updateQuery, IndexCoordinates.of(getIndexName(entity)));
            return response.getResult() == UpdateResponse.Result.UPDATED ? 1 : 0;
        } catch (ResourceNotFoundException _) {
            return 0;
        }
    }

    /**
     * 真删除
     *
     * @param entity
     * @return
     */
    public int deleteById(@NotNull T entity) {
        // 默认 delete 操作没有使用 routing
        try {
            String id = operations.withRouting(RoutingResolver.just(entity.getSellerId())).delete(entity);
            return StringUtils.isNotEmpty(id) ? 1 : 0;
        } catch (ResourceNotFoundException _) {
            return 0;
        }
    }

}
