package cn.loveapp.logistics.common.dao.es;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsQueryDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import cn.loveapp.logistics.common.service.abnormalstrategy.LogisticsAbnormalStrategyChain;
import cn.loveapp.logistics.common.utils.ElasticsearchUtil;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.transport.rest_client.RestClientOptions;
import com.alibaba.fastjson2.JSON;
import com.google.common.base.CaseFormat;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.convert.ElasticsearchConverter;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.loveapp.common.es.EsQueryBuilders.*;

/**
 * 物流Es搜索Dao
 *
 * <AUTHOR>
 * @Date 2023/6/20 10:56
 */
@Repository
public class LogisticsOrderInfoSearchEsDao extends BaseElasticsearchDao<LogisticsOrderInfoSearchES> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderInfoSearchEsDao.class);

    protected static final String[] FIELDS = {EsFields.outSid};

    @Autowired
    private LogisticsAbnormalStrategyChain logisticsAbnormalStrategyChain;

    public LogisticsOrderInfoSearchEsDao(ElasticsearchTemplate elasticsearchTemplate, ElasticsearchConverter converter,
                                         RestClient restClient, ObjectProvider<RestClientOptions> restClientOptions) {
        super(elasticsearchTemplate, converter, restClient, restClientOptions);
    }

    @Override
    public String getIndexName(LogisticsOrderInfoSearchES entity) {
        return LogisticsOrderInfoSearchES.INDEX_NAME_PREFIX;
    }

    @Override
    public String getIndexName() {
        return LogisticsOrderInfoSearchES.INDEX_NAME_PREFIX;
    }

    @Override
    public String[] getIndexName(@NotNull List<TargetSellerInfo> sellerInfoList) {
        return new String[]{LogisticsOrderInfoSearchES.INDEX_NAME_PREFIX};
    }

    protected String getRouting(@NotNull List<TargetSellerInfo> sellerInfoList) {
        Set<String> routerArr = new HashSet<>();
        String router;
        TargetSellerInfo sellerInfo;
        for (int i = 0; i < sellerInfoList.size(); i++) {
            sellerInfo = sellerInfoList.get(i);
            router = sellerInfo.getTargetSellerId();
            if (StringUtils.isNotEmpty(router)) {
                routerArr.add(router);
            }
        }
        String join = StringUtils.join(routerArr, ",");
        return StringUtils.isEmpty(join) ? null : join;
    }

    /**
     * 更新ES记录没有就新增（为null的属性不修改）
     *
     * @param logisticsOrderInfoSearchES
     * @return
     */
    public int insertOrUpdate(LogisticsOrderInfoSearchES logisticsOrderInfoSearchES) {
        int result = updateByIdWithNotNull(logisticsOrderInfoSearchES);
        if (result <= 0) {
            LOGGER.logInfo(logisticsOrderInfoSearchES.getSellerId(), logisticsOrderInfoSearchES.getOutSid(),
                "ES 更新失败, 尝试插入: " + JSON.toJSONString(logisticsOrderInfoSearchES));
            save(logisticsOrderInfoSearchES);
            result = 1;
        }
        return result;
    }

    /**
     * 根据条件计数
     *
     * @param searchES
     * @param queryCondition
     * @return
     */
    public long countByQuery(LogisticsOrderInfoSearchES searchES, BoolQuery.Builder queryCondition) {

        queryCondition.mustNot(termQuery(EsFields.isDeleted, true));
        queryCondition.must(
            termQuery(EsFields.sellerId, searchES.getSellerId()),
            termQuery(EsFields.storeId, searchES.getStoreId())
        );
        appendAppNameQuery(queryCondition, searchES.getAppName(), searchES.getStoreId());

        return count(searchES, queryCondition.build());
    }

    /**
     * 根据条件分页查询物流es索引
     *
     * @param logisticsQuery
     * @param userInfoDTO
     * @return
     */
    public SearchResultDTO<LogisticsOrderInfoSearchES> logisticsListGetQueryByLimit(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO) {
        NativeQueryBuilder builder = generateCommonQueryIndexAndRoute(userInfoDTO);

        if (StringUtils.isEmpty(logisticsQuery.getSortField())) {
            builder.withSort(fieldSort(EsFields.created).order(SortOrder.Desc).build());
        } else {
            String sortField = logisticsQuery.getSortField().contains("_")
                ? CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, logisticsQuery.getSortField())
                : logisticsQuery.getSortField();

            if (LogisticsQueryDTO.SORT_FIELD_CONSIGN_TIME.equals(sortField)) {
                sortField = EsFields.consignTime;
            }
            String sortDirection = logisticsQuery.getSortDirection();
            SortOptions sortBuilder;
            if (StringUtils.isEmpty(sortDirection)) {
                sortBuilder = fieldSort(sortField).order(SortOrder.Desc).build();
            } else {
                sortBuilder = fieldSort(sortField).order(sortOrder(sortDirection)).build();
            }
            builder.withSort(sortBuilder);
        }

        BoolQuery.Builder queryCondition = createBoolQueryCondition(logisticsQuery, userInfoDTO);
        builder.withQuery(queryCondition.build()._toQuery());

        return pageQuery(logisticsQuery, builder);
    }

    /**
     * 生成ES查询条件
     *
     * @param logisticsQuery
     * @param userInfoDTO
     * @return
     */
    private BoolQuery.Builder createBoolQueryCondition(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO) {
        BoolQuery.Builder queryCondition = boolQuery();
        queryCondition.mustNot(termQuery(EsFields.isDeleted, true));

        if (BooleanUtils.isTrue(logisticsQuery.getIsExcludeProcessed())) {
            // 已处理不显示
            queryCondition.mustNot(termQuery(EsFields.logisticsAbnormalInfoProcessStatus, AbnormalProcessStatus.PROCESSED.value()));
        }

        if (CollectionUtils.isNotEmpty(userInfoDTO.getTargetSellerList())) {
            BoolQuery.Builder should = boolQuery();
            for (TargetSellerInfo sellerInfo :  userInfoDTO.getTargetSellerList()) {
                String targetStoreId = sellerInfo.getTargetStoreId();
                BoolQuery.Builder must = boolQuery().must(termQuery(EsFields.storeId, targetStoreId));
                if (StringUtils.isNotEmpty(sellerInfo.getTargetSellerId())) {
                    must.must(termQuery(EsFields.sellerId, sellerInfo.getTargetSellerId()));
                }
                appendAppNameQuery(must, sellerInfo.getTargetAppName(), sellerInfo.getTargetStoreId());
                should.should(must.build());
            }
            queryCondition.must(should.build());
        } else {
            if (StringUtils.isNotEmpty(userInfoDTO.getSellerId())) {
                queryCondition.must(termQuery(EsFields.sellerId, userInfoDTO.getSellerId()));
            }
            queryCondition.must(termQuery(EsFields.storeId, userInfoDTO.getStoreId()));
            appendAppNameQuery(queryCondition, userInfoDTO.getAppName(), userInfoDTO.getStoreId());
        }

        if (logisticsQuery.getStartTime() != null) {
            queryCondition.must(rangeQuery(EsFields.created).gte(minuteSecondFormatter.format(logisticsQuery.getStartTime())).build());
        }
        if (logisticsQuery.getEndTime() != null) {
            queryCondition.must(rangeQuery(EsFields.created).lte(minuteSecondFormatter.format(logisticsQuery.getEndTime())).build());
        }

        if (logisticsQuery.getStartConsignTime() != null) {
            queryCondition.must(rangeQuery(EsFields.consignTime).gte(minuteSecondFormatter.format(logisticsQuery.getStartConsignTime())).build());
        }

        if (logisticsQuery.getEndConsignTime() != null) {
            queryCondition.must(rangeQuery(EsFields.consignTime).lte(minuteSecondFormatter.format(logisticsQuery.getEndConsignTime())).build());
        }

        String lastAction = logisticsQuery.getLastAction();
        if (StringUtils.isNotEmpty(lastAction)) {
            if (AyLogisticsStatus.AWAITING_PICKUP.name().equals(lastAction) || AyLogisticsStatus.AWAITING_PICKUP.value().equals(lastAction)) {
                BoolQuery.Builder boolQuery = boolQuery();
                // 最新action为待揽件或者为空都视为待揽件
                boolQuery.should(termQuery(EsFields.lastAction, AyLogisticsStatus.AWAITING_PICKUP.value()))
                    .should(boolQuery().mustNot(existsQuery(EsFields.lastAction)).build())
                    .minimumShouldMatch("1");
                queryCondition.must(boolQuery.build());
            } else {
                List<String> statusList = AyLogisticsStatus.getStatusGroupList(lastAction);
                if (Objects.equals(AyLogisticsStatus.OTHER.name(), lastAction)
                    && CollectionUtils.isNotEmpty(statusList)) {
                    queryCondition.mustNot(termsQueryStr(EsFields.lastAction, statusList));
                    queryCondition.must(existsQuery(EsFields.lastAction));
                } else {
                    if (CollectionUtils.isNotEmpty(statusList)) {
                        queryCondition.must(termsQueryStr(EsFields.lastAction, statusList));
                    } else {
                        // 兼容线上直接传状态值
                        queryCondition.must(termQuery(EsFields.lastAction, lastAction));
                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(logisticsQuery.getTid())) {
            queryCondition.must(termQuery(EsFields.businessInfoTIdList, logisticsQuery.getTid()));
        }

        // 查询异常相关
        if (BooleanUtils.isTrue(logisticsQuery.getOnlySearchNormal())) {
            queryCondition.must(generateNormalBoolQueryOfTradeApp());
        } else if (CollectionUtils.isNotEmpty(logisticsQuery.getAbnormalTypes()) || !Objects.isNull(logisticsQuery.getOnlySearchAbnormal())) {
            AbnormalQueryDTO abnormalQueryDTO = new AbnormalQueryDTO();
            abnormalQueryDTO.setOnlySearchAbnormal(logisticsQuery.getOnlySearchAbnormal());
            abnormalQueryDTO.setIsExcludeProcessed(logisticsQuery.getIsExcludeProcessed());
            abnormalQueryDTO.setIsExcludeOtherAbnormalOfTradeApp(logisticsQuery.getIsExcludeOtherAbnormalOfTradeApp());
            abnormalQueryDTO.setIsExistConsignTime(logisticsQuery.getIsExistConsignTime());
            BoolQuery.Builder builder = logisticsAbnormalStrategyChain.generalAbnormalBoolQueryChain(logisticsQuery.getAbnormalTypes(), abnormalQueryDTO);
            if (!Objects.isNull(builder)) {
                queryCondition.must(builder.minimumShouldMatch("1").build());
            }
        }

        if (StringUtils.isNotEmpty(logisticsQuery.getOutSid())) {
            queryCondition.must(termQuery(EsFields.outSid, logisticsQuery.getOutSid()));
        } else if (CollectionUtils.isNotEmpty(logisticsQuery.getOutSidList())) {
            queryCondition.must(termsQueryStr(EsFields.outSid, logisticsQuery.getOutSidList()));
        }
        // 包裹类型
        if (!Objects.isNull(logisticsQuery.getPackType())) {
            switch (logisticsQuery.getPackType()) {
                case NORMAL_DELIVERY:
                    // 正常发货件
                    queryCondition.mustNot(termQuery(EsFields.isTagIntercepted, true));
                    queryCondition.mustNot(existsQuery(EsFields.businessInfoRefundIdList));
                    break;
                case MERCHANT_INTERCEPT:
                    // 商家拦截件
                    queryCondition.must(termQuery(EsFields.isTagIntercepted, true));
                    break;
                case BUYER_RETURN:
                    queryCondition.must(existsQuery(EsFields.businessInfoRefundIdList));
                    break;
            }
        }

        // 物流公司
        if (StringUtils.isNotEmpty(logisticsQuery.getLogisticsCompany())) {
            queryCondition.must(boolQuery().should(termQuery(EsFields.companyCode, logisticsQuery.getLogisticsCompany()))
                .should(matchPhraseQuery(EsFields.companyName, logisticsQuery.getLogisticsCompany())).build());
        } else if (CollectionUtils.isNotEmpty(logisticsQuery.getLogisticsCompanyList())) {
            BoolQuery.Builder builder = boolQuery();
            for (String company : logisticsQuery.getLogisticsCompanyList()) {
                builder.should(boolQuery().should(termQuery(EsFields.companyCode, company))
                    .should(matchPhraseQuery(EsFields.companyName, company)).minimumShouldMatch("1").build());
            }
            queryCondition.must(builder.minimumShouldMatch("1").build());
        }

        if (CollectionUtils.isNotEmpty(logisticsQuery.getProcessStatus())) {
            if (logisticsQuery.getProcessStatus().contains(AbnormalProcessStatus.PENDING.value())) {
                queryCondition.must(boolQuery()
                    .should(
                        termsQueryStr(EsFields.logisticsAbnormalInfoProcessStatus, logisticsQuery.getProcessStatus()))
                    .should(boolQuery().mustNot(existsQuery(EsFields.logisticsAbnormalInfoProcessStatus)).build())
                    .minimumShouldMatch("1").build());
            } else {
                queryCondition.must(
                    termsQueryStr(EsFields.logisticsAbnormalInfoProcessStatus, logisticsQuery.getProcessStatus()));
            }
        }

        // 买家openUid
        if (StringUtils.isNotEmpty(logisticsQuery.getBuyerOpenUid())) {
            queryCondition.must(termQuery(EsFields.buyerOpenUid, logisticsQuery.getBuyerOpenUid()));
        }

        if (logisticsQuery.getOrderSellerMemo() != null) {
            queryCondition.must(termQuery(EsFields.businessInfoSellerMemo, logisticsQuery.getOrderSellerMemo()));
        }

        if (!Objects.isNull(logisticsQuery.getBuyerSellerMemoSearchType())) {
            switch (logisticsQuery.getBuyerSellerMemoSearchType()) {
                case none:
                    // 无留言且无备注
                    queryCondition.must(boolQuery()
                        .should(boolQuery().mustNot(existsQuery(EsFields.businessInfoSellerMemo)).build())
                        .should(termQuery(EsFields.businessInfoSellerMemoKeyword, StringUtils.EMPTY)).build());
                    queryCondition.must(boolQuery()
                        .should(boolQuery().mustNot(existsQuery(EsFields.businessInfoBuyerMessage)).build())
                        .should(termQuery(EsFields.businessInfoBuyerMessageKeyword, StringUtils.EMPTY)).build());
                    queryCondition.mustNot(rangeQuery(EsFields.businessInfoOrderAyCustomFlag).gt(0).build());
                    queryCondition.mustNot(rangeQuery(EsFields.businessInfoSellerFlagList).gt(0).build());
                    break;
                case any:
                    // 有留言或者有备注
                    queryCondition.must(boolQuery()
                        .should(boolQuery().must(existsQuery(EsFields.businessInfoSellerMemo)).mustNot(termQuery(EsFields.businessInfoSellerMemoKeyword, StringUtils.EMPTY)).build())
                        .should(boolQuery().must(existsQuery(EsFields.businessInfoBuyerMessage)).mustNot(termQuery(EsFields.businessInfoBuyerMessageKeyword, StringUtils.EMPTY)).build())
                        .should(boolQuery().must(rangeQuery(EsFields.businessInfoOrderAyCustomFlag).gt(0).build()).build())
                        .should(boolQuery().must(rangeQuery(EsFields.businessInfoSellerFlagList).gt(0).build()).build())
                        .minimumShouldMatch("1").build());
                    break;
                case seller:
                    // 有备注
                    queryCondition.must(boolQuery()
                        .should(boolQuery().must(existsQuery(EsFields.businessInfoSellerMemo)).mustNot(termQuery(EsFields.businessInfoSellerMemoKeyword, StringUtils.EMPTY)).build())
                        .should(boolQuery().must(rangeQuery(EsFields.businessInfoOrderAyCustomFlag).gt(0).build()).build())
                        .should(boolQuery().must(rangeQuery(EsFields.businessInfoSellerFlagList).gt(0).build()).build())
                        .minimumShouldMatch("1").build());
                    break;
                case buyer:
                    // 有留言
                    queryCondition.must(boolQuery().must(existsQuery(EsFields.businessInfoBuyerMessage))
                        .mustNot(termQuery(EsFields.businessInfoBuyerMessageKeyword, StringUtils.EMPTY)).build());
                    break;
            }
        }

        if (logisticsQuery.getIsRefund() != null) {
            if (BooleanUtils.isTrue(logisticsQuery.getIsRefund())) {
                queryCondition.must(termQuery(EsFields.businessInfoIsRefund, true));
            } else {
                queryCondition.must(boolQuery().should(termQuery(EsFields.businessInfoIsRefund, false))
                    .should(boolQuery().mustNot(existsQuery(EsFields.businessInfoIsRefund)).build()).build());
            }
        }

        if (StringUtils.isNotEmpty(logisticsQuery.getSkuName())) {
            queryCondition.must(matchPhraseQuery(EsFields.businessInfoSkuNameList,
                ElasticsearchUtil.splitAlphanumeric(logisticsQuery.getSkuName())));
        }

        if (CollectionUtils.isNotEmpty(logisticsQuery.getTidOrBuyerOpenUid())) {
            if (logisticsQuery.getTidOrBuyerOpenUid().size() == 1) {
                String tidOrBuyerOpenUid = logisticsQuery.getTidOrBuyerOpenUid().getFirst();
                queryCondition.must(
                    boolQuery().should(termsQueryStr(EsFields.businessInfoTIdList, logisticsQuery.getTidOrBuyerOpenUid()))
                        .should(termsQueryStr(EsFields.buyerOpenUid, logisticsQuery.getTidOrBuyerOpenUid()))
                        .should(matchPhraseQuery(EsFields.buyerNick, ElasticsearchUtil.splitAlphanumeric(tidOrBuyerOpenUid))).build());
            } else {
                queryCondition.must(
                    boolQuery().should(termsQueryStr(EsFields.businessInfoTIdList, logisticsQuery.getTidOrBuyerOpenUid()))
                        .should(termsQueryStr(EsFields.buyerOpenUid, logisticsQuery.getTidOrBuyerOpenUid()))
                        .should(termsQueryStr(EsFields.buyerNickKeyword, logisticsQuery.getTidOrBuyerOpenUid())).build());
            }
        }

        handleSearchType(logisticsQuery, queryCondition);
        return queryCondition;
    }

    /**
     * 处理指定搜索类型的条件
     *
     * @param logisticsQuery
     * @param queryCondition
     */
    private static void handleSearchType(LogisticsQueryDTO logisticsQuery, BoolQuery.Builder queryCondition) {
        if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderSellerFlagList())
            || CollectionUtils.isNotEmpty(logisticsQuery.getOrderAyCustomFlagList())) {
            BoolQuery.Builder fuzzQueryBuilder = boolQuery();
            switch (logisticsQuery.getOrderFlagSearchType()) {
                case ACCURATE_SEARCH:
                    // 精确
                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderSellerFlagList())) {
                        queryCondition
                            .must(termsQueryInt(EsFields.businessInfoSellerFlagList, logisticsQuery.getOrderSellerFlagList()));
                    }

                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderAyCustomFlagList())) {
                        queryCondition.must(
                            termsQueryInt(EsFields.businessInfoOrderAyCustomFlag, logisticsQuery.getOrderAyCustomFlagList()));
                    }
                    break;
                case FUZZY_SEARCH:
                    // 模糊
                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderSellerFlagList())) {
                        fuzzQueryBuilder.should(
                            termsQueryInt(EsFields.businessInfoSellerFlagList, logisticsQuery.getOrderSellerFlagList()));
                    }

                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderAyCustomFlagList())) {
                        fuzzQueryBuilder.should(
                            termsQueryInt(EsFields.businessInfoOrderAyCustomFlag, logisticsQuery.getOrderAyCustomFlagList()));
                    }
                    queryCondition.must(fuzzQueryBuilder.build());

                    break;
                case REVERSE_FUZZY_SEARCH:
                    // 反向模糊
                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderSellerFlagList())) {
                        fuzzQueryBuilder.should(
                            termsQueryInt(EsFields.businessInfoSellerFlagList, logisticsQuery.getOrderSellerFlagList()));
                    }

                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderAyCustomFlagList())) {
                        fuzzQueryBuilder.should(
                            termsQueryInt(EsFields.businessInfoOrderAyCustomFlag, logisticsQuery.getOrderAyCustomFlagList()));
                    }
                    queryCondition.mustNot(fuzzQueryBuilder.build());
                    break;
                default:
            }
        }

        if (StringUtils.isNotEmpty(logisticsQuery.getOuterSkuId())) {
            switch (logisticsQuery.getSkuInfoSearchType()) {
                case ACCURATE_SEARCH:
                    // 精确
                    if (StringUtils.isNotEmpty(logisticsQuery.getOuterSkuId())) {
                        queryCondition.must(termQuery(EsFields.businessInfoOuterSkuIdListKeyword,
                            ElasticsearchUtil.splitAlphanumeric(logisticsQuery.getOuterSkuId())));
                    }

                    break;
                case FUZZY_SEARCH:
                    // 模糊
                    if (StringUtils.isNotEmpty(logisticsQuery.getOuterSkuId())) {
                        queryCondition.must(matchPhraseQuery(EsFields.businessInfoOuterSkuIdList,
                            ElasticsearchUtil.splitAlphanumeric(logisticsQuery.getOuterSkuId())));
                    }

                    break;
                case REVERSE_FUZZY_SEARCH:
                    // 反向模糊
                    if (StringUtils.isNotEmpty(logisticsQuery.getOuterSkuId())) {
                        queryCondition.mustNot(matchPhraseQuery(EsFields.businessInfoOuterSkuIdList,
                            ElasticsearchUtil.splitAlphanumeric(logisticsQuery.getOuterSkuId())));
                    }

                    break;
                default:
            }
        }
    }

    private BoolQuery generateNormalBoolQueryOfTradeApp() {
        BoolQuery.Builder normalBoolQueryBuilder = boolQuery();

        // 仅排除存在的交易的异常
        normalBoolQueryBuilder.mustNot(termQuery(EsFields.notPickedUpAfterSendIsExists, true))
            .mustNot(termQuery(EsFields.firstTraceAfterPickedUpTimeoutIsExists, true))
            .mustNot(termQuery(EsFields.transferTimeoutIsExists, true))
            .mustNot(termQuery(EsFields.deliverySignTimeOutIsExists, true))
            .mustNot(termQuery(EsFields.otherAbnormalOfTradeAppIsExists, true));
        return normalBoolQueryBuilder.build();
    }


    /**
     * 根据条件获取数量
     * @param logisticsQuery
     * @param userInfoDTO
     * @return
     */
    public Integer queryCountFromDB(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO) {
        BoolQuery.Builder queryCondition = createBoolQueryCondition(logisticsQuery, userInfoDTO);
        return (int) count(
            LogisticsOrderInfoSearchES.of(userInfoDTO.getSellerId(), userInfoDTO.getStoreId(), userInfoDTO.getAppName()), userInfoDTO.getTargetSellerList(),
            queryCondition.build());
    }

    private SearchResultDTO<LogisticsOrderInfoSearchES> pageQuery(LogisticsQueryDTO logisticsQuery, NativeQueryBuilder builder) {
        SearchResultDTO<LogisticsOrderInfoSearchES> searchAfterResultDTO = new SearchResultDTO<>();

        // 添加上次的查询游标
        List<Object> lastSearchSortValues = logisticsQuery.getLastSearchSortValues();
        if (lastSearchSortValues != null) {
            if (CollectionUtils.isEmpty(builder.getSortOptions())) {
                builder.withSort(fieldSort(EsFields.gmtCreate).build());
            }
            //添加唯一标识，解决search after重复的问题
            builder.withSort(fieldSort(EsFields.id).order(SortOrder.Desc).build());
            // 里面有值则插入上一次的游标值,第一次访问时，lastSearchSortValues = [null]
            if (!lastSearchSortValues.isEmpty() && lastSearchSortValues.getFirst() != null) {
                builder.withSearchAfter(lastSearchSortValues);
            }
            builder.withPageable(PageRequest.ofSize(logisticsQuery.getLimit()));
        }else{
            builder.withPageable(PageRequest.of(logisticsQuery.getStart(), logisticsQuery.getLimit()));
        }

        SearchHits<LogisticsOrderInfoSearchES> result = operations.search(builder.build(), LogisticsOrderInfoSearchES.class);

        List<SearchHit<LogisticsOrderInfoSearchES>> searchHits = result.getSearchHits();
        searchAfterResultDTO.setSearchResults(searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList()));
        searchAfterResultDTO.setTotalResults((int) result.getTotalHits());
        // 返回游标值
        if (lastSearchSortValues != null && !searchHits.isEmpty() && CollectionUtils.isNotEmpty(searchHits.getLast().getSortValues())) {
            SearchHit<LogisticsOrderInfoSearchES> lastSearchHit = searchHits.getLast();
            if (lastSearchHit != null && CollectionUtils.isNotEmpty(lastSearchHit.getSortValues())) {
                searchAfterResultDTO.setSearchAfterSortValues(lastSearchHit.getSortValues());
            }
        }
        return searchAfterResultDTO;
    }


    private NativeQueryBuilder generateCommonQueryIndexAndRoute(UserInfoDTO userInfoDTO) {
        NativeQueryBuilder builder = new NativeQueryBuilder()
            .withSearchType(Query.SearchType.QUERY_THEN_FETCH);
        if (FIELDS != null) {
            builder.withFields(FIELDS);
        }
        builder.withRoute(userInfoDTO.getSellerId());
        if (CollectionUtils.isNotEmpty(userInfoDTO.getTargetSellerList())) {
            List<TargetSellerInfo> targetSellerInfoList = userInfoDTO.getTargetSellerList();
            if (CollectionUtils.isNotEmpty(targetSellerInfoList)) {
                String routingArr = getRouting(targetSellerInfoList);
                builder.withRoute(routingArr);
            }
        }
        return builder;
    }

    protected void appendAppNameQuery(BoolQuery.Builder boolQueryBuilder, String appName, String storeId) {
        if (StringUtils.isNotEmpty(appName)) {
            boolQueryBuilder.must(termQuery(EsFields.appName, appName));
        } else {
            boolQueryBuilder.mustNot(existsQuery(EsFields.appName));
        }
    }

}
