package cn.loveapp.logistics.common.dao.es;

import lombok.Data;

import java.util.List;

/**
 * es - searchAfter查询结果包装类
 *
 * @program:logistics-services-group
 * @author: Zzz
 * @Time: 2024/4/25  15:27
 */
@Data
public class SearchResultDTO<T> {

    /**
     * searchAfter 排序值
     */
    private List<Object> searchAfterSortValues;

    /**
     * 搜索结果
     */
    private List<T> searchResults;

    /**
     * 符合条件的总数
     */
    private Integer totalResults;
}
