package cn.loveapp.logistics.common.dao.mongo;

import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;

import java.util.List;


/**
 * 物流单信息表DAO
 *
 * <AUTHOR>
 * @Date 2023/6/8 11:34
 */
public interface LogisticsOrderInfoDao {

    /**
     * 保存物流单信息
     *
     * @param logisticsOrderInfo
     */
    void insert(LogisticsOrderInfo logisticsOrderInfo);


    /**
     * 更新物流单信息
     *
     * @param logisticsOrderInfo
     */
    int update(LogisticsOrderInfo logisticsOrderInfo);

    /**
     * 根据运单号和用户查询物流单信息
     *
     * @param outSid
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    LogisticsOrderInfo queryByOutSidAndSeller(String outSid, String sellerId, String storeId, String appName);

    /**
     * 根据运单号和用户查询物流单信息
     *
     * @param outSidList
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    List<LogisticsOrderInfo> queryByOutSidListAndSeller(List<String> outSidList, String sellerId, String storeId,
        String appName);

    /**
     * 根据运单号和用户查询物流单信息
     *
     * @param outSid
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    LogisticsOrderInfo queryByOutSidAndSaveLogisticsId(String outSid, String saveLogisticsId, List<String> fields, String sellerId, String storeId, String appName);


    /**
     * 根据运单号和物流公司询物流单信息
     *
     * @param outSid
     * @param companyCode
     * @param companyName
     * @return
     */
    List<LogisticsOrderInfo> queryByOutSidAndCompany(String outSid, String companyCode, String companyName);

    /**
     * 根据运单号和物流公司询物流单信息
     *
     * @param outSid
     * @param companyCode
     * @param companyName
     * @return
     */
    List<LogisticsOrderInfo> queryByOutSidAndCompany(String outSid, String companyCode, String companyName,
        String storeId, String appName);


    /**
     * 根据运单号和用户查询物流单信息
     * @param outSid
     * @param userInfoDTO
     * @return
     */
    List<LogisticsOrderInfo> queryByOutSidAndSeller(List<String> outSid, UserInfoDTO userInfoDTO);

}
