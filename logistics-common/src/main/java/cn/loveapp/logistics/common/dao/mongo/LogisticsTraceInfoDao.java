package cn.loveapp.logistics.common.dao.mongo;

import java.io.IOException;
import java.util.List;

import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;

/**
 * LogisticsTraceInfoDao
 *
 * <AUTHOR>
 * @date 2022/1/24
 */
public interface LogisticsTraceInfoDao {

    /**
     * 保存物流流转信息
     *
     * @param logisticsTraceInfoList
     */
    void save(List<LogisticsTraceInfo> logisticsTraceInfoList) throws IOException;

    /**
     * 依据tid 查询物流信息
     *
     * @param tidList
     * @param sellerId
     * @param platformId
     * @param appName
     * @return
     */
    List<LogisticsTraceInfo> queryByTid(List<String> tidList, String sellerId, String platformId, String appName, String logisticsStoreId);

    /**
     * 依据tid与logisticsStoreIds 查询物流信息
     *
     * @param tidList
     * @param sellerId
     * @param platformId
     * @param appName
     * @param logisticsStoreIds
     * @return
     */
    List<LogisticsTraceInfo> queryByTidAndStoreIds(List<String> tidList, String sellerId, String platformId,
        String appName, List<String> logisticsStoreIds);

    /**
     * 依据tid和物流单号 查询物流信息
     *
     * @param tidList
     *            订单号集合
     * @param invoiceNoList
     *            物流单号集合
     * @param sellerId
     * @param platformId
     * @param appName
     * @return
     */
    List<LogisticsTraceInfo> queryByTidAndInvoiceNo(List<String> tidList, List<String> invoiceNoList, String sellerId,
        String platformId, String appName);

    /**
     * 依据tid和outSid查询最新的一条物流信息
     *
     * @param traceInfo
     *
     * @return
     */
    LogisticsTraceInfo queryLastByTidAndOutSid(LogisticsTraceInfo traceInfo);

    /**
     * 按tid删除
     *
     * @param tid
     * @param sellerId
     * @param platformId
     * @param appName
     * @return
     */
    long deleteByTid(List<String> tid, String sellerId, String platformId, String appName);
}
