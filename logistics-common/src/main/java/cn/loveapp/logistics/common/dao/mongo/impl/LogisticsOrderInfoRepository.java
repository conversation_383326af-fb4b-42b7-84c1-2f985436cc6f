package cn.loveapp.logistics.common.dao.mongo.impl;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.constant.MongoConstant;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dao.mongo.BaseMongoDao;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 物流单(logistics_order_info)实现
 *
 * <AUTHOR>
 * @Date 2023/6/8 11:38
 */
@Repository
public class LogisticsOrderInfoRepository extends BaseMongoDao implements LogisticsOrderInfoDao {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderInfoRepository.class);

    @Autowired
    protected MongoTemplate mongoTemplate;

    public LogisticsOrderInfoRepository(MongoTemplate mongoTemplate) {
        super(mongoTemplate);
    }


    @Override
    protected String getShardPrimaryKey() {
        return MongoConstant.OUT_SID_FIELD;
    }


    @Override
    protected boolean isShardCollection() {
        return true;
    }

    @Override
    protected String getCollectionName() {
        return mongoTemplate.getCollectionName(LogisticsOrderInfo.class);
    }


    /**
     * mongo通用基础拼接商家信息 支持多店铺查询.多店铺目标集合不为空则使用多店铺方式查询
     * @param sellerId
     * @param storeId
     * @param appName
     * @param targetSellerInfoList
     * @return
     */
    public Criteria generateSellerCommonSearch(String sellerId, String storeId, String appName, List<TargetSellerInfo> targetSellerInfoList){
        Criteria criteria = new Criteria();
        if (CollectionUtils.isNotEmpty(targetSellerInfoList)) {
            Criteria [] orArr = new Criteria[targetSellerInfoList.size()];
            TargetSellerInfo sellerInfo;
            for (int i = 0; i < targetSellerInfoList.size(); i++) {
                sellerInfo = targetSellerInfoList.get(i);
                Criteria tempCriteria = where(MongoConstant.STORE_ID_FIELD).is(sellerInfo.getTargetStoreId());
                appendAppNameQuery(tempCriteria, sellerInfo.getTargetAppName(), sellerInfo.getTargetStoreId());
                if (StringUtils.isNotEmpty(sellerId)) {
                    tempCriteria.and(MongoConstant.SELLER_ID_FIELD).is(sellerInfo.getTargetSellerId());
                }
                orArr[i] = tempCriteria;
            }
            criteria.orOperator(orArr);
        }else{
            criteria.and(MongoConstant.STORE_ID_FIELD).is(storeId);
            appendAppNameQuery(criteria, appName, storeId);
            if (StringUtils.isNotEmpty(sellerId)) {
                criteria.and(MongoConstant.SELLER_ID_FIELD).is(sellerId);
            }
        }
        return criteria;
    }

    @Override
    public void insert(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null) {
            return;
        }
        Date now = DateUtil.convertLocalDateTimetoDate(DateUtil.currentDate());
        logisticsOrderInfo.setGmtCreate(now);
        logisticsOrderInfo.setGmtModified(now);
        logisticsOrderInfo.setId(logisticsOrderInfo.getId());
        super.insert(logisticsOrderInfo);
    }

    @Override
    public int update(LogisticsOrderInfo logisticsOrderInfo) {
        logisticsOrderInfo.setGmtCreate(null);
        Update update = fromNeedDocumentUpdateValues(logisticsOrderInfo, true);
        Query query = buildQueryById(logisticsOrderInfo.getId(), logisticsOrderInfo.getOutSid());
        return updateOperation(query, update);
    }

    @Override
    public LogisticsOrderInfo queryByOutSidAndSeller(String outSid, String sellerId, String storeId, String appName) {
        Criteria criteria = where(MongoConstant.OUT_SID_FIELD).is(outSid)
            .and(MongoConstant.SELLER_ID_FIELD).is(sellerId)
            .and(MongoConstant.STORE_ID_FIELD).is(storeId);

        appendAppNameQuery(criteria, appName, storeId);

        Query conditions = query(criteria);
        return findOne(conditions, LogisticsOrderInfo.class);
    }

    @Override
    public List<LogisticsOrderInfo> queryByOutSidListAndSeller(List<String> outSidList, String sellerId, String storeId,
        String appName) {
        Criteria criteria = where(MongoConstant.OUT_SID_FIELD).in(outSidList).and(MongoConstant.SELLER_ID_FIELD)
            .is(sellerId).and(MongoConstant.STORE_ID_FIELD).is(storeId);

        appendAppNameQuery(criteria, appName, storeId);

        Query conditions = query(criteria);
        return find(conditions, LogisticsOrderInfo.class);
    }

    @Override
    public LogisticsOrderInfo queryByOutSidAndSaveLogisticsId(String outSid, String saveLogisticsId, List<String> fields, String sellerId, String storeId, String appName) {
        Criteria criteria = where(MongoConstant.OUT_SID_FIELD).is(outSid)
            .and(MongoConstant.SELLER_ID_FIELD).is(sellerId)
            .and(MongoConstant.STORE_ID_FIELD).is(storeId);
        if (CommonLogisticsConstants.PLATFORM_CAINIAO.equals(saveLogisticsId)) {
            // 菜鸟上线兼容快递鸟，防止重复扣除额度
            criteria.and(MongoConstant.SAVE_LOGISTICS_STORE_ID)
                .in(Lists.newArrayList(saveLogisticsId, CommonLogisticsConstants.PLATFORM_KDNIAO));
        } else {
            criteria.and(MongoConstant.SAVE_LOGISTICS_STORE_ID).is(saveLogisticsId);
        }

        appendAppNameQuery(criteria, appName, storeId);
        Query conditions = queryWithFields(fields, criteria);
        return findOne(conditions, LogisticsOrderInfo.class);
    }

    @Override
    public List<LogisticsOrderInfo> queryByOutSidAndCompany(String outSid, String companyCode, String companyName) {
        if (StringUtils.isAllEmpty(companyCode, companyName)) {
            LOGGER.logError("-", outSid, "查询异常，物流公司信息为空");
            return null;
        }
        Criteria criteria = where(MongoConstant.OUT_SID_FIELD).is(outSid);
        if (StringUtils.isNotEmpty(companyCode)) {
            criteria.and(MongoConstant.COMPANY_CODE_FIELD).is(companyCode);
        }

        if (StringUtils.isNotEmpty(companyName)) {
            criteria.and(MongoConstant.COMPANY_NAME_FIELD).is(companyName);
        }
        Query conditions = query(criteria);

        return find(conditions, LogisticsOrderInfo.class);
    }

    @Override
    public List<LogisticsOrderInfo> queryByOutSidAndCompany(String outSid, String companyCode, String companyName,
        String storeId, String appName) {
        if (StringUtils.isAllEmpty(companyCode, companyName)) {
            LOGGER.logError("-", outSid, "查询异常，物流公司信息为空");
            return null;
        }

        Criteria criteria = where(MongoConstant.OUT_SID_FIELD).is(outSid).and(MongoConstant.STORE_ID_FIELD).is(storeId);

        if (StringUtils.isNotEmpty(appName)) {
            criteria.and(MongoConstant.APP_NAME_FIELD).is(appName);
        }

        if (StringUtils.isNotEmpty(companyCode)) {
            criteria.and(MongoConstant.COMPANY_CODE_FIELD).is(companyCode);
        }

        if (StringUtils.isNotEmpty(companyName)) {
            criteria.and(MongoConstant.COMPANY_NAME_FIELD).is(companyName);
        }
        Query conditions = query(criteria);

        return find(conditions, LogisticsOrderInfo.class);
    }

    @Override
    public List<LogisticsOrderInfo> queryByOutSidAndSeller(List<String> outSid, UserInfoDTO userInfoDTO) {
        if (CollectionUtils.isEmpty(outSid)) {
            LOGGER.logError("查询异常，运单号为空");
            return null;
        }
        Criteria criteria = generateSellerCommonSearch(userInfoDTO.getSellerId(), userInfoDTO.getStoreId(), userInfoDTO.getAppName(), userInfoDTO.getTargetSellerList());
        criteria.and(MongoConstant.OUT_SID_FIELD).in(outSid);
        Query conditions = query(criteria);
        return find(conditions, LogisticsOrderInfo.class);
    }

    /**
     * 设置应用查询逻辑，兼容TAO老数据
     * @param criteria
     * @param appName
     * @param storeId
     */
    private void appendAppNameQuery(Criteria criteria, String appName, String storeId) {
        if (criteria == null || storeId == null) {
            return;
        }
        boolean isNotTaoTrade = !(CommonPlatformConstants.PLATFORM_TAO.equals(storeId) && StringUtils.isEmpty(appName));
        // 只有非淘宝交易才查询appName
        if (isNotTaoTrade) {
            criteria.and(MongoConstant.APP_NAME_FIELD).is(StringUtils.trimToNull(appName));
        }
    }
}
