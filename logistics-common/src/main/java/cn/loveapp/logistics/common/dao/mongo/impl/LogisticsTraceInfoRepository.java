package cn.loveapp.logistics.common.dao.mongo.impl;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import cn.loveapp.logistics.common.constant.MongoConstant;
import cn.loveapp.logistics.common.dao.mongo.BaseMongoDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.dao.mongo.LogisticsTraceInfoDao;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;

/**
 * LogisticsTraceInfoRepository
 *
 * <AUTHOR>
 * @date 2022/1/24
 */
@Repository
public class LogisticsTraceInfoRepository extends BaseMongoDao implements LogisticsTraceInfoDao {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsTraceInfoRepository.class);

    @Autowired
    protected MongoTemplate mongoTemplate;

    public LogisticsTraceInfoRepository(MongoTemplate mongoTemplate) {
        super(mongoTemplate);
    }

    @Override
    public void save(List<LogisticsTraceInfo> logisticsTraceInfoList) throws IOException {
        try {
            Date date = new Date();
            logisticsTraceInfoList.forEach(info -> info.setGmtCreate(date));
            mongoTemplate.insert(logisticsTraceInfoList, LogisticsTraceInfo.class);
        } catch (Throwable e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    @Override
    public List<LogisticsTraceInfo> queryByTid(List<String> tidList, String sellerId, String platformId,
                                               String appName, String logisticsStoreId) {
        Criteria criteria = createCriteria(sellerId, platformId, appName).and("tid").in(tidList);
        if (StringUtils.isNotEmpty(logisticsStoreId)) {
            // 物流轨迹查询根据物流平台
            criteria.and(MongoConstant.LOGISTICS_STORE_ID).is(logisticsStoreId);
        }

        Query query = Query.query(criteria);
        return mongoTemplate.find(query, LogisticsTraceInfo.class);
    }

    @Override
    public List<LogisticsTraceInfo> queryByTidAndStoreIds(List<String> tidList, String sellerId, String platformId, String appName,
                                                          List<String> logisticsStoreIds) {
        Criteria criteria = createCriteria(sellerId, platformId, appName).and("tid").in(tidList);
        if (CollectionUtils.isNotEmpty(logisticsStoreIds)) {
            // 物流轨迹查询根据物流平台
            criteria.and(MongoConstant.LOGISTICS_STORE_ID).in(logisticsStoreIds);
        }
        Query query = Query.query(criteria);
        return mongoTemplate.find(query, LogisticsTraceInfo.class);
    }

    @Override
    public List<LogisticsTraceInfo> queryByTidAndInvoiceNo(List<String> tidList, List<String> invoiceNoList,
        String sellerId, String platformId, String appName) {
        Criteria criteria = createCriteria(sellerId, platformId, appName).and("tid").in(tidList);
        if (CollectionUtils.isNotEmpty(invoiceNoList)) {
            criteria.and("outSid").in(invoiceNoList);
        }
        Query query = Query.query(criteria);
        return mongoTemplate.find(query, LogisticsTraceInfo.class);
    }

    @Override
    public LogisticsTraceInfo queryLastByTidAndOutSid(LogisticsTraceInfo traceInfo) {
        String sellerId = traceInfo.getSellerId();
        String platformId = traceInfo.getPlatformId();
        String appName = traceInfo.getAppName();
        Criteria criteria = createCriteria(sellerId, platformId, appName).and("tid").is(traceInfo.getTid())
            .and("outSid").is(traceInfo.getOutSid());
        Query query = Query.query(criteria).limit(1);
        List<LogisticsTraceInfo> result = mongoTemplate.find(query, LogisticsTraceInfo.class);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }

    @Override
    public long deleteByTid(List<String> tidList, String sellerId, String platformId, String appName) {
        Criteria criteria = createCriteria(sellerId, platformId, appName).and("tid").in(tidList);
        return mongoTemplate.remove(Query.query(criteria), LogisticsTraceInfo.class).getDeletedCount();
    }

    private Criteria createCriteria(String sellerId, String platformId, String appName) {
        return Criteria.where("sellerId").is(sellerId).and("platformId").is(platformId).and("appName").is(appName);
    }

    @Override
    protected String getShardPrimaryKey() {
        return MongoConstant.TID_FIELD;
    }

    @Override
    protected boolean isShardCollection() {
        return true;
    }

    @Override
    protected String getCollectionName() {
        return mongoTemplate.getCollectionName(LogisticsTraceInfo.class);
    }
}
