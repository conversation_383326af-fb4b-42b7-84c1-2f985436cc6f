package cn.loveapp.logistics.common.dao.redis;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 物流异常缓存redis
 *
 * <AUTHOR>
 * @Date 2023/6/21 17:13
 */
@Repository
public class LogisticsAbnormalRedisDao {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsAbnormalRedisDao.class);

    private static final String PREFIX_LOGISTICS_ABNORMAL = "logistics:abnormal:";

    private static final String COLON = ":";

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private LogisticsConfig logisticsConfig;

    private String initKey(String abnormalType, String storeId, String appName, String sellerId) {
        try {
            return PREFIX_LOGISTICS_ABNORMAL + abnormalType + COLON + storeId + COLON + StringUtils.trimToEmpty(appName) + COLON + sellerId;
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取异常缓存（不存在则查库并更新）
     *
     * @param supplier
     * @param abnormalType
     * @param storeId
     * @param appName
     * @param sellerId
     * @return
     */
    public String getCache(Supplier<String> supplier, String abnormalType, String storeId, String appName, String sellerId) {
        String key = initKey(abnormalType, storeId, appName, sellerId);
        ValueOperations<String, String> operations = redisTemplate.opsForValue();
        if (StringUtils.isEmpty(key)) {
            return supplier.get();
        }
        String value = operations.get(key);
        if (value == null) {
            value = supplier.get();
            if (value != null) {
                operations.set(key, value, logisticsConfig.getAbnormalRedisTimeoutDays(), TimeUnit.DAYS);
            }
        } else {
            LOGGER.logInfo("Redis 缓存命中, key=" + key + " value=" + value);
        }
        return value;
    }

    /**
     * 清除缓存
     *
     * @param abnormalType
     * @param storeId
     * @param appName
     * @param sellerId
     */
    public void clear(String abnormalType, String storeId, String appName, String sellerId) {
        String key = initKey(abnormalType, storeId, appName, sellerId);
        if (StringUtils.isEmpty(key)) {
            return;
        }
        redisTemplate.delete(key);
        LOGGER.logInfo("Redis 清除缓存 key=" + key);
    }
}
