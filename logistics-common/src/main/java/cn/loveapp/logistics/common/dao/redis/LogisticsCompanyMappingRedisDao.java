package cn.loveapp.logistics.common.dao.redis;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.dto.LogisticsCompanyInfoDTO;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 物流公司映射缓存DAO
 *
 * <AUTHOR>
 * @Date 2023/8/10 15:37
 */
@Repository
public class LogisticsCompanyMappingRedisDao {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsCompanyMappingRedisDao.class);

    private static final String PREFIX_LOGISTICS_COMPANY = "logistics:company:mapping:";

    private static final String COLON = ":";

    private StringRedisTemplate stringRedisTemplate;

    /**
     * 物流公司映射缓存天数
     */
    @Value("${logistics.company.mapping.cacheDays:1}")
    private Integer cacheDays;

    public LogisticsCompanyMappingRedisDao(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }


    private String initKey(String sourceCompany, String sourceStoreId, String targetLogisticsStoreId) {
        try {
            return PREFIX_LOGISTICS_COMPANY + sourceCompany + COLON + sourceStoreId + COLON + targetLogisticsStoreId;
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 判断是否存在
     *
     * @param collection
     * @return
     */
    private Boolean hasExistCollection(String collection) {
        try {
            if (StringUtils.isEmpty(collection)) {
                return false;
            }
            return stringRedisTemplate.hasKey(collection);
        } catch (Exception e) {
            if (e.getMessage() == null) {
                LOGGER.logError("Entry '" + collection + "' does not exist in cache", e);
            } else {
                LOGGER.logError("Unable to find entry '" + collection + "' in cache collection '" + collection + "': " + e.getMessage() + "", e);
            }
            return false;
        }
    }

    private boolean put(String collection, LogisticsCompanyInfoDTO companyInfoDTO) {
        if (StringUtils.isEmpty(collection)) {
            return false;
        }
        try {
            ValueOperations<String, String> operations = stringRedisTemplate.opsForValue();
            String value = JSON.toJSONString(companyInfoDTO);
            operations.set(collection, value, cacheDays, TimeUnit.DAYS);
            return true;
        } catch (Exception e) {
            LOGGER.logError("Unable to add object of key " + collection + " to cache collection '" + collection + "': " + e.getMessage() + "", e);
            return false;
        }
    }

    private LogisticsCompanyInfoDTO find(String collection) {
        if (StringUtils.isEmpty(collection)) {
            return null;
        }
        try {
            ValueOperations<String, String> operations = stringRedisTemplate.opsForValue();
            String value = operations.get(collection);
            if (value == null) {
                return null;
            }
            LOGGER.logInfo("Redis 缓存命中, key=" + collection + " value=" + value);
            return JSON.parseObject(value, LogisticsCompanyInfoDTO.class);
        } catch (Exception e) {
            if (e.getMessage() == null) {
                LOGGER.logError("Entry '" + collection + "' does not exist in cache", e);
            } else {
                LOGGER.logError("Unable to find entry '" + collection + "' in cache collection '" + collection + "': " + e.getMessage() + "", e);
            }
            return null;
        }
    }

    /**
     * 获取物流公司映射缓存（不存在则查库并更新）
     * @param sourceCompanyInfo
     * @param targetLogisticsStoreId
     * @param supplier
     * @return
     */
    public LogisticsCompanyInfoDTO getCache(LogisticsCompanyInfoDTO sourceCompanyInfo, String targetLogisticsStoreId, Supplier<LogisticsCompanyInfoDTO> supplier) {
        String logisticsStoreId = sourceCompanyInfo.getLogisticsStoreId();
        String companyId = sourceCompanyInfo.getCompanyId();
        String companyCode = sourceCompanyInfo.getCompanyCode();
        String companyName = sourceCompanyInfo.getCompanyName();

        String sourceCompany = ConvertUtil.firstNotNull(companyId, companyCode, companyName);

        if (StringUtils.isAllEmpty(sourceCompany, logisticsStoreId, targetLogisticsStoreId)) {
            return null;
        }

        String key = initKey(sourceCompany, logisticsStoreId, targetLogisticsStoreId);
        if (StringUtils.isEmpty(key)) {
            return supplier.get();
        }
        LogisticsCompanyInfoDTO logisticsCompanyInfoDTO = find(key);
        if (logisticsCompanyInfoDTO != null) {
            return logisticsCompanyInfoDTO;
        }
        logisticsCompanyInfoDTO = supplier.get();
        if (logisticsCompanyInfoDTO != null) {
            put(key, logisticsCompanyInfoDTO);
        }
        return logisticsCompanyInfoDTO;
    }


}
