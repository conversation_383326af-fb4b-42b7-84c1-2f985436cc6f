package cn.loveapp.logistics.common.dao.redis;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.RedisUtil;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.exception.LockLogisticsFailedException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 物流入库运单锁
 *
 * <AUTHOR>
 * @Date 2023/6/8 15:19
 */
@Component
public class LogisticsSaveLockRedisDao {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsSaveLockRedisDao.class);

    private static ThreadLocal<Map<String, AtomicInteger>> lockCount = new ThreadLocal<>();

    private static final String PREFIX_LOGISTICS_KEY = "logistics:batch:outSid:";

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private LogisticsConfig logisticsConfig;

    private String initKey(String outSid, String sellerId, String storeId, String appName) {
        try {
            return PREFIX_LOGISTICS_KEY + URLEncoder.encode(outSid, "utf8") + sellerId + storeId + StringUtils.trimToEmpty(appName);
        } catch (UnsupportedEncodingException e) {
            LOGGER.logError(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 锁定运单（可重入）
     * @param outSid
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    public String lockLogistics(String outSid, String sellerId, String storeId, String appName) {
        try {
            return lockLogistics(outSid, sellerId, storeId, appName, -1);
        } catch (Exception e) {
            LOGGER.logError(sellerId, outSid, "物流单锁定失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 锁定运单（可重入）
     * @param outSid
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    public String lockLogistics(String outSid, String sellerId, String storeId, String appName, int retryCount) throws LockLogisticsFailedException {
        if (StringUtils.isEmpty(outSid)) {
            return null;
        }
        String key = initKey(outSid, sellerId, storeId, appName);

        if (StringUtils.isEmpty(key)) {
            return null;
        }

        Map<String, AtomicInteger> countMap = lockCount.get();
        if (countMap == null) {
            countMap = new HashMap<>(1);
        }

        AtomicInteger count = countMap.get(key);
        if (count != null) {
            count.incrementAndGet();
            return count.toString();
        }

        String lockValue = null;
        try {
            lockValue = RedisUtil.timedLock(key, Duration.ofSeconds(logisticsConfig.getOutSidLockTimeout()), retryCount, stringRedisTemplate);
        } catch (Exception e) {
            if (retryCount >= 0) {
                throw new LockLogisticsFailedException("物流单锁定失败: " + outSid + ", " + e.getMessage(), e);
            } else {
                LOGGER.logError(sellerId, outSid, "物流单锁定失败: " + outSid + ", " + e.getMessage(), e);
            }
        }
        if (lockValue == null) {
            throw new LockLogisticsFailedException("物流单锁定失败, 超过重试次数限制: " + outSid);
        } else {
            countMap.put(key, new AtomicInteger(1));
            lockCount.set(countMap);
        }
        return lockValue;
    }

    /***
     * 解锁运单
     * @param outSid
     * @param sellerId
     * @param storeId
     * @param appName
     * @param lockValue
     */
    public void unLockLogistics(String outSid, String sellerId, String storeId, String appName, String lockValue) {
        if (StringUtils.isAnyEmpty(outSid, lockValue)) {
            return;
        }
        String key = initKey(outSid, sellerId, storeId, appName);

        if (StringUtils.isEmpty(key)) {
            return;
        }

        Map<String, AtomicInteger> countMap = lockCount.get();
        if (countMap == null) {
            return;
        }

        AtomicInteger count = countMap.get(key);
        if (count != null) {
            if (count.get() > 1) {
                count.decrementAndGet();
                return;
            }
            try {
                RedisUtil.timedUnlock(key, lockValue, stringRedisTemplate);
                countMap.remove(key);
            } catch (Exception e) {
                LOGGER.logError(sellerId, outSid, "物流单锁定失败 " + lockValue + " : " + e.getMessage(), e);
            }
        }
    }

}
