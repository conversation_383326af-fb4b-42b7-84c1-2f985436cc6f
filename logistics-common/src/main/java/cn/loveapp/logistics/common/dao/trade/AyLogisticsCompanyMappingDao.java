package cn.loveapp.logistics.common.dao.trade;

import cn.loveapp.logistics.common.entity.db.AyLogisticsCompanyMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物流公司映射表Dao
 *
 * <AUTHOR>
 * @Date 2023/8/10 14:41
 */
public interface AyLogisticsCompanyMappingDao {


    /**
     * 插入单条
     *
     * @param mapping
     * @return
     */
    int insert(AyLogisticsCompanyMapping mapping);

    /**
     * 批量插入
     * @param mappingList
     * @return
     */
    int batchInsert(@Param("mappingList") List<AyLogisticsCompanyMapping> mappingList);

    /**
     * 更新数据
     *
     * @param mapping
     * @return
     */
    int update(@Param("mapping") AyLogisticsCompanyMapping mapping);

    /**
     * 根据来源物流公司获取物流映射
     *
     * @param sourceCompanyId
     * @param logisticsStoreId
     * @return
     */
    AyLogisticsCompanyMapping queryByCompanyIdAndStoreId(@Param("sourceCompanyId") String sourceCompanyId, @Param("logisticsStoreId")String logisticsStoreId);

    /**
     * 根据来源物流公司获取物流映射
     *
     * @param sourceCompanyName
     * @param logisticsStoreId
     * @return
     */
    AyLogisticsCompanyMapping queryByCompanyNameAndStoreId(@Param("sourceCompanyName")String sourceCompanyName, @Param("logisticsStoreId")String logisticsStoreId);


    /**
     * 根据来源物流公司获取物流映射
     *
     * @param sourceCompanyCode
     * @param logisticsStoreId
     * @return
     */
    AyLogisticsCompanyMapping queryByCompanyCodeAndStoreId(@Param("sourceCompanyCode")String sourceCompanyCode, @Param("logisticsStoreId")String logisticsStoreId);

    /**
     * 根据爱用Id获取目标物流公司
     *
     * @param ayLogisticsCompanyId
     * @param targetLogisticsStoreId
     * @return
     */
    AyLogisticsCompanyMapping queryByAyIdAndStoreId(@Param("ayLogisticsCompanyId")String ayLogisticsCompanyId, @Param("targetLogisticsStoreId")String targetLogisticsStoreId);


}
