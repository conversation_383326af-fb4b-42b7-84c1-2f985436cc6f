package cn.loveapp.logistics.common.dto;

import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import com.google.common.collect.Lists;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 异常条件查询DTO
 *
 * <AUTHOR>
 * @Date 2023/7/21 16:15
 */
@Data
public class AbnormalQueryDTO {

    /**
     * 异常统计的类型
     */
    private List<String> abnormalTypes;

    /**
     * 处理状态
     */
    private AbnormalProcessStatus processStatus;

    /**
     * 是否只查询存在异常的运单
     */
    private Boolean onlySearchAbnormal;

    /**
     * 是否排除已处理异常物流
     */
    private Boolean isExcludeProcessed;

    /**
     * 物流单是否必须存在发货时间
     */
    private Boolean isExistConsignTime;

    /**
     * 是否排除交易其他异常
     */
    private Boolean isExcludeOtherAbnormalOfTradeApp;

    /**
     * 异常统计发货开始时间
     */
    private LocalDateTime startConsignTime;


    public void setAbnormalTypes(String abnormalTypes) {
        if (abnormalTypes != null) {
            String[] split = abnormalTypes.split(",");
            this.abnormalTypes = Lists.newArrayList(split);
        }
    }

}
