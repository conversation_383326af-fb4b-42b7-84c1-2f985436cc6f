package cn.loveapp.logistics.common.dto;

import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import lombok.Data;

import java.util.List;

/**
 * 物流ES列表返回,携带es数据以及es聚合数据
 *
 * <AUTHOR>
 * @Date 2023/7/3 17:02
 */
@Data
public class AyLogisticsOrderInfoSearchListAndAggDto {

    /**
     * 结果总数
     */
    private Integer totalResults = 0;

    /**
     * 是否存在下一页
     */
    private Boolean hasNext = false;

    /**
     * 物流单结果列表
     */
    List<LogisticsOrderInfoSearchES> logisticsOrderInfoSearchESList;

    /**
     * search after查询时，响应回去的游标值
     */
    private List<Object> searchSortValues;

}
