package cn.loveapp.logistics.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 异常物流统计DTO
 *
 * <AUTHOR>
 * @Date 2023/6/21 18:34
 */
@Data
@ApiModel
public class LogisticsAbnormalCountDTO {

    /**
     * 发货N小时后未揽件计数
     */
    @ApiModelProperty(value = "发货N小时后未揽件计数")
    @JSONField(name = "not_picked_up_after_send_count")
    @JsonProperty("not_picked_up_after_send_count")
    private Integer notPickedUpAfterSendCount;

    /**
     * 揽收将要超时
     */
    @ApiModelProperty(value = "揽收超时前N小时将要超时")
    @JSONField(name = "collection_will_timeout_count")
    @JsonProperty("collection_will_timeout_count")
    private Integer collectionWillTimeoutCount;

    /**
     * 标记拦截后N小时未截返计数
     */
    @ApiModelProperty(value = "标记拦截后N小时未截返计数")
    @JSONField(name = "not_returned_of_interception_count")
    @JsonProperty("not_returned_of_interception_count")
    private Integer notReturnedOfInterceptionCount;

    /**
     * 揽收后签收前轨迹超N小时未更新计数
     */
    @ApiModelProperty(value = "揽收后签收前轨迹超N小时未更新计数")
    @JSONField(name = "tracking_not_updated_after_of_pickup_count")
    @JsonProperty("tracking_not_updated_after_of_pickup_count")
    private Integer trackingNotUpdatedAfterOfPickupCount;

    /**
     * 派件异常计数
     */
    @ApiModelProperty(value = "派件异常计数")
    @JSONField(name = "delivery_exception_count")
    @JsonProperty("delivery_exception_count")
    private Integer deliveryExceptionCount;

    /**
     * 拒收计数
     */
    @ApiModelProperty(value = "拒收计数")
    @JSONField(name = "refused_accept_count")
    @JsonProperty("refused_accept_count")
    private Integer refusedAcceptCount;

    /**
     * 超时未签收计数
     */
    @ApiModelProperty(value = "超时未签收计数")
    @JSONField(name = "not_delivered_on_time_count")
    @JsonProperty("not_delivered_on_time_count")
    private Integer notDeliveredOnTimeCount;

    /**
     * 揽收后更新超时
     */
    @ApiModelProperty(value = "揽收后更新超时")
    @JSONField(name = "first_trace_after_picked_up_timeout_count")
    @JsonProperty("first_trace_after_picked_up_timeout_count")
    private Integer firstTraceAfterPickedUpTimeoutCount;

    /**
     * 揽收后更新将超时
     */
    @ApiModelProperty(value = "揽收后更新将超时")
    @JSONField(name = "first_trace_after_picked_up_will_timeout_count")
    @JsonProperty("first_trace_after_picked_up_will_timeout_count")
    private Integer firstTraceAfterPickedUpWillTimeoutCount;

    /**
     * 中转超时
     */
    @ApiModelProperty(value = "中转超时")
    @JSONField(name = "transfer_timeout_count")
    @JsonProperty("transfer_timeout_count")
    private Integer transferTimeoutCount;

    /**
     * 派签超时
     */
    @ApiModelProperty(value = "派签超时")
    @JSONField(name = "delivery_sign_timeout_count")
    @JsonProperty("delivery_sign_timeout_count")
    private Integer deliverySignTimeOutCount;

    /**
     * 其他异常计数
     */
    @ApiModelProperty(value = "其他异常计数")
    @JSONField(name = "other_abnormal_count")
    @JsonProperty("other_abnormal_count")
    private Integer otherAbnormalCount;

    /**
     * 其他异常计数(交易)
     */
    @ApiModelProperty(value = "其他异常计数")
    @JSONField(name = "other_abnormal_of_trade_app_count")
    @JsonProperty("other_abnormal_of_trade_app_count")
    private Integer otherAbnormalOfTradeAppCount;

    /**
     * 包裹与关键字异常计数
     */
    @ApiModelProperty(value = "其他异常计数")
    @JSONField(name = "parcel_abnormal_and_custom_keyword_count")
    @JsonProperty("parcel_abnormal_and_custom_keyword_count")
    private Integer parcelAbnormalAndCustomKeywordCount;

}
