package cn.loveapp.logistics.common.dto;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 物流包裹对象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-11-05 下午12:24
 */
@Data
public class LogisticsDetailDTO {

    /**
     * 物流平台
     */
    @JSONField(name = "logistics_store_id")
    @JsonProperty("logistics_store_id")
    private String logisticsStoreId;

    /**
     * 物流公司名称
     */
    @JSONField(name = "company_name")
    @JsonProperty("company_name")
    private String companyName;

    /**
     * 物流公司名称
     */
    @JSONField(name = "company_code")
    @JsonProperty("company_code")
    private String companyCode;


    /**
     * 运单号
     */
    @JSONField(name = "out_sid")
    @JsonProperty("out_sid")
    private String outSid;

    /**
     * 子订单号
     */
    @Deprecated
    private List<String> oid;

    /**
     * 订单号
     */
    private List<String> tid;

    /**
     * 是否拆单发货 ----已废弃
     */
    @Deprecated
    @JSONField(name = "is_split")
    @JsonProperty("is_split")
    private Integer isSplit;

    /**
     * 流转信息列表
     */
    @JSONField(name = "trace_list")
    @JsonProperty("trace_list")
    private TracesDTO traceList;

    public LogisticsDetailDTO() {}

}
