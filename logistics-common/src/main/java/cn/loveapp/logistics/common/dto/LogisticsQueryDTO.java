package cn.loveapp.logistics.common.dto;

import cn.loveapp.logistics.common.constant.AySearchTypeEnum;
import cn.loveapp.logistics.common.constant.BuyerSellerMemoSearchType;
import cn.loveapp.logistics.common.constant.LogisticsPackType;
import com.google.common.collect.Lists;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流查询DTO
 *
 * <AUTHOR>
 * @Date 2023/6/21 18:46
 */
@Data
public class LogisticsQueryDTO {

    public static final String SORT_FIELD_CONSIGN_TIME  = "consignTime";

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向 asc / desc
     */
    private String sortDirection;

    /**
     * 异常统计的类型
     */
    private List<String> abnormalTypes;

    /**
     * 是否只查询存在异常的运单
     */
    private Boolean onlySearchAbnormal;

    /**
     * 是否只查询正常件
     */
    private Boolean onlySearchNormal;


    /**
     * 是否排除已处理异常物流
     */
    private Boolean isExcludeProcessed;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 运单号
     */
    private List<String> outSidList;

    /**
     * 订单号
     */
    private String tid;

    /**
     * 订单号或买家昵称
     */
    private List<String> tidOrBuyerOpenUid;

    /**
     * 包裹类型
     */
    private LogisticsPackType packType;

    /**
     * 物流公司Code/name
     */
    private String logisticsCompany;

    /**
     * 快递公司集合
     */
    private List<String> logisticsCompanyList;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 发货时间筛选开始时间
     */
    private LocalDateTime startConsignTime;

    /**
     * 发货时间筛选结束时间
     */
    private LocalDateTime endConsignTime;

    /**
     * 最新物流状态
     */
    private String lastAction;

    /**
     * 处理进度
     */
    private List<String> processStatus;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页数
     */
    private Integer pageSize;

    private Integer limit;
    private Integer start;

    /**
     * 轨迹排序方向 asc/desc
     */
    private String traceSortDirection;

    /**
     * 去重是否需要依据status字段
     */
    private boolean distinctWithStatusField;

    /**
     * 是否返回已处理状态的异常列表
     */
    private Boolean includeProcessedAbnormal;

    /**
     * 物流单是否必须存在发货时间
     */
    private Boolean isExistConsignTime;

    /**
     * 是否排除交易其他异常
     */
    private Boolean isExcludeOtherAbnormalOfTradeApp;

    /**
     * 买家openUid
     */
    private String buyerOpenUid;

    /**
     * search after查询时，响应回去的游标值
     */
    private List<Object> lastSearchSortValues;

    /**
     * 订单旗帜
     */
    private List<Integer> orderSellerFlagList;

    /**
     * 订单自定义备注标记 对应订单的ayCustomFlag
     */
    private List<Integer> orderAyCustomFlagList;

    /**
     * 订单备注
     */
    private String orderSellerMemo;

    /**
     * 买家留言卖家备注搜索类型
     */
    private BuyerSellerMemoSearchType buyerSellerMemoSearchType;

    /**
     * 是否存在退款
     */
    private Boolean isRefund;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku外部编码
     */
    private String outerSkuId;

    /**
     * 旗帜搜索类型
     */
    private AySearchTypeEnum orderFlagSearchType;

    /**
     * 商品信息搜索裂隙
     */
    private AySearchTypeEnum skuInfoSearchType;

    public void setAbnormalTypes(List<String> abnormalTypes) {
        this.abnormalTypes = abnormalTypes;
    }

    public void setAbnormalTypes(String abnormalTypes) {
        if (abnormalTypes != null) {
            String[] split = abnormalTypes.split(",");
            this.abnormalTypes = Lists.newArrayList(split);
        }
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
        if (pageNo != null) {
            this.start = pageNo - 1;
        }
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        if (pageSize != null) {
            this.limit = pageSize;
        }
    }

}
