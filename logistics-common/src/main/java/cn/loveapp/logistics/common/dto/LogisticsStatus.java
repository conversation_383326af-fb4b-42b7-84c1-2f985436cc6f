package cn.loveapp.logistics.common.dto;

/**
 * 物流状态
 *
 * <AUTHOR>
 * @date 2018/11/27
 */
public enum LogisticsStatus {
    /**
     * 物流状态: 订单完成或关闭
     */
    CLOSE(0),

    /**
     * 物流状态: 已发货未揽件
     */
    NOPACKAGE(1),

    /**
     * 物流状态: 运输途中
     */
    TRANSPORTATION(2),

    /**
     * 物流状态: 已签收
     */
    SIGNED(3),

    /**
     * 物流状态: 不知如何分类, 需要忽略异常处理状态
     */
    UNKNOWN(9);

    private int value;

    LogisticsStatus(int value) {
        this.value = value;
    }

    public int value() {
        return value;
    }

    public static LogisticsStatus valueOf(int value) {
        switch (value) {
            case 0:
                return CLOSE;
            case 1:
                return NOPACKAGE;
            case 2:
                return TRANSPORTATION;
            case 3:
                return SIGNED;
            default:
                return UNKNOWN;
        }
    }
}
