package cn.loveapp.logistics.common.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @Description 预发货DTO
 * @Date 15:48 2023/11/25
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrepareConsignDTO {

    /**
     * 是否强制订阅
     * 物流轨迹为空 || forceSubscribe = true时，调用订阅物流,
     */
    private boolean forceSubscribe;

    /**
     * 是否在订阅时上报使用记录
     * 调用php，上报物流包额度使用记录
     */
    private boolean reportedUsedWhenSubscribe;

    /**
     * 平台来源
     * aiyong账号查询时AIYONG
     * 淘宝交易使用时传入数据 TAOTRADE
     */
    private SourceAppEnum sourceApp = SourceAppEnum.AIYONG;


    /**
     * app来源
     */
    public enum SourceAppEnum {
        /**
         * 爱用来源
         */
        AIYONG(SubscribeConsumeUserEnum.TARGET_USER),
        /**
         * 淘宝交易来源
         */
        TAOTRADE(SubscribeConsumeUserEnum.LOGIN_USER),

        /**
         * tradeERP交易来源
         */
        TRADEERP(SubscribeConsumeUserEnum.LOGIN_USER);

        private SubscribeConsumeUserEnum var;
        public SubscribeConsumeUserEnum value() {
            return var;
        }

        SourceAppEnum(SubscribeConsumeUserEnum var) {
            this.var = var;
        }
    }

    /**
     * 扣款账号取值位置
     */
    public enum SubscribeConsumeUserEnum {
        /**
         * 使用的扣款账户从参数中取值
         */
        TARGET_USER,
        /**
         * 使用的扣款账户从登录用户中取值
         */
        LOGIN_USER,

    }
}
