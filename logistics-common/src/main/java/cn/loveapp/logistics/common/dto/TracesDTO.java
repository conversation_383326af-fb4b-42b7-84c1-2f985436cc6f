package cn.loveapp.logistics.common.dto;

import java.util.*;
import java.util.stream.Collectors;

import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;

/**
 * 物流中转对象, 只是为了接受前端json数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-11-02 下午6:19
 */
@Data
@ApiModel
public class TracesDTO {

    /**
     * 时间转换格式
     */
    private static final FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    /**
     * 物流流转信息列表
     */
    @ApiModelProperty(value = "物流流转信息列表")
    @JSONField(name = "transit_step_info")
    @JsonProperty("transit_step_info")
    private List<TransitStepDTO> transitStepInfo;


    public static Map<String, TracesDTO> of(List<LogisticsTraceInfo> logisticsTraceInfos, String traceSortDirection, boolean isDistinctWithStatusField) {
        if (CollectionUtils.isEmpty(logisticsTraceInfos)) {
            return new HashMap<>();
        }

        Map<String, TracesDTO> tracesDTOMap = new HashMap<>();

        // 以描述out_sid进行分组
        Map<String, List<LogisticsTraceInfo>> logisticsDetailDTOByOutSid =
            logisticsTraceInfos.stream().collect(Collectors.groupingBy(LogisticsTraceInfo::getOutSid));

        // 遍历包裹信息，即遍历每组信息
        for (Map.Entry<String, List<LogisticsTraceInfo>> entry : logisticsDetailDTOByOutSid.entrySet()) {
            LogisticsDetailDTO logisticsDetailDTO = new LogisticsDetailDTO();

            // 使用map的key进行去重
            Map<String, TransitStepDTO> stepList = new HashMap<>();


            for (LogisticsTraceInfo logisticsTraceInfo : entry.getValue()) {
                // 对每个包裹进行去重，根据描述去重
                TransitStepDTO step = new TransitStepDTO(logisticsTraceInfo.getAction(),
                    logisticsTraceInfo.getDesc(), fastDateFormat.format(logisticsTraceInfo.getModified()), logisticsTraceInfo.getStatus());
                // 将描述和时间都相同的过滤掉
                stepList.put(logisticsTraceInfo.getActionAndDescAndModified(isDistinctWithStatusField), step);
                logisticsDetailDTO.setOutSid(logisticsTraceInfo.getOutSid());
                logisticsDetailDTO.setCompanyName(logisticsTraceInfo.getCompanyName());
                logisticsDetailDTO.setCompanyCode(logisticsTraceInfo.getCompanyCode());
                String logisticsStoreId = logisticsTraceInfo.getLogisticsStoreId();
                if (StringUtils.isEmpty(logisticsTraceInfo.getLogisticsStoreId())) {
                    // 兼容老数据
                    logisticsStoreId = logisticsTraceInfo.getPlatformId();
                }
                logisticsDetailDTO.setLogisticsStoreId(logisticsStoreId);
            }

            TracesDTO tracesDTO = new TracesDTO();
            List<TransitStepDTO> transitStepDTOS = new ArrayList<>(stepList.values());
            List<TransitStepDTO> sortedList = sort(transitStepDTOS, traceSortDirection);
            tracesDTO.setTransitStepInfo(sortedList);
            tracesDTOMap.put(entry.getKey(), tracesDTO);
        }

        return tracesDTOMap;
    }

    /**
     * 轨迹排序 desc
     * @param transitStepInfo
     * @return
     */
    private static List<TransitStepDTO> sort(List<TransitStepDTO> transitStepInfo, String traceSortDirection){
        if ("desc".equals(traceSortDirection)) {
            transitStepInfo = transitStepInfo.stream().sorted(Comparator.comparing(TransitStepDTO::getStatusTime).reversed())
                .collect(Collectors.toList());
        } else if ("asc".equals(traceSortDirection)) {
            transitStepInfo = transitStepInfo.stream().sorted(Comparator.comparing(TransitStepDTO::getStatusTime))
                .collect(Collectors.toList());
        }
        return transitStepInfo;
    }

}
