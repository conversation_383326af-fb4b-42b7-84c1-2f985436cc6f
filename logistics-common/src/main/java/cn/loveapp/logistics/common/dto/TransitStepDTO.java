package cn.loveapp.logistics.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 *
 * 物流流转对象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-10-31 下午4:22
 */
@Data
public class TransitStepDTO {

    /**
     * 流转事件
     */
    @ApiModelProperty(value = "流转事件")
    @JSONField(name = "action")
    @JsonProperty("action")
    private String action;

    /**
     * 流转状态
     */
    @ApiModelProperty(value = "流转状态")
    @JSONField(name = "status")
    @JsonProperty("status")
    private String status;

    /**
     * 物流状态描述
     */
    @ApiModelProperty(value = "物流状态描述")
    @JSONField(name = "status_desc")
    @JsonProperty("status_desc")
    private String statusDesc;

    /**
     * 物流状态改变时间
     */
    @ApiModelProperty(value = "物流状态描述")
    @JSONField(name = "status_time")
    @JsonProperty("status_time")
    private String statusTime;

    public TransitStepDTO() {}

    public TransitStepDTO(String action, String statusDesc, String statusTime, String status) {
        this.action = action;
        this.statusDesc = statusDesc;
        this.statusTime = statusTime;
        this.status = status;
    }

}
