package cn.loveapp.logistics.common.dto;

import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import lombok.Data;

import java.util.List;

/**
 * 目标用户信息的DTO 用于最终(多店鉴权后的)存储用户信息
 *
 * <AUTHOR>
 * @date 2020-02-10 21:07:13
 */
@Data
public class UserInfoDTO {
    /**
     * 目标用户Nick
     */
    private String nick;

    /**
     * 目标用户id
     */
    private String sellerId;

    /**
     * 目标用户 storeId
     */
    private String storeId;

    /**
     * 目标用户 appName
     */
    private String appName;

    /**
     * 目标用户 CorpId
     */
    private String corpId;

    /**
     * 用户版本
     */
    private Integer vipFlag;

    /**
     * 目标商家信息,淘宝用户appName为null
     */
    private List<TargetSellerInfo> targetSellerList;
}
