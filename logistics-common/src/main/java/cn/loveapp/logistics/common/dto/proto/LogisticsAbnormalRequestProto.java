package cn.loveapp.logistics.common.dto.proto;

import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

/**
 * 物流异常校验队列消息体
 *
 * <AUTHOR>
 * @Date 2023/6/20 15:36
 */
@Data
public class LogisticsAbnormalRequestProto {

    /**
     * 自动队列内消息（为true才会自动转发）
     */
    @Tag(1)
    private boolean checkAbnormal = false;

    /**
     * 运单号
     */
    @Tag(2)
    private String outSid;

    /**
     * 物流公司
     */
    @Tag(3)
    private String companyCode;

    /**
     * 物流公司名称
     */
    @Tag(4)
    private String companyName;

    /**
     * 订单号（TAO物流必传）
     */
    @Tag(5)
    private List<String> tidList;

    /**
     * 用户id
     */
    @Tag(6)
    private String sellerId;

    /**
     * 平台
     */
    @Tag(7)
    private String storeId;

    /**
     * 应用
     */
    @Tag(8)
    private String appName;

    /**
     * 物流发送弹窗的异常列表
     */
    @Tag(9)
    private List<String> abnormalTypeNotifyList;

}
