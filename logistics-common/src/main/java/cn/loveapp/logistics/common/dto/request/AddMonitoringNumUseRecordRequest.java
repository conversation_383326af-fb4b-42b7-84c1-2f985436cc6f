package cn.loveapp.logistics.common.dto.request;

import cn.loveapp.logistics.api.constant.BusinessType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 添加物流包余额消耗记录request
 * @Date 15:13 2023/11/24
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddMonitoringNumUseRecordRequest {

    /**
     * 消耗记录集合
     */
    private List<MonitoringNumUseRecord> useRecordList;

    @Data
    public static class MonitoringNumUseRecord {
        /**
         * 商家id
         */
        private String sellerId;

        /**
         * 商家nick
         */
        private String sellerNick;

        /**
         * 商家平台
         */
        private String storeId;

        /**
         * 商家应用
         */
        private String appName;

        /**
         * 订阅店铺的id
         */
        private String subscribeSellerId;

        /**
         * 订阅店铺nick
         */
        private String subscribeSellerNick;

        /**
         * 订阅店铺平台
         */
        private String subscribeStoreId;

        /**
         * 订阅店铺应用
         */
        private String subscribeAppName;

        /**
         * 运单号
         */
        private String waybillNo;

        /**
         * 使用的额度
         */
        private int useQuantity;

        /**
         * 使用类型
         */
        private BusinessType useType;
    }

}
