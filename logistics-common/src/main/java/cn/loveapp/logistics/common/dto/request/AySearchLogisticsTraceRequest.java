package cn.loveapp.logistics.common.dto.request;

import cn.loveapp.logistics.common.dto.PrepareConsignDTO;
import lombok.Data;

/**
 * 物流轨迹查询request
 *
 * <AUTHOR>
 * @Date 2023/5/30 15:17
 */
@Data
public class AySearchLogisticsTraceRequest extends BaseLogisticsTraceRequest {

    /**
     * 运单号
     */
    private String outSid;

    /**
     * 物流公司Code
     */
    private String logisticsCompanyCode;

    /**
     * 发货人手机号后四位
     */
    private String customerName;

    /**
     * 用户id
     */
    private String sellerId;

    /**
     * 用户平台
     */
    private String storeId;

    /**
     * 应用
     */
    private String appName;

    /**
     * 用户nick
     */
    private String sellerNick;

    /**
     * 用户access_token
     */
    private String topSession;

    /**
     * 订单号
     */
    private String tid;

    /**
     * 收件人/寄件人手机 顺丰必传
     */
    private String phone;

    /**
     * 平台来源
     */
    private PrepareConsignDTO.SourceAppEnum sourceApp;

}
