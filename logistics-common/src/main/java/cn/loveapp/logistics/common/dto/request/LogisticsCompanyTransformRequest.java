package cn.loveapp.logistics.common.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 物流公司转换Request
 *
 * <AUTHOR>
 * @Date 2023/5/31 14:49
 */
@Data
public class LogisticsCompanyTransformRequest {

    /**
     * 物流公司平台
     */
    @JSONField(name = "source_store_id")
    private String sourceLogisticStoreId;

    /**
     * 物流公司平台
     */
    @JSONField(name = "target_store_id")
    private String targetLogisticStoreId;

    /**
     * 物流公司code
     */
    @JSONField(name = "company_code")
    private String logisticsCompanyCode;

    /**
     * 物流公司name
     */
    @JSONField(name = "company_name")
    private String logisticsCompanyName;

    /**
     * 物流公司id
     */
    @JSONField(name = "company_id")
    private String logisticsCompanyId;
}
