package cn.loveapp.logistics.common.dto.request;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.common.constant.LogisticsPackType;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * 物流调用日志入库request
 *
 * <AUTHOR>
 * @Date 2023/8/15 10:59
 */
@Data
public class LogisticsMonitoringLogRequest {

    /**
     * 监控类型-查看
     */
    public static final String MONITORING_TYPE_LOOK = "look";

    /**
     * 监控类型-订阅
     */
    public static final String MONITORING_TYPE_SUBSCRIBE = "subscribe";

    /**
     * 接口请求路径
     */
    public static final String REQUEST_URL = "trade/saveLogisticsMonitoringLog";


    /**
     * 日志入库请求列表
     */
    @JSONField(name = "param")
    private List<MonitoringLog> monitoringLogList;


    @Data
    public static class MonitoringLog {

        /**
         * 订单号列表
         */
        @JSONField(name = "tid_list")
        private List<String> tisList;

        /**
         * 售后单列表
         */
        @JSONField(name = "refund_id_list")
        private List<String> refundIdList;

        /**
         * 运单号
         */
        @JSONField(name = "waybill_no")
        private String outSid;

        /**
         * 快递公司code
         */
        @JSONField(name = "cp_code")
        private String cpCode;

        /**
         * 服务类型
         */
        @JSONField(name = "service_type")
        private String serviceType;

        /**
         * 用户nick
         */
        @JSONField(name = "seller_nick")
        private String sellerNick;

        /**
         * 用户id
         */
        @JSONField(name = "user_id")
        private String sellerId;

        /**
         * 用户平台
         */
        @JSONField(name = "store_id")
        private String storeId;

        /**
         * 应用
         */
        @JSONField(name = "app_name")
        private String appName;

        /**
         * 平台来源
         * aiyong账号查询时AIYONG
         * 淘宝交易使用时传入数据 TAOTRADE
         */
        @JSONField(name = "source_app")
        private String sourceApp;

        /**
         * 监控时间
         */
        @JSONField(name = "monitoring_time")
        private String monitoringTime;

        /**
         * 消耗额度
         */
        @JSONField(name = "quota")
        private Integer quota;

        /**
         * 日志类型
         */
        @JSONField(name = "type")
        private String monitoringType;

    }

    public static LogisticsMonitoringLogRequest generalRequest(LogisticsOrderSubscribeDTO logisticsOrderSubscribeDTO, boolean success, Integer withholdingNum, LogisticsPackType packType, String monitoringType) {
        LogisticsMonitoringLogRequest monitoringLogRequest = new LogisticsMonitoringLogRequest();
        MonitoringLog monitoringLog = new MonitoringLog();
        if (BusinessType.refund.equals(logisticsOrderSubscribeDTO.getBusinessType())) {
            monitoringLog.setRefundIdList(logisticsOrderSubscribeDTO.getBusinessIds());
        } else {
            monitoringLog.setTisList(logisticsOrderSubscribeDTO.getBusinessIds());
        }
        monitoringLog.setOutSid(logisticsOrderSubscribeDTO.getOutSid());
        monitoringLog.setCpCode(logisticsOrderSubscribeDTO.getLogisticsCompanyCode());
        if (packType != null) {
            monitoringLog.setServiceType(packType.name());
        }
        monitoringLog.setSellerId(logisticsOrderSubscribeDTO.getSellerId());
        monitoringLog.setSellerNick(logisticsOrderSubscribeDTO.getSellerNick());
        monitoringLog.setAppName(logisticsOrderSubscribeDTO.getAppName());
        monitoringLog.setStoreId(logisticsOrderSubscribeDTO.getStoreId());
        monitoringLog.setSourceApp(logisticsOrderSubscribeDTO.getSourceApp());
        monitoringLog.setMonitoringTime(DateUtil.convertLocalDateTimetoString());
        if (success) {
            monitoringLog.setQuota(withholdingNum);
        } else {
            // 订阅失败，返还
            monitoringLog.setQuota(-withholdingNum);
        }
        monitoringLog.setMonitoringType(monitoringType);
        monitoringLogRequest.setMonitoringLogList(Lists.newArrayList(monitoringLog));
        return monitoringLogRequest;
    }

}
