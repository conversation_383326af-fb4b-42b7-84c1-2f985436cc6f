package cn.loveapp.logistics.common.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 物流额度预扣出request
 *
 * <AUTHOR>
 * @Date 2023/8/15 11:10
 */
@Data
public class MonitoringNumWithholdingRequset {

    /**
     * 用户nick
     */
    @JSONField(name = "nick")
    private String sellerNick;

    /**
     * 用户id
     */
    @JSONField(name = "user_id")
    private String sellerId;

    /**
     * 用户平台
     */
    @JSONField(name = "store_id")
    private String storeId;

    /**
     * 应用
     */
    @JSONField(name = "app_name")
    private String appName;

    /**
     * 预扣除额度
     */
    @JSONField(name = "withholding_num")
    private Integer withholdingNum;

    /**
     * 平台来源
     * aiyong账号查询时AIYONG
     * 淘宝交易使用时传入数据 TAOTRADE
     */
    @JSONField(name = "source_app")
    private String sourceApp;
}
