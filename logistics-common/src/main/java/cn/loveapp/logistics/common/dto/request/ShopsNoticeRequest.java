package cn.loveapp.logistics.common.dto.request;

import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * 多店消息弹窗提醒Request
 *
 * <AUTHOR>
 * @Date 2023/7/20 15:22
 */
@Data
public class ShopsNoticeRequest {

    public static final String TYPE_ERROR = "error";

    public static final String TYPE_INFO = "info";


    @JsonProperty("store_id")
    @JSONField(name = "store_id")
    private String storeId;

    @JsonProperty("app_name")
    @JSONField(name = "app_name")
    private String appName;

    @JsonProperty("nick")
    @JSONField(name = "nick")
    private String sellerNick;

    @JsonProperty("user_id")
    @JSONField(name = "user_id")
    private String sellerId;

    @JsonProperty("content")
    @JSONField(name = "content")
    private Content content;

    @JsonProperty("title")
    @JSONField(name = "title")
    private String title;

    @JsonProperty("type")
    @JSONField(name = "type")
    private String type = TYPE_ERROR;

    @Data
    public static class Content {
        @JsonProperty("error_type")
        @JSONField(name = "error_type")
        private String errorType;

        @JsonProperty("refund_id")
        @JSONField(name = "refund_id")
        private String refundId;

        @JsonProperty("tid")
        @JSONField(name = "tid")
        private String tid;

        @JsonProperty("single")
        @JSONField(name = "single")
        private String single;

        @JsonProperty("waybill_code")
        @JSONField(name = "waybill_code")
        private String waybillCode;

        @JsonProperty("message")
        @JSONField(name = "message")
        private String message;
    }


    /**
     * 异常类型映射
     */
    public enum ErrorType {

        /**
         * 发货N小时后未揽件
         */
        NO_PICKED_UP_AFTER_SEND("unpacked_items_after_shipment", "发货超时未揽件"),

        /**
         * 标记拦截后N小时未截返
         */
        NOT_RETURNED_OF_INTERCEPTION("blocked_marked", "标记拦截后超时未截返"),

        /**
         * 揽收后签收前轨迹超N小时未更新
         */
        TRACKING_NOT_UPDATE_AFTER_OF_PICKUP("before_signing_for_package", "揽收后签收前轨迹超时未更新"),

        /**
         * 其他异常
         */
        OTHER_ABNORMAL("other_logistics", "发生物流异常"),

        DELIVERY_ABNORMAL("delivery_exception", "派件异常"),
        REFUSED_ACCEPT("refused_accept", "物流拒收"),
        NOT_DELIVERED_ON_TIME("not_delivered_on_time", "物流超时未签收"),
        ;

        String key;

        String name;

        ErrorType(String key, String name) {
            this.key = key;
            this.name = name;
        }
    }


    /**
     * 生成请求体
     *
     * @param logisticsOrderInfo
     * @param abnormal
     * @return
     */
    public boolean generalRequest(LogisticsOrderInfo logisticsOrderInfo, String abnormal) {
        if (abnormal == null || logisticsOrderInfo == null) {
            return false;
        }
        this.sellerId = logisticsOrderInfo.getSellerId();
        this.sellerNick = logisticsOrderInfo.getSellerNick();
        this.appName = logisticsOrderInfo.getAppName();
        this.storeId = logisticsOrderInfo.getStoreId();

        this.content = new Content();
        this.content.setWaybillCode(logisticsOrderInfo.getOutSid());

        ErrorType errorType = null;
        if (LogisticsAbnormal.NO_PICKED_UP_AFTER_SEND.value().equals(abnormal)) {
            errorType = ErrorType.NO_PICKED_UP_AFTER_SEND;
        } else if (LogisticsAbnormal.NOT_RETURNED_OF_INTERCEPTION.value().equals(abnormal)) {
            errorType = ErrorType.NOT_RETURNED_OF_INTERCEPTION;
        } else if (LogisticsAbnormal.TRACKING_NOT_UPDATE_AFTER_OF_PICKUP.value().equals(abnormal)) {
            errorType = ErrorType.TRACKING_NOT_UPDATE_AFTER_OF_PICKUP;
        } else if (LogisticsAbnormal.OTHER_ABNORMAL.value().equals(abnormal)) {
            errorType = ErrorType.OTHER_ABNORMAL;
        } else if (LogisticsAbnormal.DELIVERY_ABNORMAL.value().equals(abnormal)) {
            errorType = ErrorType.DELIVERY_ABNORMAL;
        }else if (LogisticsAbnormal.REFUSED_ACCEPT.value().equals(abnormal)) {
            errorType = ErrorType.REFUSED_ACCEPT;
        }else if (LogisticsAbnormal.NOT_DELIVERED_ON_TIME.value().equals(abnormal)) {
            errorType = ErrorType.NOT_DELIVERED_ON_TIME;
        }else {
            return false;
        }

        content.setMessage(errorType.name);
        content.setErrorType(errorType.key);
        this.title = errorType.key;
        return true;
    }

}
