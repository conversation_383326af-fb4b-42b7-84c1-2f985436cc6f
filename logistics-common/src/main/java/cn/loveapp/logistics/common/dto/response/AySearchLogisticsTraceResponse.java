package cn.loveapp.logistics.common.dto.response;

import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 物流轨迹查询response
 *
 * <AUTHOR>
 * @Date 2023/5/30 15:17
 */
@Data
public class AySearchLogisticsTraceResponse {

    /**
     * 物流轨迹列表
     */
    private List<LogisticsInfoDTO> logisticsInfos;

    public void addLogisticsInfo(LogisticsInfoDTO logisticsInfo) {
        if (logisticsInfos == null) {
            logisticsInfos = new ArrayList<>();
        }
        logisticsInfos.add(logisticsInfo);
    }

}
