package cn.loveapp.logistics.common.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 物流公司转换response
 *
 * <AUTHOR>
 * @Date 2023/5/31 14:41
 */
@Data
public class LogisticsCompanyTransformResponse {

    /**
     * 物流公司平台
     */
    @JSONField(name = "store_id")
    private String logisticStoreId;

    /**
     * 物流公司code
     */
    @JSONField(name = "code")
    private String logisticsCompanyCode;

    /**
     * 物流公司name
     */
    @JSONField(name = "name")
    private String logisticsCompanyName;

    /**
     * 物流公司id
     */
    @JSONField(name = "company_id")
    private String logisticsCompanyId;


}
