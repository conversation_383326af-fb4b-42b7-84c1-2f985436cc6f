package cn.loveapp.logistics.common.entity;

import lombok.Data;


/**
 * 多店铺混单业务下绑定店铺信息实体
 *
 * <AUTHOR>
 * @date 2022/3/28$ 16:38$
 */
@Data
public class TargetSellerInfo {

    /**
     * 前端传递-目标店铺名称
     */
    private String targetNick;

    /**
     * 前端传递-目标店铺平台
     */
    private String targetStoreId;

    /**
     * 前端传递-目标店铺应用名称 （淘宝可以不用传）
     */
    private String targetAppName;

    /**
     * getUserInfo-查询出的商家sellerId
     */
    private String targetSellerId;

    /**
     * getUserInfo-查询出的商家CorpId
     */
    private String targetCorpId;

    /**
     * getUserInfo-查询出的商家vipFlag
     */
    private Integer targetVipFlag;

    /**
     * getAuthorization-获取道德商家topsession
     */
    private String targetTopSession;

    public TargetSellerInfo() {
    }

    public TargetSellerInfo(String targetNick, String targetStoreId, String targetAppName) {
        this.targetNick = targetNick;
        this.targetStoreId = targetStoreId;
        this.targetAppName = targetAppName;
    }

    public TargetSellerInfo(String targetNick, String targetStoreId) {
        this.targetNick = targetNick;
        this.targetStoreId = targetStoreId;
    }

    public TargetSellerInfo(String targetNick, String targetSellerId, String targetStoreId, String targetAppName, String targetTopSession) {
        this.targetNick = targetNick;
        this.targetSellerId = targetSellerId;
        this.targetStoreId = targetStoreId;
        this.targetAppName = targetAppName;
        this.targetTopSession = targetTopSession;
    }
}
