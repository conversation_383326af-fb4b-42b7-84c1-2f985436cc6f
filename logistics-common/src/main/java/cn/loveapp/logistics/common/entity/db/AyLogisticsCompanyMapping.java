package cn.loveapp.logistics.common.entity.db;

import lombok.Data;

/**
 * 物流公司映射实体
 *
 * <AUTHOR>
 * @Date 2023/8/10 14:09
 */
@Data
public class AyLogisticsCompanyMapping {

    private static final long serialVersionUID = 8211873353716939151L;

    /**
     * 待更新flag———1
     */
    public static final Integer FLAG_PREPARE = 1;

    /**
     * 手动处理flag
     */
    public static final Integer FLAG_MANUAL = 2;

    private Integer id;

    /**
     * 物流公司code
     */
    private String companyCode;

    /**
     * 物流公司名称
     */
    private String companyName;

    /**
     * 物流公司Id
     */
    private String companyId;

    /**
     * 物流平台
     */
    private String logisticsStoreId;

    /**
     * 关联爱用平台Id
     */
    private String ayLogisticsCompanyId;

    /**
     * 标识  1为待更新
     */
    private Integer flag;
}
