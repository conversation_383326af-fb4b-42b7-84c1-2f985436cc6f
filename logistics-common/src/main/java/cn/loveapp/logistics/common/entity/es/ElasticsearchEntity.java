package cn.loveapp.logistics.common.entity.es;

import java.util.Date;

/**
 * ES Entity
 *
 * <AUTHOR>
 * @date 2020/2/18
 */
public interface ElasticsearchEntity extends Cloneable{
	/**
	 * 获取id
	 * @return
	 */
	String getId();

    /**
     * 获取卖家id (routing)
     * @return
     */
    String getSellerId();


	/**
	 * 获取创建时间
	 * @return
	 */
	Date getGmtCreate();

	/**
	 * 设置创建时间
	 * @return
	 */
	void setGmtCreate(Date gmtCreate);

	/**
	 * 获取修改时间
	 * @return
	 */
	Date getGmtModified();

	/**
	 * 设置修改时间
	 * @param gmtModified
	 */
	void setGmtModified(Date gmtModified);

	/**
	 * 设置是否已经被删除
	 *
	 * @param isDeleted
	 */
	void setIsDeleted(Boolean isDeleted);

	/**
	 * 获取是否已经被删除
	 *
	 * @return
	 */
	Boolean getIsDeleted();

	/**
	 * 初始化默认值
	 */
	void initDefault();

	/**
	 * 初始化特殊值, 为null时不添加任何默认值, 只将不正确的值修改为正确的值
	 *
	 * 1. 额外附加信息, 不能从订单自有信息中获取的信息, 不能设置为false 只能设置为null
	 * 2. 不能为空值, 只能为null
	 * 3. 数字字母周围需要添加空格, 方便搜索
	 */
	void initSpecial();

	Object clone();
}
