package cn.loveapp.logistics.common.entity.es;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import cn.loveapp.logistics.common.utils.ElasticsearchUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 物流单ES索引实体
 *
 * <AUTHOR>
 * @Date 2023/6/19 11:12
 */
@Data
@Mapping
@Routing("sellerId")
@Document(indexName = LogisticsOrderInfoSearchES.INDEX_NAME_PREFIX, createIndex = false, writeTypeHint = WriteTypeHint.FALSE)
@NoArgsConstructor
public class LogisticsOrderInfoSearchES implements Serializable, ElasticsearchEntity {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderInfoSearchES.class);

    public static final String INDEX_NAME_PREFIX = "ay_logistics_order_info_search";
    public static final String GMT_FORMATER = "yyyy-MM-dd'T'HH:mm:ss.SSS";
    public static final String MINUTE_SECOND_FORMATER = "yyyy-MM-dd'T'HH:mm:ss";


    /**
     * 主键(StoreId+AppName+RefundId)
     */
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_millis)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = GMT_FORMATER)
    private Date gmtCreate;

    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_millis)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = GMT_FORMATER)
    private Date gmtModified;

    /**
     * 卖家nick
     */
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    /**
     * 卖家id
     */
    @Field(type = FieldType.Keyword)
    private String sellerId;

    /**
     * 商场类型 TAO JD。线上订单（TAO、JD...）线下订单 offline 售后订单 service
     */
    @Field(type = FieldType.Keyword)
    private String storeId;

    /**
     * 应用
     */
    @Field(type = FieldType.Keyword)
    private String appName;

    /**
     * 退款申请时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = MINUTE_SECOND_FORMATER)
    private Date created;

    /**
     * 退款更新时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = MINUTE_SECOND_FORMATER)
    private Date modified;

    /**
     * 订单编号
     */
    @Field(type = FieldType.Keyword)
    private String outSid;


    /**
     * 轨迹入库物流平台（接收轨迹消息平台不同则不进行入库，只做转发）
     */
    @Field(type = FieldType.Keyword)
    private String saveLogisticsStoreId;

    /**
     * 物流公司名称
     */
    @Field(type = FieldType.Keyword)
    private String companyCode;

    /**
     * 物流公司名称
     */
    @Field(type = FieldType.Text, analyzer = "single_analyzer")
    private String companyName;


    /**
     * 上次更新最后状态
     */
    @Field(type = FieldType.Keyword)
    private String lastAction;

    /**
     * 上次更新状态时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = MINUTE_SECOND_FORMATER)
    private Date lastActionModified;

    /**
     * 业务信息
     */
    @Field(type = FieldType.Object)
    private BusinessInfo businessInfo;

    /**
     * 轨迹订阅信息
     */
    @Deprecated
    @Field(type = FieldType.Object)
    private SubscribeInfo subscribeInfo;


    /**
     * 异常物流信息
     */
    @Field(type = FieldType.Object)
    private LogisticsAbnormalInfo logisticsAbnormalInfo;

    /**
     * 是否标记拦截
     */
    @Field(type = FieldType.Boolean)
    private Boolean isTagIntercepted;

    /**
     * 是否已经被删除
     */
    @Field(type = FieldType.Boolean)
    private Boolean isDeleted;

    /**
     * 生成物流单es索引
     *
     * @param newLogisticsOrderInfo
     * @return
     */
    public static LogisticsOrderInfoSearchES of(LogisticsOrderInfo newLogisticsOrderInfo) {
        LogisticsOrderInfoSearchES logisticsOrderInfoSearchES = new LogisticsOrderInfoSearchES();
        BeanUtils.copyProperties(newLogisticsOrderInfo, logisticsOrderInfoSearchES);

        logisticsOrderInfoSearchES.setSubscribeInfo(ConvertUtil.convert(newLogisticsOrderInfo.getSubscribeInfo(), SubscribeInfo.class));

        if (!Objects.isNull(newLogisticsOrderInfo.getBusinessInfo())) {
            LogisticsOrderInfo.BusinessInfo businessInfo = newLogisticsOrderInfo.getBusinessInfo();
            BusinessInfo businessInfoES = ConvertUtil.convert(businessInfo, BusinessInfo.class);
            businessInfoES.setTidList(ElasticsearchUtil.toList(businessInfo.getTidList()));
            businessInfoES.setRefundIdList(ElasticsearchUtil.toList(businessInfo.getRefundIdList()));

            HashSet<Integer> orderFlagSet = Sets.newHashSet();
            HashSet<Integer> orderAyCustomFlagSet = Sets.newHashSet();
            HashSet<String> orderMemoSet = Sets.newHashSet();
            HashSet<String> orderBuyerMessageSet = Sets.newHashSet();
            HashSet<String> orderOuterSkuIdSet = Sets.newHashSet();
            HashSet<String> orderSkuNameSet = Sets.newHashSet();
            LocalDateTime refundCreatedTime = null;
            boolean isRefund = false;
            List<LogisticsOrderInfo.OrderInfo> orderInfoList = businessInfo.getOrderInfoList();
            if (CollectionUtils.isNotEmpty(orderInfoList)) {
                for (LogisticsOrderInfo.OrderInfo orderInfo : orderInfoList) {
                    if (orderInfo.getSellerFlag() != null) {
                        orderFlagSet.add(orderInfo.getSellerFlag());
                    }

                    if (orderInfo.getOrderAyCustomFlag() != null) {
                        orderAyCustomFlagSet.add(orderInfo.getOrderAyCustomFlag());
                    }

                    // 为空说明备注备注为空补个空串防止不更新(因为是list 当存在合单时 一个有备注一个没有 如果用!=null 判定会出现list中一个空串元素和一个字符串元素，导致搜索出现问题)
                    if (StringUtils.isNotEmpty(orderInfo.getSellerMemo())) {
                        orderMemoSet.add(orderInfo.getSellerMemo());
                    }

                    // 非全量设置字段
                    if (StringUtils.isNotEmpty(orderInfo.getBuyerMessage())) {
                        orderBuyerMessageSet.add(orderInfo.getBuyerMessage());
                    }

                    refundCreatedTime = beforeTime(refundCreatedTime, orderInfo.getRefundCreatedTime());

                    if (BooleanUtils.isTrue(orderInfo.getIsRefund())) {
                        isRefund = true;
                    }

                    List<LogisticsOrderInfo.OrderSkuInfo> skuInfos = orderInfo.getSkuInfos();
                    if (CollectionUtils.isNotEmpty(skuInfos)) {
                        for (LogisticsOrderInfo.OrderSkuInfo skuInfo : skuInfos) {
                            orderSkuNameSet.add(skuInfo.getSkuName());
                            orderOuterSkuIdSet.add(skuInfo.getOuterSkuId());
                        }
                    }
                }
            }

            if (CollectionUtils.isEmpty(orderMemoSet)) {
                orderMemoSet.add(StringUtils.EMPTY);
            }

            if (CollectionUtils.isEmpty(orderBuyerMessageSet)) {
                orderBuyerMessageSet.add(StringUtils.EMPTY);
            }

            businessInfoES.setOrderSellerFlag(ElasticsearchUtil.toList(orderFlagSet));
            businessInfoES.setOrderAyCustomFlag(ElasticsearchUtil.toList(orderAyCustomFlagSet));
            businessInfoES.setOrderSellerMemo(ElasticsearchUtil.toList(orderMemoSet));
            businessInfoES.setOrderBuyerMessage(ElasticsearchUtil.toList(orderBuyerMessageSet));
            businessInfoES.setRefundCreatedTime(DateUtil.convertLocalDateTimetoDate(refundCreatedTime));
            businessInfoES.setIsRefund(isRefund);
            businessInfoES.setSkuNameList(ElasticsearchUtil.toList(orderSkuNameSet));
            businessInfoES.setOuterSkuIdList(ElasticsearchUtil.toList(orderOuterSkuIdSet));

            logisticsOrderInfoSearchES.setBusinessInfo(businessInfoES);
        }

        if (!Objects.isNull(newLogisticsOrderInfo.getLogisticsAbnormalInfo())) {
            LogisticsOrderInfo.LogisticsAbnormalInfo abnormalInfo = newLogisticsOrderInfo.getLogisticsAbnormalInfo();
            LogisticsAbnormalInfo abnormalInfoEs = ConvertUtil.convert(abnormalInfo, LogisticsAbnormalInfo.class);
            abnormalInfoEs.setDeliveryException(ConvertUtil.convert(abnormalInfo.getDeliveryException(), AbnormalDetails.class));
            abnormalInfoEs.setOtherAbnormal(ConvertUtil.convert(abnormalInfo.getOtherAbnormal(), AbnormalDetails.class));
            abnormalInfoEs.setRefusedAccept(ConvertUtil.convert(abnormalInfo.getRefusedAccept(), AbnormalDetails.class));
            abnormalInfoEs.setNotDeliveredOnTime(ConvertUtil.convert(abnormalInfo.getNotDeliveredOnTime(), AbnormalDetails.class));
            abnormalInfoEs.setNotPickedUpAfterSend(ConvertUtil.convert(abnormalInfo.getNotPickedUpAfterSend(), AbnormalDetails.class));
            abnormalInfoEs.setCollectionWillTimeout(ConvertUtil.convert(abnormalInfo.getCollectionWillTimeout(), AbnormalDetails.class));
            abnormalInfoEs.setNotReturnedOfInterception(ConvertUtil.convert(abnormalInfo.getNotReturnedOfInterception(), AbnormalDetails.class));
            abnormalInfoEs.setTrackingNotUpdatedAfterOfPickup(ConvertUtil.convert(abnormalInfo.getTrackingNotUpdatedAfterOfPickup(), AbnormalDetails.class));
            abnormalInfoEs.setDeliverySignTimeOut(ConvertUtil.convert(abnormalInfo.getDeliverySignTimeOut(), AbnormalDetails.class));
            abnormalInfoEs.setFirstTraceAfterPickedUpTimeout(ConvertUtil.convert(abnormalInfo.getFirstTraceAfterPickedUpTimeout(), AbnormalDetails.class));
            abnormalInfoEs.setFirstTraceAfterPickedUpWillTimeout(ConvertUtil.convert(abnormalInfo.getFirstTraceAfterPickedUpWillTimeout(), AbnormalDetails.class));
            abnormalInfoEs.setTransferTimeout(ConvertUtil.convert(abnormalInfo.getTransferTimeout(), AbnormalDetails.class));
            abnormalInfoEs.setOtherAbnormalOfTradeApp(ConvertUtil.convert(abnormalInfo.getOtherAbnormalOfTradeApp(), AbnormalDetails.class));
            abnormalInfoEs.setParcelAbnormalAndCustomKeyword(ConvertUtil.convert(abnormalInfo.getParcelAbnormalAndCustomKeyword(), AbnormalDetails.class));
            logisticsOrderInfoSearchES.setLogisticsAbnormalInfo(abnormalInfoEs);
        }

        return logisticsOrderInfoSearchES;
    }

    public static LogisticsOrderInfoSearchES of(String sellerId, String storeId, String appName) {
        LogisticsOrderInfoSearchES logisticsOrderInfoSearchES = new LogisticsOrderInfoSearchES();
        logisticsOrderInfoSearchES.setSellerId(sellerId);
        logisticsOrderInfoSearchES.setStoreId(storeId);
        logisticsOrderInfoSearchES.setAppName(appName);
        return logisticsOrderInfoSearchES;
    }


    /**
     * 业务信息
     */
    @Data
    public static class BusinessInfo {

        /**
         * 关联订单列表
         */
        @Field(type = FieldType.Keyword)
        private ArrayList<String> tidList;

        /**
         * 关联售后单列表
         */
        @Field(type = FieldType.Keyword)
        private ArrayList<String> refundIdList;

        /**
         * 发货时间
         */
        @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = MINUTE_SECOND_FORMATER)
        private Date consignTime;

        /**
         * 买家openUid
         */
        @Field(type = FieldType.Keyword)
        private String buyerOpenUid;

        /**
         * 买家openUid
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word"),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
        private String buyerNick;

        /**
         * 订单旗帜
         */
        @Field(type = FieldType.Keyword)
        private List<Integer> orderSellerFlag;

        /**
         * 订单自定义旗帜
         */
        @Field(type = FieldType.Keyword)
        private List<Integer> orderAyCustomFlag;

        /**
         * 订单备注
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word"),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
        private List<String> orderSellerMemo;

        /**
         * 订单备注
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word"),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
        private List<String> orderBuyerMessage;

        /**
         * 是否退款
         */
        @Field(type = FieldType.Keyword)
        private Boolean isRefund;

        /**
         * 退款创建时间
         */
        @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd'T'HH:mm:ss")
        private Date refundCreatedTime;

        /**
         * skuName列表
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word"),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
        private List<String> skuNameList;

        /**
         * sku外部编码列表
         */
        @MultiField(mainField = @Field(type = FieldType.Text, analyzer = "ik_max_word"),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)})
        private List<String> outerSkuIdList;
    }

    /**
     * 订阅信息
     */
    @Data
    public static class SubscribeInfo {

        /**
         * 订阅时间（最新一次订阅时间）
         */
        @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = MINUTE_SECOND_FORMATER)
        private Date lastModified;

        /**
         * 是否订阅成功
         */
        @Field(type = FieldType.Boolean)
        private Boolean isSuccess;
    }


    /**
     * 物流异常信息
     */
    @Data
    public static class LogisticsAbnormalInfo {

        /**
         * 发货后N小时未揽件
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails notPickedUpAfterSend;

        /**
         * 揽收将超时
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails collectionWillTimeout;

        /**
         * 标记拦截未截返
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails notReturnedOfInterception;

        /**
         * 揽收后签收前轨迹超N小时未更新
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails trackingNotUpdatedAfterOfPickup;

        /**
         * 派件异常
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails deliveryException;

        /**
         * 拒收
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails refusedAccept;

        /**
         * 超时未签收
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails notDeliveredOnTime;

        /**
         * 揽收后更新超时
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails firstTraceAfterPickedUpTimeout;

        /**
         * 揽收后将更新超时
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails firstTraceAfterPickedUpWillTimeout;

        /**
         * 中转超时
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails transferTimeout;

        /**
         * 派签超时
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails deliverySignTimeOut;


        /**
         * 其他异常
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails otherAbnormal;

        /**
         * 其他异常(交易)
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails otherAbnormalOfTradeApp;

        /**
         * 包裹异常和关键字异常
         */
        @Field(type = FieldType.Object)
        private AbnormalDetails parcelAbnormalAndCustomKeyword;

        /**
         * 处理状态
         */
        @Field(type = FieldType.Keyword)
        private String processStatus;

    }

    /**
     * 异常项实体
     */
    @Data
    public static class AbnormalDetails {
        /**
         * 是否存在异常
         */
        @Field(type = FieldType.Boolean)
        private Boolean isExists;

        /**
         * 处理状态
         */
        @Field(type = FieldType.Keyword)
        private String processStatus;

        /**
         * 异常终止时间
         */
        @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd'T'HH:mm:ss")
        private Date abnormalEndTime;
    }


    @Override
    public void initDefault() {
        // 自有属性, 设置默认值, 不能为null
        appName = StringUtils.trimToNull(appName);
//        isRefund = BooleanUtils.isTrue(isRefund);
        initSpecial();
    }

    @Override
    public void initSpecial() {
        if (businessInfo != null) {
            ElasticsearchUtil.splitAlphanumeric(businessInfo.orderSellerMemo);
            ElasticsearchUtil.splitAlphanumeric(businessInfo.outerSkuIdList);
            ElasticsearchUtil.splitAlphanumeric(businessInfo.skuNameList);
        }
    }

    @Override
    public Object clone() {
        LogisticsOrderInfoSearchES logisticsOrderInfoSearchES = null;
        try {
            logisticsOrderInfoSearchES = (LogisticsOrderInfoSearchES) super.clone();
        } catch (CloneNotSupportedException e) {
            LOGGER.logError(e.getMessage(), e);
        }
        return logisticsOrderInfoSearchES;
    }

    /**
     * 较旧的时间作为合单时间
     *
     * @param frist
     * @param second
     * @return
     */
    private static LocalDateTime beforeTime(LocalDateTime frist, LocalDateTime second) {
        if (frist == null) {
            return second;
        }
        if (second == null) {
            return frist;
        }
        if (frist.isBefore(second)) {
            return frist;
        } else {
            return second;
        }
    }
}
