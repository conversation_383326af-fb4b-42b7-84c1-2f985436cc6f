package cn.loveapp.logistics.common.entity.mongo;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.common.constant.AbnormalCheckSendStatus;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;

/**
 * logistics_order_info表实体
 *
 * <AUTHOR>
 * @create 2018-11-05 上午11:57
 */

@Data
@Document(collection = "logistics_order_info")
public class LogisticsOrderInfo {

    private static final long serialVersionUID = 973321844981334808L;

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderInfo.class);

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 卖家nick
     */
    private String sellerNick;

    /**
     * 平台id
     */
    private String storeId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 物流单号(分片键)
     */
    private String outSid;

    /**
     * 轨迹入库物流平台（接收轨迹消息平台不同则不进行入库，只做转发）
     */
    private String saveLogisticsStoreId;

    /**
     * 物流公司名称
     */
    private String companyCode;

    /**
     * 物流公司名称
     */
    private String companyName;

    /**
     * 改变时间
     */
    private Date modified;

    /**
     * 轨迹状态列表
     */
    private Set<String> logisticsStatusList;

    /**
     * 上次更新最后状态
     */
    private String lastAction;

    /**
     * 上次更新状态时间
     */
    private Date lastActionModified;

    /**
     * 上次更新轨迹描述
     */
    private String lastTraceDesc;

    /**
     * 业务信息
     */
    private BusinessInfo businessInfo;

    /**
     * 业务类型
     */
    private BusinessType businessType;

    /**
     * 轨迹订阅信息
     */
    private SubscribeInfo subscribeInfo;


    /**
     * 异常物流信息
     */
    private LogisticsAbnormalInfo logisticsAbnormalInfo;

    /**
     * 异常类型历史
     */
    private List<String> logisticsAbnormalTypeHistory;

    /**
     * 是否存在异常物流
     */
    private Boolean hasAbnormal;

    /**
     * 是否标记拦截
     */
    private Boolean isTagIntercepted;

    /**
     * 拦截标记时间
     */
    private Date tagInterceptedModified;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 当前发送中的异常物流校验队列的消息ID
     */
    private AbnormalCheckSendStatus abnormalCheckSendStatus;

    /**
     * 业务信息
     */
    @Data
    public static class BusinessInfo {

        /**
         * 关联订单列表
         */
        private Set<String> tidList;

        /**
         * 关联售后单列表
         */
        private Set<String> refundIdList;

        /**
         * 订单发货时间
         */
        private Date consignTime;

        /**
         * 买家昵称
         */
        private String buyerNick;

        /**
         * 买家OpenUid
         */
        private String buyerOpenUid;

        /**
         * 订单信息
         */
        private List<OrderInfo> orderInfoList;

        public void addTidList(List<String> tids) {
            if (tids == null || tids.size() == 0) {
                return;
            }
            if (tidList == null) {
                tidList = new HashSet<>();
            }
            tidList.addAll(tids);
        }

        public void addRefundIdList(List<String> refundIds) {
            if (refundIds == null || refundIds.size() == 0) {
                return;
            }
            if (refundIdList == null) {
                refundIdList = new HashSet<>();
            }
            refundIdList.addAll(refundIds);
        }

    }

    @Data
    public static class OrderInfo {

        /**
         * 订单号
         */
        private String tid;

        /**
         * 卖家备注旗帜
         */
        private Integer sellerFlag;

        /**
         * 卖家自定义旗帜
         */
        private Integer orderAyCustomFlag;

        /**
         * 卖家备注
         */
        private String sellerMemo;

        /**
         * 买家留言
         */
        private String buyerMessage;

        /**
         * 是否退款
         */
        private Boolean isRefund;

        /**
         * 退款创建时间
         */
        private LocalDateTime refundCreatedTime;

        /**
         * 商品信息
         */
        private List<OrderSkuInfo> skuInfos;

    }

    @Data
    public static class OrderSkuInfo {
        /**
         * skuId
         */
        private String skuId;

        /**
         * 商品名称
         */
        private String skuName;

        /**
         * 图片url
         */
        private String picUrl;

        /**
         * sku外部编码
         */
        private String outerSkuId;

        /**
         * sku数量
         */
        private Integer num;
    }

    /**
     * 订阅信息
     */
    @Data
    public static class SubscribeInfo {

        /**
         * 订阅时间（第一次订阅时间）
         */
        private Date firstModified;

        /**
         * 订阅时间（最新一次订阅时间）
         */
        private Date lastModified;

        /**
         * 是否订阅成功
         */
        private Boolean isSuccess;

        /**
         * 订阅失败原因
         */
        private String errorMsg;

    }


    /**
     * 物流异常信息
     */
    @Data
    public static class LogisticsAbnormalInfo {

        /**
         * 发货后N小时未揽件(揽收超时)
         */
        private AbnormalDetails notPickedUpAfterSend;

        /**
         * 揽收将超时
         */
        private AbnormalDetails collectionWillTimeout;

        /**
         * 标记拦截未截返
         */
        private AbnormalDetails notReturnedOfInterception;

        /**
         * 揽收后签收前轨迹超N小时未更新
         */
        private AbnormalDetails trackingNotUpdatedAfterOfPickup;

        /**
         * 派件异常
         */
        private AbnormalDetails deliveryException;

        /**
         * 拒收
         */
        private AbnormalDetails refusedAccept;

        /**
         * 超时未签收
         */
        private AbnormalDetails notDeliveredOnTime;

        /**
         * 揽收后更新超时
         */
        private AbnormalDetails firstTraceAfterPickedUpTimeout;

        /**
         * 揽收后将更新超时
         */
        private AbnormalDetails firstTraceAfterPickedUpWillTimeout;

        /**
         * 中转超时
         */
        private AbnormalDetails transferTimeout;

        /**
         * 派签超时
         */
        private AbnormalDetails deliverySignTimeOut;

        /**
         * 其他异常
         */
        private AbnormalDetails otherAbnormal;

        /**
         * 其他异常(交易)
         */
        private AbnormalDetails otherAbnormalOfTradeApp;

        /**
         * 包裹异常和关键字异常
         */
        private AbnormalDetails parcelAbnormalAndCustomKeyword;

        /**
         * 处理状态
         */
        private String processStatus;

    }

    /**
     * 异常项实体
     */
    @Data
    public static class AbnormalDetails {
        /**
         * 是否存在异常
         */
        private Boolean isExists;

        /**
         * 处理状态
         */
        private String processStatus;

        /**
         * 异常判断最终时间
         */
        private Date abnormalEndTime;
    }

    public String getId() {
        if (this.id == null && outSid != null && sellerId != null && !StringUtils.isAllEmpty(companyCode, companyName)) {
            String company = null;
            if (companyCode == null) {
                company = String.valueOf(Math.abs(companyName.hashCode()));
            } else {
                company = companyCode;
            }
            return sellerId + StringUtils.trimToNull(appName) + outSid + company;
        }
        return id;
    }


    /**
     * 生成异常项
     * @param hasAbnormal
     * @param abnormalDetails
     * @return
     */
    public static boolean generalAbnormalDetails(boolean hasAbnormal, @NotNull AbnormalDetails abnormalDetails,
        LocalDateTime abnormalEndTime) {
        boolean isUpdate = false;
        Boolean isExists = abnormalDetails.getIsExists();
        if (!Objects.isNull(isExists) && Objects.equals(isExists, hasAbnormal)) {
            // 异常项未变更
            return isUpdate;
        }

        abnormalDetails.setIsExists(hasAbnormal);
        if (abnormalEndTime != null) {
            abnormalDetails.setAbnormalEndTime(DateUtil.convertLocalDateTimetoDate(abnormalEndTime));
        }
        return true;
    }

    /**
     * 更新汇总物流异常信息
     *
     * @param hasAbnormal
     * @param isUpdate
     */
    public void checkAndSetMainAbnormalInfo(boolean hasAbnormal, boolean isUpdate) {
        // 存在一个异常项，则该物流单存在异常
        if (hasAbnormal) {
            this.hasAbnormal = true;
            // 存在一个新的变更的异常项, 总的处理状态更新为待处理
            if (isUpdate && this.logisticsAbnormalInfo != null) {
                this.logisticsAbnormalInfo.setProcessStatus(AbnormalProcessStatus.PENDING.value());
            }
        }
    }


    /**
     * 生成异常项映射
     * @return
     */
    public Map<String, AbnormalDetails> convertToAbnormalMap(Boolean includeProcessedAbnormal) {
        if (this.logisticsAbnormalInfo == null) {
            return null;
        }
        Map<String, AbnormalDetails> map = new HashMap<>();

        Field[] fields = LogisticsAbnormalInfo.class.getDeclaredFields();
        for (Field field : fields) {
            if ("processStatus".equals(field.getName())) {
                // 忽略processStatus属性
                continue;
            }
            field.setAccessible(true);
            try {
                Object value = field.get(this.logisticsAbnormalInfo);
                if (value instanceof AbnormalDetails) {
                    AbnormalDetails details = (AbnormalDetails) value;
                    if (!BooleanUtils.isTrue(details.getIsExists())
                        || (AbnormalProcessStatus.PROCESSED.value().equals(details.getProcessStatus()) && BooleanUtils.isNotTrue(includeProcessedAbnormal))) {                        // 不是异常或已处理跳过
                        continue;
                    }
                    map.put(field.getName(), details);
                }
            } catch (IllegalAccessException e) {
                LOGGER.logError("转换异常列表失败" + e.getMessage(), e);
            }
        }

        return map;
    }

    public void setLogisticsStatusList(List<String> statusList) {
        if (CollectionUtils.isEmpty(statusList)) {
            return;
        }

        if (this.logisticsStatusList == null) {
            logisticsStatusList = new HashSet<>();
        }
        statusList.stream().filter(Objects::nonNull).forEach(status -> logisticsStatusList.add(status));
    }

    public void setLogisticsStatusList(String status) {
        if (StringUtils.isEmpty(status)) {
            return;
        }

        if (this.logisticsStatusList == null) {
            logisticsStatusList = new HashSet<>();
        }

        logisticsStatusList.add(status);
    }
}
