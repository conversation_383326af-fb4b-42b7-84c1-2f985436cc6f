package cn.loveapp.logistics.common.entity.mongo;

import java.util.Date;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.orders.dto.proto.LogisticsTraceRequest;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Data;

/**
 * logistics_trace_info表实体
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-11-05 上午11:57
 */

@Data
@Document(collection = "logistics_trace_info")
public class LogisticsTraceInfo {
    private static final long serialVersionUID = 973321844981334807L;

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 运单号/订单号
     * TAO的物流轨迹存的订单号，其他平台存的运单号
     */
    private String tid;

    /**
     * 物流单号
     */
    private String outSid;

    /**
     * 物流公司名称
     */
    private String companyCode;

    /**
     * 物流公司名称
     */
    private String companyName;

    /**
     * 物流状态
     */
    private String action;

    /**
     * 物流状态改变时间
     */
    private Date modified;

    /**
     * 物流中转描述
     */
    private String desc;

    /**
     * 平台id
     */
    private String platformId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 物流状态
     */
    private String status;

    /**
     * 物流轨迹来源（KDNIAO、PDD、TAO）
     */
    private String logisticsStoreId;



    private Date gmtCreate;

    public LogisticsTraceInfo() {}

    public LogisticsTraceInfo(String tid, String sellerId, String outSid, String companyName, String action,
        String desc, Date modified, String appName, String platformId) {
        this.tid = tid;
        this.sellerId = sellerId;
        this.outSid = outSid;
        this.companyName = companyName;
        this.action = action;
        this.desc = desc;
        this.modified = modified;
        this.appName = appName;
        this.platformId = platformId;
    }

    public String getActionAndDescAndModified() {
        return this.getOutSid() + "-" + this.getStatus() + "-" + this.getDesc() + "-" + this.getModified();
    }

    public String getActionAndDescAndModified(boolean isDistinctWithStatusField) {
        String uniqueKey = this.getOutSid()  + "-" + this.getDesc() + "-" + this.getModified();
        if (isDistinctWithStatusField) {
            uniqueKey += "-" + this.getStatus();
        }
        return uniqueKey;
    }

    public LogisticsTraceInfo(String tid) {
        this.tid = tid;
    }

    /**
     * 淘宝、PDD(旧)
     * @param logisticsInfoDTO
     * @return
     */
    public static LogisticsTraceInfo fromLogisticsInfoDto(LogisticsInfoDTO logisticsInfoDTO) {
        if (logisticsInfoDTO == null) {
            return null;
        }
        LogisticsTraceInfo info = new LogisticsTraceInfo();
        info.setTid(logisticsInfoDTO.getTid());
        info.setOutSid(logisticsInfoDTO.getOutSid());
        info.setSellerId(logisticsInfoDTO.getSellerId());
        info.setAction(logisticsInfoDTO.getAction());
        info.setCompanyName(logisticsInfoDTO.getCompanyName());
        info.setDesc(logisticsInfoDTO.getDesc());
        info.setModified(logisticsInfoDTO.getModified());
        info.setPlatformId(logisticsInfoDTO.getPlatformId());
        info.setAppName(logisticsInfoDTO.getAppName());
        info.setLogisticsStoreId(logisticsInfoDTO.getPlatformId());
        info.setStatus(logisticsInfoDTO.getStatus());
        return info;
    }


    /**
     * 物流多平台
     * @param logisticsInfoDTO
     * @return
     */
    public static LogisticsTraceInfo fromLogisticsInfoDtoMulti(LogisticsInfoDTO logisticsInfoDTO) {
        if (logisticsInfoDTO == null) {
            return null;
        }
        LogisticsTraceInfo info = new LogisticsTraceInfo();
        info.setTid(logisticsInfoDTO.getOutSid());
        info.setOutSid(logisticsInfoDTO.getOutSid());
        info.setSellerId(logisticsInfoDTO.getSellerId());
        info.setAction(logisticsInfoDTO.getAction());
        info.setCompanyCode(logisticsInfoDTO.getCompanyCode());
        info.setCompanyName(logisticsInfoDTO.getCompanyName());
        info.setDesc(logisticsInfoDTO.getDesc());
        info.setModified(logisticsInfoDTO.getModified());
        info.setPlatformId(logisticsInfoDTO.getPlatformId());
        info.setAppName(logisticsInfoDTO.getAppName());
        info.setStatus(logisticsInfoDTO.getStatus());
        info.setLogisticsStoreId(logisticsInfoDTO.getLogisticsStoreId());
        return info;
    }

    public static LogisticsTraceRequest toLogisticsTraceRequest(LogisticsTraceInfo logisticsTraceInfo) {
        if(logisticsTraceInfo == null) {
            return null;
        }
        LogisticsTraceRequest logisticsTraceRequest = new LogisticsTraceRequest();
        logisticsTraceRequest.setSellerId(logisticsTraceInfo.getSellerId());
        logisticsTraceRequest.setPlatformId(logisticsTraceInfo.getPlatformId());
        logisticsTraceRequest.setAppName(logisticsTraceInfo.getAppName());
        logisticsTraceRequest.setLogisticsCompanyCode(logisticsTraceInfo.getCompanyCode());
        logisticsTraceRequest.setOutSid(logisticsTraceInfo.getOutSid());
        logisticsTraceRequest.setCompanyName(logisticsTraceInfo.getCompanyName());
        // modified 对应 json 的 time 字段
        logisticsTraceRequest.setTime(DateUtil.parseDate(logisticsTraceInfo.getModified()));
        logisticsTraceRequest.setAction(logisticsTraceInfo.getAction());
        logisticsTraceRequest.setTid(logisticsTraceInfo.getTid());
        logisticsTraceRequest.setLogisticsStoreId(logisticsTraceInfo.getLogisticsStoreId());
        logisticsTraceRequest.setDesc(logisticsTraceInfo.getDesc());
        logisticsTraceRequest.setStatus(logisticsTraceInfo.getStatus());
        return logisticsTraceRequest;
    }
}
