package cn.loveapp.logistics.common.service;

import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.common.constant.LogisticsPackType;

/**
 * <AUTHOR>
 * @date 2024-10-29 10:37
 * @description: 爱用物流额度服务
 */
public interface AyLogisticsQuotaService {

    /**
     * 扣除物流额度
     *
     * @param logisticsHandle
     * @param isNeedDeductionQuota
     * @return
     */
    Integer withholdingLogisticQuota(LogisticsOrderSubscribeDTO logisticsHandle, boolean isNeedDeductionQuota);

    /**
     * 物流额度消费确认/回滚
     *
     * @param logisticsOrderSubscribeDTO
     * @param isSearchSuccess
     * @param isNeedDeductionQuota
     * @param consumeNum
     * @param packType
     * @param monitorType
     */
    void logisticQuotaConsumeConfirm(LogisticsOrderSubscribeDTO logisticsOrderSubscribeDTO, boolean isSearchSuccess,
        boolean isNeedDeductionQuota, Integer consumeNum, LogisticsPackType packType, String monitorType);
}
