package cn.loveapp.logistics.common.service;

import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;

import java.util.List;

/**
 * 物流异常相关操作处理service
 *
 * <AUTHOR>
 * @Date 2023/6/26 15:27
 */
public interface LogisticsAbnormalHandleService {

    /**
     * 校验是否存在物流异常
     *
     * @param logisticsHandleBo
     * @param checkAbnormal     是否自动队列消息
     * @return
     */
    boolean checkAbnormalLogistics(LogisticsHandleBo logisticsHandleBo, boolean checkAbnormal);


    /**
     * 发送异常物流通知
     * @param logisticsHandleBo
     * @param abnormalTypeNotifyList
     */
    void sendAbnormalLogisticsNotify(LogisticsHandleBo logisticsHandleBo, List<String> abnormalTypeNotifyList);


    /**
     * 异常物流统计查询
     *
     * @param abnormalQueryDTO
     * @return
     * @throws LogisticsHandlesException
     */
    LogisticsAbnormalCountDTO abnormalStatisticsGet(AbnormalQueryDTO abnormalQueryDTO, UserInfoDTO userInfoDTO) throws LogisticsHandlesException;


    /**
     * 异常物流信息入库
     *
     * @param logisticsHandleBo
     * @return
     */
    boolean pullLogisticsAbnormalData(LogisticsHandleBo logisticsHandleBo) throws LogisticsHandlesException;


    /**
     * 设置处理状态
     * @param logisticsOrderInfo
     * @param newProcessStatus
     */
    void setLogisticsProcessStatus(LogisticsOrderInfo logisticsOrderInfo, String newProcessStatus);


}
