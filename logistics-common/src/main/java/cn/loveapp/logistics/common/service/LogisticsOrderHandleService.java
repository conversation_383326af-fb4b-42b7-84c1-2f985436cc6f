package cn.loveapp.logistics.common.service;

import java.util.List;

import cn.loveapp.logistics.api.dto.LogisticsOrderChangeDTO;
import cn.loveapp.logistics.api.dto.OrderInfoChangeDTO;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.dto.*;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;

/**
 * 物流单相关操作处理service
 *
 * <AUTHOR>
 * @Date 2023/5/30 16:25
 */
public interface LogisticsOrderHandleService {

    /**
     * 物流公司转换
     *
     * @param sourceLogisticsCompanyInfo
     * @param targetLogisticsStoreId
     * @return
     */
    LogisticsCompanyInfoDTO logisticsCompanyTransform(LogisticsCompanyInfoDTO sourceLogisticsCompanyInfo, String targetLogisticsStoreId) throws LogisticsHandlesException;

    /**
     * 物流单信息入库
     *
     * @param logisticsHandleBo
     */
    void pullLogisticsData(LogisticsHandleBo logisticsHandleBo) throws LogisticsHandlesException;

    /**
     * 判断物流是否已经结束
     *
     * @param logisticsOrderInfo
     * @return
     */
    boolean checkLogisticsIsEnd(LogisticsOrderInfo logisticsOrderInfo);

    /**
     * 物流单变更
     *
     * @param logisticsUpdate
     * @return
     * @throws LogisticsHandlesException
     */
    boolean logisticsInfoUpdate(LogisticsOrderChangeDTO logisticsUpdate) throws LogisticsHandlesException;

    /**
     * 通过订单信息更新物流单信息
     *
     * @param logisticsOrderChangeInfos
     * @return
     */
    boolean logisticsInfoUpdateByOrderInfo(OrderInfoChangeDTO logisticsOrderChangeInfos) throws LogisticsHandlesException;

    /**
     * 获取物流单搜索列表
     * @param logisticsQuery
     * @param userInfoDTO
     * @return
     * @throws LogisticsHandlesException
     */
    AyLogisticsOrderInfoSearchListAndAggDto logisticsOrderInfoListQueryByLimit(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO) throws LogisticsHandlesException;


    /**
     * 获取物流单列表
     *
     * @param logisticsQuery
     * @param outSidList
     * @param userInfoDTO
     * @param isSearchTrace
     * @return
     */
    List<LogisticsPackInfo> logisticsPackListGetByOutSidList(LogisticsQueryDTO logisticsQuery, List<String> outSidList, UserInfoDTO userInfoDTO, Boolean isSearchTrace) throws LogisticsHandlesException;

    /**
     * 校验物流单是否长时间未更新,调用api获取全量物流轨迹,并推送物流轨迹消息补齐物流轨迹
     *
     * @param logisticsHandleBo
     * @return 返回物流单是否长时间未更新
     */
    boolean checkOverdueUpdateAndPushTraceMessage(LogisticsHandleBo logisticsHandleBo);

    /**
     * 处理物流公司映射
     *
     * @param companyCpCode
     * @param companyName
     * @param logisticsStoreId
     * @param targetLogisticsStoreId
     */
    LogisticsCompanyMappingDTO handelLogisticsCompanyMapping(String companyCpCode, String companyName,
        String logisticsStoreId, String targetLogisticsStoreId);
}
