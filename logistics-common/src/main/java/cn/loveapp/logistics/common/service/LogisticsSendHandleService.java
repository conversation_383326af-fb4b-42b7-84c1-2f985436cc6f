package cn.loveapp.logistics.common.service;

import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流相关推送接口
 *
 * <AUTHOR>
 * @Date 2023/6/6 17:56
 */
public interface LogisticsSendHandleService {


    /**
     * 转发物流消息到灰度队列
     *
     * @param oMsg           源消息
     * @param logisticsInfos
     * @param sourceTopic    源队列
     * @return
     */
    boolean pushLogisticsPretest(LogisticsTraceRequestProto oMsg, List<LogisticsInfoDTO> logisticsInfos, String sourceTopic);


    /**
     * 发送物流轨迹入库消息
     * @param logisticsInfos
     * @param checkModified
     * @param logisticsStoreId
     * @param logisticsAppName
     */
    void pushLogisticsSaveMsg(List<LogisticsInfoDTO> logisticsInfos, boolean checkModified, String logisticsStoreId, String logisticsAppName);

    /**
     * 发送物流轨迹至订单
     *
     * @param newLogisticsOrderInfo
     * @param logisticsTraceInfo
     */
    void pushLogisticsToMcRouter(LogisticsOrderInfo newLogisticsOrderInfo, LogisticsTraceInfo logisticsTraceInfo);

    /**
     * 发送异常物流通知队列
     * @param newLogisticsOrderInfo
     */
    boolean pushLogisticsAbnormalNotifyMsg(LogisticsOrderInfo newLogisticsOrderInfo, List<String> abnormalTypeNotifyList);

    /**
     * 发送异常物流校验队列
     * @param newLogisticsOrderInfo
     */
    boolean pushLogisticsAbnormalResendMsg(LogisticsOrderInfo newLogisticsOrderInfo, LocalDateTime deadLine);

    /**
     * 发送自定义请求消息
     */
    void pushCustomNotifyMsg(String consumerUrl, Object data);

}
