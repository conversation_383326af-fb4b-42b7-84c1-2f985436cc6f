package cn.loveapp.logistics.common.service;

import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.dto.OrderInfoDTO;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.dto.LogisticsDetailDTO;
import cn.loveapp.logistics.common.dto.LogisticsInfoApiSearchDTO;
import cn.loveapp.logistics.common.dto.PrepareConsignDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;

import java.util.List;

/**
 * 物流轨迹相关操作处理service
 *
 * <AUTHOR>
 * @Date 2023/6/26 15:26
 */
public interface LogisticsTraceHandleService {

    /**
     * 物流轨迹入库
     *
     * @param logisticsHandleBo
     * @return
     * @throws LogisticsHandlesException
     */
    boolean pullLogisticsTraceData(LogisticsHandleBo logisticsHandleBo) throws LogisticsHandlesException;

    /**
     * 物流轨迹订阅
     *
     * @param logisticsHandle
     * @throws LogisticsHandlesException
     */
    boolean subscribeLogisticsTrace(LogisticsOrderSubscribeDTO logisticsHandle) throws LogisticsHandlesException;

    /**
     * 物流轨迹订阅（附带订单信息，物流单存储部分订单信息使用）
     *
     * @param logisticsHandle
     * @param orderInfoDTOList
     * @return
     * @throws LogisticsHandlesException
     */
    boolean subscribeLogisticsTrace(LogisticsOrderSubscribeDTO logisticsHandle, List<OrderInfoDTO> orderInfoDTOList) throws LogisticsHandlesException;

    /**
     * 物流轨迹查询（api）
     *
     * @param apiSearchList
     * @param prepareConsignDTO
     * @throws LogisticsHandlesException
     */
    List<LogisticsDetailDTO> searchLogisticsTraceFromApi(List<LogisticsInfoApiSearchDTO> apiSearchList, UserInfoDTO userInfoDTO, PrepareConsignDTO prepareConsignDTO) throws LogisticsHandlesException;


    /**
     * 通过物流单获取物流轨迹
     *
     * @param logisticsHandleBo
     * @return
     */
    List<LogisticsTraceInfo> queryLogisticsTraceList(LogisticsOrderInfo logisticsHandleBo);

}
