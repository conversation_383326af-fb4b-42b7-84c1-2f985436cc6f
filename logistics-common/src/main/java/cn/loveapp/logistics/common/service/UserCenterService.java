package cn.loveapp.logistics.common.service;

import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.uac.request.UserInfoRequest;

import java.util.List;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 * @Date 2023/8/15 15:47
 */
public interface UserCenterService {

    /**
     * 校验物流监控开关
     * @return
     */
    boolean checkLogisticsMonitorEnable(BusinessType type, UserInfoRequest userInfoRequest,
        List<String> extraSettingCheckList, String logisticsAppName, String logisticsStoreId);

    /**
     * 判断是否异常打标用户
     *
     * @param sellerId
     * @param sellerNick
     * @param appName
     * @param storeId
     * @return
     */
    boolean isAbnormalAutoCheckUser(String sellerId, String sellerNick, String appName, String storeId);

}
