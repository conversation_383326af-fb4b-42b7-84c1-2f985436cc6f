package cn.loveapp.logistics.common.service;

import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.uac.domain.UserSettingDTO;
import cn.loveapp.uac.request.BatchSettingGetRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserCacheInfoResponse;
import cn.loveapp.uac.response.UserInfoResponse;

import java.util.List;

/**
 * 拼多多的物流信息补充 通过uac获取userNick
 *
 * <AUTHOR>
 * @Since 2020/6/23 21:41
 */
public interface UserInfoService {

    /**
     * 从缓存中 获取 sellerNick
     *
     * @param userInfoRequest
     *            包含 appname:guanDian platform:PDD tid:xxxx
     * @return sellerNick
     */
    UserInfoResponse getSellerInfo(UserInfoRequest userInfoRequest);


    /**
     * 获取多店tag
     * @param userInfoRequest
     * @return
     */
    UserCacheInfoResponse getShopTags(UserInfoRequest userInfoRequest);

    /**
     * 获取用户信息
     *
     * @param targetSellerInfoList
     * @return
     */
    List<UserInfoResponse> getUserInfo(List<TargetSellerInfo> targetSellerInfoList);


    /**
     * 获取用户配置信息
     * @param request
     * @return
     */
    List<UserSettingDTO> batchSettingGet(BatchSettingGetRequest request);

    /**
     * 获取用户的 topSession
     * @param sellerNick
     * @param sellerId
     * @param platformId
     * @param appName
     * @return
     */
    String getTopSession(String sellerNick, String sellerId, String platformId, String appName);
}
