package cn.loveapp.logistics.common.service.abnormalstrategy;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 策略执行结果
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExecuteResult {
    /**
     * 是否有变化
     */
    private boolean isChanged;

    /**
     * 最晚校验时间
     */
    private LocalDateTime deadline;
}
