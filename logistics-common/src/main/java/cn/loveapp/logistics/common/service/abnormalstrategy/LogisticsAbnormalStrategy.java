package cn.loveapp.logistics.common.service.abnormalstrategy;

import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 异常物流校验策略接口
 *
 * <AUTHOR>
 * @Date 2023/6/21 10:37
 */
public interface LogisticsAbnormalStrategy {


    /**
     * 返回异常类型
     *
     * @return
     */
    String getAbnormalType();


    /**
     * 执行异常物流校验
     *
     * @param logisticsOrderInfo
     *
     * @return change 返回是否变更
     */
    ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings);


    /**
     * 计算并设置异常各指标计数
     * @param abnormalCountDTO
     * @param abnormalCount
     */
    void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount);

    /**
     * 生成异常ES查询语法
     * @return BoolQuery.Builder
     */
    BoolQuery.Builder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO);

    /**
     * 设置异常项处理状态
     *
     * @param logisticsAbnormalInfo
     * @param newProcessStatus
     * @param appName
     * @return change 返回是否变更
     */
    boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo, String newProcessStatus, String appName);


    /**
     * 判断是否异常
     * @param logisticsOrderInfo
     * @return
     */
    boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo);

    /**
     * 根据配置判断是否需要弹窗
     * @param userSettings
     * @return
     */
    boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings);

    /**
     * 追加异常历史
     *
     * @param logisticsOrderInfo
     * @param abnormalType
     */
    default void appendLogisticsAbnormalTypeHistory(LogisticsOrderInfo logisticsOrderInfo, String abnormalType) {
        if (StringUtils.isNotEmpty(abnormalType)) {
            List<String> logisticsAbnormalTypeHistory = logisticsOrderInfo.getLogisticsAbnormalTypeHistory();
            if (CollectionUtils.isEmpty(logisticsAbnormalTypeHistory)) {
                logisticsAbnormalTypeHistory = Lists.newArrayList();
                logisticsAbnormalTypeHistory.add(abnormalType);
            } else {
                if (!logisticsAbnormalTypeHistory.contains(abnormalType)) {
                    logisticsAbnormalTypeHistory.add(abnormalType);
                }
            }

            logisticsOrderInfo.setLogisticsAbnormalTypeHistory(logisticsAbnormalTypeHistory);
        }
    };

}
