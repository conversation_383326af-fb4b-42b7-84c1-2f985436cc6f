package cn.loveapp.logistics.common.service.abnormalstrategy;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dao.es.LogisticsOrderInfoSearchEsDao;
import cn.loveapp.logistics.common.dao.redis.LogisticsAbnormalRedisDao;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.UserInfoService;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import cn.loveapp.uac.domain.UserSettingDTO;
import cn.loveapp.uac.request.BatchSettingGetRequest;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.loveapp.common.es.EsQueryBuilders.boolQuery;

/**
 * 异常物流判断策略链
 *
 * <AUTHOR>
 * @Date 2023/6/21 11:02
 */
@Service
public class LogisticsAbnormalStrategyChain {

    @Autowired
    private List<LogisticsAbnormalStrategy> strategyList;

    @Autowired
    private LogisticsAbnormalRedisDao logisticsAbnormalRedisDao;

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 执行异常物流判断链，更新物流单
     *
     * @param logisticsOrderInfo  物流单
     * @param abnormalTypeNotifyList
     * @return isUpdate 是否存在异常信息更新
     */
    @NotNull
    public ExecuteResult executeChain(LogisticsOrderInfo logisticsOrderInfo, List<String> abnormalTypeNotifyList) {
        if (logisticsOrderInfo == null) {
            return new ExecuteResult();
        }
        // 是否变更
        boolean isUpdate = false;
        int delayLevel = 15;

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        if (logisticsAbnormalInfo == null) {
            // 初始化
            logisticsAbnormalInfo = new LogisticsOrderInfo.LogisticsAbnormalInfo();
            logisticsOrderInfo.setLogisticsAbnormalInfo(logisticsAbnormalInfo);
        }
        if (abnormalTypeNotifyList == null) {
            abnormalTypeNotifyList = new ArrayList<>();
        }

        // 统计前清空重新计算
        logisticsOrderInfo.setHasAbnormal(null);

        Map<String, String> userSetting = getSettings(logisticsOrderInfo);

        LocalDateTime deadline = null;
        for (LogisticsAbnormalStrategy strategy : strategyList) {
            ExecuteResult result = strategy.execute(logisticsOrderInfo, userSetting);
            // 取最近的判断时间
            deadline = DateUtil.earlierTime(deadline, result.getDeadline());
            if (result.isChanged()) {
                if (strategy.checkHasAbnormal(logisticsOrderInfo)) {
                    // 存在变更且发生异常
                    abnormalTypeNotifyList.add(strategy.getAbnormalType());
                }
                // 异常信息更新，清除对应项的缓存
                String abnormalType = strategy.getAbnormalType();
                String sellerId = logisticsOrderInfo.getSellerId();
                String storeId = logisticsOrderInfo.getStoreId();
                String appName = logisticsOrderInfo.getAppName();
                logisticsAbnormalRedisDao.clear(abnormalType, storeId, appName, sellerId);
            }
            isUpdate |= result.isChanged();
        }
        return new ExecuteResult(isUpdate, deadline);
    }

    /**
     * 清除异常计数缓存
     * @param logisticsOrderInfo
     */
    public void clearCountCache(LogisticsOrderInfo logisticsOrderInfo) {
        for (LogisticsAbnormalStrategy strategy : strategyList) {
            // 清除异常计数缓存
            String abnormalType = strategy.getAbnormalType();
            String sellerId = logisticsOrderInfo.getSellerId();
            String storeId = logisticsOrderInfo.getStoreId();
            String appName = logisticsOrderInfo.getAppName();
            logisticsAbnormalRedisDao.clear(abnormalType, storeId, appName, sellerId);
        }
    }

    /**
     * 异常物流统计链
     *
     * @param sellerInfos
     * @param abnormalQueryDTO
     * @param logisticsOrderInfoSearchEsDao
     * @return
     */
    public LogisticsAbnormalCountDTO countAbnormalChain(List<TargetSellerInfo> sellerInfos, AbnormalQueryDTO abnormalQueryDTO, LogisticsOrderInfoSearchEsDao logisticsOrderInfoSearchEsDao) {
        List<String> fields = abnormalQueryDTO.getAbnormalTypes();
        if (CollectionUtils.isEmpty(sellerInfos) || CollectionUtils.isEmpty(fields)) {
            return new LogisticsAbnormalCountDTO();
        }

        LogisticsAbnormalCountDTO abnormalCountDTO = new LogisticsAbnormalCountDTO();
        for (LogisticsAbnormalStrategy strategy : strategyList) {
            if (!fields.contains(strategy.getAbnormalType())) {
                // 未指定该指标查询，跳过
                continue;
            }
            sellerInfos.forEach(sellerInfo -> {
                String targetSellerId = sellerInfo.getTargetSellerId();
                String targetAppName = sellerInfo.getTargetAppName();
                String targetStoreId = sellerInfo.getTargetStoreId();
                LogisticsOrderInfoSearchES searchEs = LogisticsOrderInfoSearchES.of(targetSellerId, targetStoreId, targetAppName);

                // 计数只统计待处理的异常
                abnormalQueryDTO.setProcessStatus(AbnormalProcessStatus.PENDING);

                String count = logisticsAbnormalRedisDao.getCache(() -> String.valueOf(logisticsOrderInfoSearchEsDao.countByQuery(searchEs, strategy.generalAbnormalBoolQuery(abnormalQueryDTO))),
                    strategy.getAbnormalType(), targetStoreId, targetAppName, targetSellerId);
                if (StringUtils.isEmpty(count)) {
                    return;
                }
                strategy.calculateAndSetAbnormalCount(abnormalCountDTO, Integer.parseInt(count));
            });
        }
        return abnormalCountDTO;
    }

    /**
     * 根据搜索类型生成ES异常搜索条件
     * @param fields
     * @return
     */
    public BoolQuery.Builder generalAbnormalBoolQueryChain(List<String> fields, AbnormalQueryDTO abnormalQueryDTO) {
        // 是否指定类型查询
        boolean isSpecifiedType = false;
        if (CollectionUtils.isNotEmpty(fields) && StringUtils.isNotEmpty(fields.get(0))) {
            isSpecifiedType = true;
        }

        Boolean onlySearchAbnormal = abnormalQueryDTO.getOnlySearchAbnormal();
        BoolQuery.Builder boolQueryBuilder = boolQuery();
        for (LogisticsAbnormalStrategy strategy : strategyList) {
            if (isSpecifiedType && !fields.contains(strategy.getAbnormalType())) {
                // 非指定该指标查询，跳过
                continue;
            } else if (!isSpecifiedType && BooleanUtils.isFalse(onlySearchAbnormal)) {
                // 未指定且查询全部（包含非异常）
                continue;
            }
            boolQueryBuilder.should(strategy.generalAbnormalBoolQuery(abnormalQueryDTO).build());
        }
        return boolQueryBuilder;
    }

    /**
     * 设置处理异常处理状态
     * @param logisticsOrderInfo
     * @param newProcessStatus
     */
    public void setLogisticsProcessStatus(LogisticsOrderInfo logisticsOrderInfo, String newProcessStatus) {
        if (Objects.isNull(logisticsOrderInfo) || Objects.isNull(logisticsOrderInfo.getLogisticsAbnormalInfo()) || StringUtils.isEmpty(newProcessStatus)) {
            // 不存在异常无需设置
            return;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        if (!LogisticsHandleBo.checkProcessStatusUpdate(logisticsAbnormalInfo.getProcessStatus(), newProcessStatus, logisticsOrderInfo.getAppName())) {
            // 不存在变化，跳过
            return;
        }
        // 更新物流单的处理状态
        logisticsAbnormalInfo.setProcessStatus(newProcessStatus);
        // 更新所有异常项的处理状态
        for (LogisticsAbnormalStrategy strategy : strategyList) {
            boolean change = strategy.setLogisticsProcessStatus(logisticsAbnormalInfo, newProcessStatus, logisticsOrderInfo.getAppName());
            if (change) {
                // 异常信息更新，清除对应项的缓存
                String abnormalType = strategy.getAbnormalType();
                String sellerId = logisticsOrderInfo.getSellerId();
                String storeId = logisticsOrderInfo.getStoreId();
                String appName = logisticsOrderInfo.getAppName();
                logisticsAbnormalRedisDao.clear(abnormalType, storeId, appName, sellerId);
            }
        }
        logisticsOrderInfo.setLogisticsAbnormalInfo(logisticsAbnormalInfo);
    }

    /**
     * 判断是否需要发送弹窗
     * @param logisticsOrderInfo
     * @param abnormalType
     * @return
     */
    public boolean checkNeedSendAbnormalNotify(LogisticsOrderInfo logisticsOrderInfo, String abnormalType) {
        if (Objects.isNull(logisticsOrderInfo)) {
            // 不存在异常无需弹窗
            return false;
        }
        Map<String, String> settings = getSettings(logisticsOrderInfo);
        for (LogisticsAbnormalStrategy strategy : strategyList) {
            if (abnormalType.equals(strategy.getAbnormalType())) {
                return strategy.checkHasAbnormal(logisticsOrderInfo) && strategy.checkNeedSendAbnormalNotify(settings);
            }
        }
        return false;
    }


    /**
     * 获取用户配置
     * @param logisticsOrderInfo
     * @return
     */
    private Map<String, String> getSettings(LogisticsOrderInfo logisticsOrderInfo) {

        String sellerId = logisticsOrderInfo.getSellerId();
        String appName = logisticsOrderInfo.getAppName();
        String storeId = logisticsOrderInfo.getStoreId();

        if (StringUtils.isAnyEmpty(sellerId, appName, storeId)) {
            return null;
        }

        BatchSettingGetRequest batchSettingGetRequest = new BatchSettingGetRequest();
        batchSettingGetRequest.setApp(appName);
        batchSettingGetRequest.setPlatformId(storeId);
        batchSettingGetRequest.setUserId(sellerId);
        List<String> settingStrList = UserAbnormalSettingConstant.appNameAndSettingStrListMap
            .get(LogisticsUtil.defaultAppName(storeId, appName, CommonAppConstants.APP_TRADE));

        batchSettingGetRequest.setSettings(settingStrList);
        batchSettingGetRequest.setLoadDefaultSetting(true);
        List<UserSettingDTO> userSettings = userInfoService.batchSettingGet(batchSettingGetRequest);

        if (CollectionUtils.isEmpty(userSettings)) {
            return null;
        }
        return userSettings.stream().collect(Collectors.toMap(UserSettingDTO::getKey, UserSettingDTO::getValue));
    }
}
