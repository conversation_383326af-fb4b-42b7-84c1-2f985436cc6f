package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.logistics.common.config.LogisticsAbnormalCheckConfig;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import cn.loveapp.logistics.common.service.abnormalstrategy.LogisticsAbnormalStrategy;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import static cn.loveapp.common.es.EsQueryBuilders.*;

/**
 * 异常物流公共处理逻辑抽象类
 *
 * <AUTHOR>
 * @Date 2023/7/13 15:27
 */
public abstract class AbstractLogisticsAbnormalStrategy implements LogisticsAbnormalStrategy {

    protected final DateTimeFormatter minuteSecondFormatter = DateTimeFormatter.ofPattern(LogisticsOrderInfoSearchES.MINUTE_SECOND_FORMATER);

    @Autowired
    protected LogisticsAbnormalCheckConfig checkConfig;

    /**
     * 获取异常校验配置
     * @param userSetting
     * @param settingStr
     * @param defaultSetting
     * @return
     * @param <T>
     */
    protected <T> T getSetting(Map<String, String> userSetting, String settingStr, T defaultSetting, Class<T> type) {

        if (userSetting == null) {
            return defaultSetting;
        }

        String value = userSetting.get(settingStr);
        if (value == null) {
            return defaultSetting;
        }

        try {
            return ConvertUtil.parseOrGetDefault(value, defaultSetting, type);
        } catch (Exception e) {
            return defaultSetting;
        }
    }

    /**
     * 获取计算异常校验队列延迟等级的时间
     * @return
     */
    abstract LocalDateTime getAbnormalDeadline(LocalDateTime checkTime);

    /**
     * 获取计算异常校验队列延迟等级的时间
     * @param isTimeFixed 时间是否可变化
     * @param checkTimeMaxHour 最大判断配置时长（小时）
     * @param checkTimeMinHour 最小判断配置时长（小时）
     * @param abnormalCheckTime 异常对比时间
     * @return
     */
    protected LocalDateTime getAbnormalDeadline(boolean isTimeFixed, int checkTimeMaxHour, int checkTimeMinHour, LocalDateTime abnormalCheckTime) {
        LocalDateTime now = LocalDateTime.now();
        if (abnormalCheckTime == null && isTimeFixed) {
            // 异常校验时间可变化，并且当前为空，需要实时判断
            return now;
        } else if (abnormalCheckTime == null){
            return null;
        }

        LocalDateTime maxCheckTime = abnormalCheckTime.plusHours(checkTimeMaxHour);
        LocalDateTime minCheckTime = abnormalCheckTime.plusHours(checkTimeMinHour);

        if (now.isBefore(minCheckTime)) {
            // 当前时间小于最小判断异常时间，使用最小异常校验时间去计算延迟等级
            return minCheckTime;
        } else if (now.isAfter(minCheckTime) && now.isBefore(maxCheckTime)){
            // 根据配置不同，用户切换配置会变更，需要实时判断
            return now;
        } else {
            // 超过最大判断时间，根据校验时间是否可变，返回
            if (isTimeFixed) {
                // 按照最小配置+当前时间（假设消息刚结束就异常了）去算
                return now.plusHours(checkTimeMinHour);
            } else {
                // 一定是一直异常了，通过推送消息更新为正常，无需校验队列判断直接停止当作不需要判断时间延迟的异常
                return null;
            }
        }
    }

    /**
     * 生成交易异常es语法
     *
     * @param abnormalQueryDTO
     * @return
     */
    protected void appendQueryBuilder(AbnormalQueryDTO abnormalQueryDTO, BoolQuery.Builder queryCondition) {
        if (abnormalQueryDTO.getStartConsignTime() != null) {
            queryCondition.must(rangeQuery(EsFields.consignTime).gte(minuteSecondFormatter.format(abnormalQueryDTO.getStartConsignTime())).build());
        }
    }
}
