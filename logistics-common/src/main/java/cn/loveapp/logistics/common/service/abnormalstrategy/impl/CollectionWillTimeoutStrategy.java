package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static cn.loveapp.common.es.EsQueryBuilders.*;

/**
 * <AUTHOR>
 * @date 2025-03-20 14:47
 * @description: 揽收将超时（在设置的揽收超时时间基础上追加一段范围为将要超时）
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class CollectionWillTimeoutStrategy extends AbstractLogisticsAbnormalStrategy{

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CollectionWillTimeoutStrategy.class);

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.COLLECTION_WILL_TIMEOUT.value();
    }

    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (!LogisticsUtil.isTradeERP(appName)) {
            // 非erp跳过
            return new ExecuteResult();
        }

        boolean collectionTimeoutEnable = getSetting(userSettings,
            UserAbnormalSettingConstant.LOGISTICS_UN_COLLECT_TIMEOUT_ENABLE, true, Boolean.class);

        if (!collectionTimeoutEnable) {
            return new ExecuteResult();
        }

        boolean collectionWillTimeoutEnable = getSetting(userSettings,
            UserAbnormalSettingConstant.LOGISTICS_COLLECTION_WILL_TIMEOUT_ENABLE, true, Boolean.class);

        if (!collectionWillTimeoutEnable) {
            return new ExecuteResult();
        }

        // 揽收将要超时时间
        Integer collectionWillTimeout = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_COLLECTION_WILL_TIMEOUT, 12, Integer.class);
        // 自定义揽收超时时间
        Integer collectionTimeout = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_UN_COLLECT_TIMEOUT, 12, Integer.class);
        LogisticsOrderInfo.BusinessInfo businessInfo = logisticsOrderInfo.getBusinessInfo();
        if (businessInfo == null || Objects.isNull(businessInfo.getConsignTime())) {
            // 无发货信息，跳过
            return new ExecuteResult();
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
        // 已揽收
        boolean collect = false;
        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            collect = logisticsStatusList.contains(AyLogisticsStatus.PICKED_UP.value())
                || logisticsStatusList.stream().anyMatch(status -> status.startsWith(AyLogisticsStatus.AFTER_PICK_BEFORE_DELIVER_STATUS_PREFIX) || status.startsWith(AyLogisticsStatus.DELIVERED_STATUS_PREFIX));
        }

        LocalDateTime now = LocalDateTime.now();
        // 发货时间
        LocalDateTime consignTime = DateUtil.parseDate(businessInfo.getConsignTime());
        // 揽收超时开始时间
        LocalDateTime collectionTimeoutStartTime = consignTime.plusHours(collectionTimeout);
        // 揽收将超时时间
        LocalDateTime collectionWillTimeoutStartTime = collectionTimeoutStartTime.minusHours(collectionWillTimeout);
        // 最长不超过发货时间
        collectionWillTimeoutStartTime =
            collectionWillTimeoutStartTime.isAfter(consignTime) ? collectionWillTimeoutStartTime : consignTime;

        // 揽收将超时  揽收超时时间-M【将要超时提醒】 > 当前时间 > 揽收超时时间(发货时间 + N【揽收超时提醒】)
        boolean checkCollectionWillTimeout =
            !collect && collectionWillTimeoutStartTime.isBefore(now) && collectionTimeoutStartTime.isAfter(now);

        LogisticsOrderInfo.AbnormalDetails collectionWillTimeoutAbnormalDetails =
            logisticsAbnormalInfo.getCollectionWillTimeout();
        if (Objects.isNull(collectionWillTimeoutAbnormalDetails)) {
            collectionWillTimeoutAbnormalDetails = new LogisticsOrderInfo.AbnormalDetails();
        }

        boolean isUpdate =
            LogisticsOrderInfo.generalAbnormalDetails(checkCollectionWillTimeout, collectionWillTimeoutAbnormalDetails, collectionTimeoutStartTime);

        if (checkCollectionWillTimeout || isUpdate) {
            LOGGER.logInfo("执行策略：【揽收超时前 " + collectionWillTimeoutStartTime + "小时, 揽收将超时】，判断结果："
                + checkCollectionWillTimeout + ", 是否变更：" + isUpdate);
        }

        if (isUpdate) {
            logisticsAbnormalInfo.setCollectionWillTimeout(collectionWillTimeoutAbnormalDetails);
        }

        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkCollectionWillTimeout, isUpdate);
        return new ExecuteResult(isUpdate, getAbnormalDeadline(consignTime));
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getCollectionWillTimeoutCount();
        if (countAll == null) {
            countAll = 0;
        }
        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setCollectionWillTimeoutCount(countAll);
    }

    @Override
    public BoolQuery.Builder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQuery.Builder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.collectionWillTimeoutExists, true));

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }

        if (BooleanUtils.isNotFalse(abnormalQueryDTO.getIsExcludeProcessed())) {
            queryCondition
                .mustNot(termQuery(EsFields.collectionWillTimeoutProcessStatus, AbnormalProcessStatus.PROCESSED.value()));
        }

        if (BooleanUtils.isTrue(abnormalQueryDTO.getIsExistConsignTime())) {
            queryCondition.must(existsQuery(EsFields.consignTime));
        }

        if (BooleanUtils.isTrue(abnormalQueryDTO.getIsExcludeOtherAbnormalOfTradeApp())) {
            queryCondition.mustNot(termQuery(EsFields.otherAbnormalOfTradeAppIsExists, true));
        }

        appendQueryBuilder(abnormalQueryDTO, queryCondition);

        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termQuery(EsFields.collectionWillTimeoutProcessStatus, AbnormalProcessStatus.PENDING.value()))
                .should(boolQuery().mustNot(existsQuery(EsFields.collectionWillTimeoutProcessStatus)).build())
            .build());
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {
        LogisticsOrderInfo.AbnormalDetails collectionWillTimeout = logisticsAbnormalInfo.getCollectionWillTimeout();
        if (Objects.isNull(collectionWillTimeout)) {
            return false;
        }

        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(collectionWillTimeout.getProcessStatus(), newProcessStatus, appName)) {
            collectionWillTimeout.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setCollectionWillTimeout(collectionWillTimeout);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal())
            || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails collectionWillTimeout = logisticsAbnormalInfo.getCollectionWillTimeout();
        return !Objects.isNull(collectionWillTimeout) && BooleanUtils.isTrue(collectionWillTimeout.getIsExists())
            && !AbnormalProcessStatus.PROCESSED.value().equals(collectionWillTimeout.getProcessStatus());
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return getSetting(userSettings, UserAbnormalSettingConstant.ABNORMAL_LOGISTICS_NAVIGATION_REMIND_ENABLE, false,
            Boolean.class);
    }

    @Override
    LocalDateTime getAbnormalDeadline(LocalDateTime checkTime) {
        int checkTimeMin = checkConfig.getNotPickedUpAfterSendMinCheckTime();
        int checkTimeMax = checkConfig.getNotPickedUpAfterSendMaxCheckTime();
        return getAbnormalDeadline(false, checkTimeMax, checkTimeMin, checkTime);
    }

}
