package cn.loveapp.logistics.common.service.abnormalstrategy.impl;


import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.config.LogisticsAbnormalCheckConfig;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static cn.loveapp.common.es.EsQueryBuilders.*;

/**
 * 派签超时: 定义为处于派送中状态, 用户自定时间内未签收成功的包裹。
 *
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class DeliverySignTimeOutStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DeliverySignTimeOutStrategy.class);

    @Autowired
    protected LogisticsAbnormalCheckConfig checkConfig;


    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (LogisticsUtil.isTradeERP(appName)) {
            // erp(无需处理此类异常 跳过)
            return new ExecuteResult();
        }

        Date lastActionModified = logisticsOrderInfo.getLastActionModified();
        String lastAction = logisticsOrderInfo.getLastAction();
        if (lastActionModified == null || StringUtils.isEmpty(lastAction)) {
            return new ExecuteResult(false, getAbnormalDeadline(null));
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        boolean isUpdate = false;

        int checkTime = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_DELIVERY_SIGN_TIME_OUT, 12, Integer.class);
        LocalDateTime now = LocalDateTime.now();

        // 上次轨迹变更时间
        LocalDateTime lastActionModifiedTime = DateUtil.parseDate(lastActionModified);
        // N小时处于派件中状态
        LocalDateTime deadLine = lastActionModifiedTime.plusHours(checkTime);
        boolean deliverySignTimeOutCheckTime = deadLine.isBefore(now);

        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
        // 已签收
        boolean delivered = false;
        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            delivered = logisticsStatusList.stream().anyMatch(AyLogisticsStatus.DELIVERED_LIST::contains);
        }
        boolean isDeliveringStatus = AyLogisticsStatus.DELIVERING.value().equals(lastAction);


        // 处于派件中状态时间超过N小时: 最后一次更新物流时间 + N 小时 < 当前时间 && 处于派件中状态 && 物流轨迹未签收
        boolean checkDeliverySignTimeOut = deliverySignTimeOutCheckTime && isDeliveringStatus && !delivered;

        LogisticsOrderInfo.AbnormalDetails deliverySignTimeOut = logisticsAbnormalInfo.getDeliverySignTimeOut();
        if (Objects.isNull(deliverySignTimeOut)) {
            deliverySignTimeOut = new LogisticsOrderInfo.AbnormalDetails();
        }
        isUpdate = LogisticsOrderInfo.generalAbnormalDetails(checkDeliverySignTimeOut, deliverySignTimeOut, null);

        if (checkDeliverySignTimeOut || isUpdate) {
            LOGGER.logInfo(logisticsOrderInfo.getOutSid() + " 执行策略：【派签" + checkTime + "未更新】，判断结果："
                + checkDeliverySignTimeOut + ", 是否变更：" + isUpdate);
        }
        if (isUpdate) {
            // 存在变化，更新存单
            logisticsAbnormalInfo.setDeliverySignTimeOut(deliverySignTimeOut);
        }
        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkDeliverySignTimeOut, isUpdate);
        return new ExecuteResult(isUpdate, getAbnormalDeadline(lastActionModifiedTime));
    }


    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.DELIVERY_SIGN_TIMEOUT.value();
    }


    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getDeliverySignTimeOutCount();
        if (countAll == null) {
            countAll = 0;
        }
        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setDeliverySignTimeOutCount(countAll);
    }

    @Override
    public BoolQuery.Builder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQuery.Builder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.deliverySignTimeOutIsExists, true))
            .must(existsQuery(EsFields.consignTime))
            .mustNot(termQuery(EsFields.otherAbnormalOfTradeAppIsExists, true));

        appendQueryBuilder(abnormalQueryDTO, queryCondition);

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }
        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termQuery(EsFields.deliverySignTimeOutProcessStatus, AbnormalProcessStatus.PENDING.value()))
                .should(boolQuery().mustNot(existsQuery(EsFields.deliverySignTimeOutProcessStatus)).build())
            .build());
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {
        LogisticsOrderInfo.AbnormalDetails deliverySignTimeOut = logisticsAbnormalInfo.getDeliverySignTimeOut();
        if (Objects.isNull(deliverySignTimeOut)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(deliverySignTimeOut.getProcessStatus(), newProcessStatus,
            appName)) {
            deliverySignTimeOut.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setTrackingNotUpdatedAfterOfPickup(deliverySignTimeOut);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails abnormalDetails = logisticsAbnormalInfo.getDeliverySignTimeOut();
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return false;
    }


    @Override
    LocalDateTime getAbnormalDeadline(LocalDateTime lastActionModified) {
        int checkTimeMin = checkConfig.getDeliverySignTimeOutMinCheckTime();
        int checkTimeMax = checkConfig.getDeliverySignTimeOutMaxCheckTime();
        return getAbnormalDeadline(true, checkTimeMax, checkTimeMin, lastActionModified);
    }
}
