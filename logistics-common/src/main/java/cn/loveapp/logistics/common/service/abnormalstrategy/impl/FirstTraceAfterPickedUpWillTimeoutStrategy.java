package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

import static cn.loveapp.common.es.EsQueryBuilders.*;

/**
 * <AUTHOR>
 * @date 2025-03-20 17:16
 * @description:揽收后更新将要超时：定义为揽收后第一次物流未在用户设置时间内更新
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class FirstTraceAfterPickedUpWillTimeoutStrategy extends AbstractLogisticsAbnormalStrategy {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(FirstTraceAfterPickedUpTimeoutStrategy.class);

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.FIRST_TRACE_AFTER_PICKED_UP_WILL_TIMEOUT.value();
    }

    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (!LogisticsUtil.isTradeERP(appName)) {
            // 非erp跳过
            return new ExecuteResult();
        }

        boolean firstTraceAfterPickedUpTimeoutEnable = getSetting(userSettings,
            UserAbnormalSettingConstant.LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT_ENABLE, true, Boolean.class);
        if (!firstTraceAfterPickedUpTimeoutEnable) {
            return new ExecuteResult();
        }

        boolean firstTraceAfterPickedUpWillTimeoutEnable = getSetting(userSettings,
            UserAbnormalSettingConstant.LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_WILL_TIMEOUT_ENABLE, true, Boolean.class);
        if (!firstTraceAfterPickedUpWillTimeoutEnable) {
            return new ExecuteResult();
        }

        String lastAction = logisticsOrderInfo.getLastAction();
        LocalDateTime lastActionModified = DateUtil.parseDate(logisticsOrderInfo.getLastActionModified());
        if (lastActionModified == null || StringUtils.isEmpty(lastAction)) {
            return new ExecuteResult(false, getAbnormalDeadline(null));
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LocalDateTime now = LocalDateTime.now();
        boolean isUpdate = false;

        Integer firstTraceAfterPickedUpTimeoutHours = getSetting(userSettings,
            UserAbnormalSettingConstant.LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT, 12, Integer.class);
        Integer firstTraceAfterPickedUpWillTimeoutHours = getSetting(userSettings,
            UserAbnormalSettingConstant.LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_WILL_TIMEOUT, 12, Integer.class);

        boolean lastActionIsPickUp = AyLogisticsStatus.PICKED_UP.value().equals(lastAction);
        LocalDateTime firstTraceAfterPickedUpTimeoutTime =
            lastActionModified.plusHours(firstTraceAfterPickedUpTimeoutHours);

        LocalDateTime firstTraceAfterPickedUpWillTimeoutTime =
            firstTraceAfterPickedUpTimeoutTime.minusHours(firstTraceAfterPickedUpWillTimeoutHours);

        firstTraceAfterPickedUpWillTimeoutTime = firstTraceAfterPickedUpWillTimeoutTime.isAfter(lastActionModified)
            ? firstTraceAfterPickedUpWillTimeoutTime : lastActionModified;

        // 揽收后第一次物流变更将要超时时间 < 当前时间 < 揽收后第一次物流变更超时时间
        boolean checkFirstTraceAfterPickWillTimeout = lastActionIsPickUp
            && now.isAfter(firstTraceAfterPickedUpWillTimeoutTime) && now.isBefore(firstTraceAfterPickedUpTimeoutTime);

        LogisticsOrderInfo.AbnormalDetails firstTraceAfterPickedUpWillTimeout =
            logisticsAbnormalInfo.getFirstTraceAfterPickedUpWillTimeout();
        if (Objects.isNull(firstTraceAfterPickedUpWillTimeout)) {
            firstTraceAfterPickedUpWillTimeout = new LogisticsOrderInfo.AbnormalDetails();
        }

        isUpdate = LogisticsOrderInfo.generalAbnormalDetails(checkFirstTraceAfterPickWillTimeout,
            firstTraceAfterPickedUpWillTimeout, firstTraceAfterPickedUpTimeoutTime);
        if (checkFirstTraceAfterPickWillTimeout || isUpdate) {
            LOGGER.logInfo(logisticsOrderInfo.getOutSid() + "执行策略：【揽收超时前" + firstTraceAfterPickedUpWillTimeoutHours
                + "小时后未更新第一次物流】，判断结果：" + checkFirstTraceAfterPickWillTimeout + ", 是否变更：" + isUpdate);
        }

        if (isUpdate) {
            logisticsAbnormalInfo.setFirstTraceAfterPickedUpWillTimeout(firstTraceAfterPickedUpWillTimeout);
        }

        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkFirstTraceAfterPickWillTimeout, isUpdate);
        return new ExecuteResult(isUpdate, getAbnormalDeadline(lastActionModified));
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getFirstTraceAfterPickedUpWillTimeoutCount();
        if (countAll == null) {
            countAll = 0;
        }

        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setFirstTraceAfterPickedUpWillTimeoutCount(countAll);
    }

    @Override
    public BoolQuery.Builder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQuery.Builder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.firstTraceAfterPickedUpWillTimeoutIsExists, true))
            .must(existsQuery(EsFields.consignTime)).mustNot(termQuery(EsFields.otherAbnormalOfTradeAppIsExists, true));

        appendQueryBuilder(abnormalQueryDTO, queryCondition);

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }

        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termQuery(EsFields.firstTraceAfterPickedUpWillTimeoutProcessStatus, AbnormalProcessStatus.PENDING.value()))
                .should(boolQuery().mustNot(existsQuery(EsFields.firstTraceAfterPickedUpWillTimeoutProcessStatus)).build())
            .build());
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {
        LogisticsOrderInfo.AbnormalDetails firstTraceAfterPickedUpWillTimeout =
            logisticsAbnormalInfo.getFirstTraceAfterPickedUpWillTimeout();
        if (Objects.isNull(firstTraceAfterPickedUpWillTimeout)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(firstTraceAfterPickedUpWillTimeout.getProcessStatus(),
            newProcessStatus, appName)) {
            firstTraceAfterPickedUpWillTimeout.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setFirstTraceAfterPickedUpWillTimeout(firstTraceAfterPickedUpWillTimeout);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal())
            || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails abnormalDetails =
            logisticsAbnormalInfo.getFirstTraceAfterPickedUpWillTimeout();
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists())
            && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return false;
    }

    @Override
    LocalDateTime getAbnormalDeadline(LocalDateTime lastActionModified) {
        int checkTimeMin = checkConfig.getFirstTraceAfterPickedUpWillTimeoutMinCheckTime();
        int checkTimeMax = checkConfig.getFirstTraceAfterPickedUpWillTimeoutMaxCheckTime();
        return getAbnormalDeadline(true, checkTimeMax, checkTimeMin, lastActionModified);
    }
}
