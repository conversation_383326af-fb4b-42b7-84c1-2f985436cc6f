package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static cn.loveapp.common.es.EsQueryBuilders.*;

/**
 * 物流异常策略 发货N小时后未揽件（揽收超时）
 * <p>
 * LogisticsAbnormalType.NO_PICKED_UP_AFTER_SEND
 *
 * <AUTHOR>
 * @Date 2023/6/21 10:41
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class NoPickedUpAfterSendStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(NoPickedUpAfterSendStrategy.class);

    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LocalDateTime now = LocalDateTime.now();
        boolean isUpdate = false;
        // 设置可配置 发货N小时后未揽件, 默认20小时
        boolean enable = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_UN_COLLECT_TIMEOUT_ENABLE, true, Boolean.class);
        if (!enable) {
            return new ExecuteResult();
        }

        Integer checkTime = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_UN_COLLECT_TIMEOUT, 20, Integer.class);
        LogisticsOrderInfo.BusinessInfo businessInfo = logisticsOrderInfo.getBusinessInfo();
        if (businessInfo == null || Objects.isNull(businessInfo.getConsignTime())) {
            // 无发货信息，跳过
            return new ExecuteResult();
        }
        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
        // 已揽收
        boolean collect = false;
        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            collect = logisticsStatusList.contains(AyLogisticsStatus.PICKED_UP.value())
                || logisticsStatusList.stream().anyMatch(status -> status.startsWith(AyLogisticsStatus.AFTER_PICK_BEFORE_DELIVER_STATUS_PREFIX) || status.startsWith(AyLogisticsStatus.DELIVERED_STATUS_PREFIX));
        }
        // 发货时间
        LocalDateTime consignTime = DateUtil.parseDate(businessInfo.getConsignTime());
        // 发货N小时未揽件  发货时间 + N小时 < 当前时间  && 物流轨迹没有揽收记录
        LocalDateTime deadline = consignTime.plusHours(checkTime);
        boolean checkNotPickedUpAfterSend = deadline.isBefore(now) && !collect;

        LogisticsOrderInfo.AbnormalDetails notPickedUpAfterSend = logisticsAbnormalInfo.getNotPickedUpAfterSend();
        if (Objects.isNull(notPickedUpAfterSend)) {
            notPickedUpAfterSend = new LogisticsOrderInfo.AbnormalDetails();
        }

        isUpdate = LogisticsOrderInfo.generalAbnormalDetails(checkNotPickedUpAfterSend, notPickedUpAfterSend, deadline);

        if (checkNotPickedUpAfterSend || isUpdate) {
            LOGGER.logInfo("执行策略：【发货" + checkTime + "小时后未揽件】，判断结果：" + checkNotPickedUpAfterSend + ", 是否变更：" + isUpdate);
        }
        if (isUpdate) {
            // 存在变化，更新存单
            logisticsAbnormalInfo.setNotPickedUpAfterSend(notPickedUpAfterSend);
        }

        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkNotPickedUpAfterSend, isUpdate);
        if (isUpdate && checkNotPickedUpAfterSend) {
            appendLogisticsAbnormalTypeHistory(logisticsOrderInfo, getAbnormalType());
        }

        return new ExecuteResult(isUpdate, getAbnormalDeadline(consignTime));
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {

        Integer countAll = abnormalCountDTO.getNotPickedUpAfterSendCount();
        if (countAll == null) {
            countAll = 0;
        }
        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setNotPickedUpAfterSendCount(countAll);
    }

    @Override
    public BoolQuery.Builder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQuery.Builder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.notPickedUpAfterSendIsExists, true));

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }

        if (BooleanUtils.isNotFalse(abnormalQueryDTO.getIsExcludeProcessed())) {
            queryCondition.mustNot(termQuery(EsFields.notPickedUpAfterSendProcessStatus, AbnormalProcessStatus.PROCESSED.value()));
        }

        if (BooleanUtils.isTrue(abnormalQueryDTO.getIsExistConsignTime())) {
            queryCondition.must(existsQuery(EsFields.consignTime));
        }

        if (BooleanUtils.isTrue(abnormalQueryDTO.getIsExcludeOtherAbnormalOfTradeApp())) {
            queryCondition.mustNot(termQuery(EsFields.otherAbnormalOfTradeAppIsExists, true));
        }

        appendQueryBuilder(abnormalQueryDTO, queryCondition);

        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termQuery(EsFields.notPickedUpAfterSendProcessStatus, AbnormalProcessStatus.PENDING.value()))
                .should(boolQuery().mustNot(existsQuery(EsFields.notPickedUpAfterSendProcessStatus)).build())
            .build());
        }

        return queryCondition;
    }

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.NO_PICKED_UP_AFTER_SEND.value();
    }


    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {

        LogisticsOrderInfo.AbnormalDetails notPickedUpAfterSend = logisticsAbnormalInfo.getNotPickedUpAfterSend();
        if (Objects.isNull(notPickedUpAfterSend)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(notPickedUpAfterSend.getProcessStatus(), newProcessStatus,
            appName)) {
            notPickedUpAfterSend.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setNotPickedUpAfterSend(notPickedUpAfterSend);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails abnormalDetails = logisticsAbnormalInfo.getNotPickedUpAfterSend();
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public LocalDateTime getAbnormalDeadline(LocalDateTime consignTime) {
        int checkTimeMin = checkConfig.getNotPickedUpAfterSendMinCheckTime();
        int checkTimeMax = checkConfig.getNotPickedUpAfterSendMaxCheckTime();
        return getAbnormalDeadline(false, checkTimeMax, checkTimeMin, consignTime);
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_UN_COLLECT_TIMEOUT_PROMPT, false, Boolean.class);
    }
}
