package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static cn.loveapp.common.es.EsQueryBuilders.*;

/**
 * 物流异常策略 标记拦截后N小时未截返
 * <p>
 * LogisticsAbnormalType.NOT_RETURNED_OF_INTERCEPTION
 *
 * <AUTHOR>
 * @Date 2023/6/21 10:41
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class NotReturnedOfInterceptionStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(NotReturnedOfInterceptionStrategy.class);

    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (LogisticsUtil.isTradeERP(appName)) {
            // erp(无需处理此类异常 跳过)
            return new ExecuteResult();
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LocalDateTime now = LocalDateTime.now();
        boolean isUpdate = false;
        // 设置可配置 标记拦截后N小时未截返 默认24
        int checkTime = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_UN_RETURN_TIMEOUT, 24, Integer.class);
        Boolean isTagIntercepted = logisticsOrderInfo.getIsTagIntercepted();
        Date tagInterceptedModified = logisticsOrderInfo.getTagInterceptedModified();

        boolean checkNotReturnedOfInterception;

        LocalDateTime deadline = null;
        LocalDateTime tagInterceptedModifiedTime = null;
        if (!BooleanUtils.isTrue(isTagIntercepted) || Objects.isNull(tagInterceptedModified)) {
            // 无拦截信息
            checkNotReturnedOfInterception = false;
        } else {
            // 拦截时间
            tagInterceptedModifiedTime = DateUtil.parseDate(tagInterceptedModified);

            Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
            // 已拦截
            boolean intercepted = false;
            if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
                intercepted = logisticsStatusList.contains(AyLogisticsStatus.INTERCEPTED.value());
            }

            // 标记拦截N小时未截返  状态标记为拦截 && 拦截时间 + N小时 < 当前时间 && 物流轨迹没有拦截记录
            deadline = tagInterceptedModifiedTime.plusHours(checkTime);
            checkNotReturnedOfInterception = deadline.isBefore(now) && !intercepted;
        }
        LogisticsOrderInfo.AbnormalDetails notReturnedOfInterception = logisticsAbnormalInfo.getNotReturnedOfInterception();
        if (Objects.isNull(notReturnedOfInterception)) {
            notReturnedOfInterception = new LogisticsOrderInfo.AbnormalDetails();
        }

        isUpdate = LogisticsOrderInfo.generalAbnormalDetails(checkNotReturnedOfInterception, notReturnedOfInterception, null);

        if (checkNotReturnedOfInterception || isUpdate) {
            LOGGER.logInfo(
                "执行策略：【标记拦截后" + checkTime + "小时未截返】，判断结果：" + checkNotReturnedOfInterception + ", 是否变更：" + isUpdate);
        }
        if (isUpdate) {
            // 存在变化，更新存单
            logisticsAbnormalInfo.setNotReturnedOfInterception(notReturnedOfInterception);
        }
        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkNotReturnedOfInterception, isUpdate);
        return new ExecuteResult(isUpdate, getAbnormalDeadline(tagInterceptedModifiedTime));
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {

        Integer countAll = abnormalCountDTO.getNotReturnedOfInterceptionCount();
        if (countAll == null) {
            countAll = 0;
        }
        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setNotReturnedOfInterceptionCount(countAll);
    }

    @Override
    public BoolQuery.Builder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQuery.Builder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.notReturnedOfInterceptionIsExists, true))
            .mustNot(termQuery(EsFields.notReturnedOfInterceptionProcessStatus, AbnormalProcessStatus.PROCESSED.value()));

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }
        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termQuery(EsFields.notReturnedOfInterceptionProcessStatus, AbnormalProcessStatus.PENDING.value()))
                .should(boolQuery().mustNot(existsQuery(EsFields.notReturnedOfInterceptionProcessStatus)).build())
            .build());
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {

        LogisticsOrderInfo.AbnormalDetails notReturnedOfInterception =
            logisticsAbnormalInfo.getNotReturnedOfInterception();
        if (Objects.isNull(notReturnedOfInterception)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(notReturnedOfInterception.getProcessStatus(), newProcessStatus,
            appName)) {
            notReturnedOfInterception.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setNotReturnedOfInterception(notReturnedOfInterception);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails abnormalDetails = logisticsAbnormalInfo.getNotReturnedOfInterception();
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.NOT_RETURNED_OF_INTERCEPTION.value();
    }

    @Override
    public LocalDateTime getAbnormalDeadline(LocalDateTime tagInterceptedModifiedTime) {
        int checkTimeMin = checkConfig.getNotReturnedOfInterceptionMinCheckTime();
        int checkTimeMax = checkConfig.getNotReturnedOfInterceptionMaxCheckTime();
        return getAbnormalDeadline(true, checkTimeMax, checkTimeMin, tagInterceptedModifiedTime);
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_UN_RETURN_TIMEOUT_PROMPT, false, Boolean.class);
    }
}
