package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 物流异常策略 其他未分类异常(交易)
 *
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class OtherAbnormalOfTradeAppStrategy extends OtherAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OtherAbnormalOfTradeAppStrategy.class);

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.OTHER_ABNORMAL_Of_TRADE_APP.value();
    }


    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return false;
    }

    @Override
    protected LogisticsOrderInfo.AbnormalDetails getLogisticsAbnormalDetails(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo) {
        return logisticsAbnormalInfo.getOtherAbnormalOfTradeApp();
    }

    @Override
    protected List<String> getExcludeList(){
        return AyLogisticsStatus.EXCLUDE_OTHER_ABNORMAL_LIST_TRADE;
    }

    @Override
    protected Integer getLogisticsAbnormalCountDTO(LogisticsAbnormalCountDTO abnormalCountDTO) {
        return abnormalCountDTO.getOtherAbnormalOfTradeAppCount();
    }

    @Override
    protected String getOtherAbnormalIsExists(){
        return EsFields.otherAbnormalOfTradeAppIsExists;
    }

    @Override
    protected String getOtherAbnormalProcessStatus(){
        return EsFields.otherAbnormalOfTradeAppProcessStatus;
    }

    @Override
    protected void setOtherAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer countAll) {
        abnormalCountDTO.setOtherAbnormalOfTradeAppCount(countAll);
    }

    @Override
    protected void setOtherAbnormal(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo, LogisticsOrderInfo.AbnormalDetails otherAbnormal) {
        logisticsAbnormalInfo.setOtherAbnormalOfTradeApp(otherAbnormal);
    }

    @Override
    protected void resultLog(String logInfo){
        LOGGER.logInfo(logInfo);
    }
}
