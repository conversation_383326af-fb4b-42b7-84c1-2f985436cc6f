package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.loveapp.logistics.api.constant.AyLogisticsStatus.REFUSED_OR_RETURNED;
import static cn.loveapp.common.es.EsQueryBuilders.*;

/**
 * 物流异常策略 REFUSED_ACCEPT(拒收)
 * <p>
 * LogisticsAbnormalType.REFUSED_ACCEPT
 *
 * <AUTHOR>
 * @Date 2023/6/21 10:41
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class RefusedAcceptStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RefusedAcceptStrategy.class);


    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (LogisticsUtil.isTradeERP(appName)) {
            // erp(无需处理此类异常 跳过)
            return new ExecuteResult();
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails refusedAccept = logisticsAbnormalInfo.getRefusedAccept();
        if (Objects.isNull(refusedAccept)) {
            refusedAccept = new LogisticsOrderInfo.AbnormalDetails();
        }

        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();

        boolean hasAbnormal;
        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            boolean delivered = logisticsStatusList.stream().anyMatch(AyLogisticsStatus.DELIVERED_LIST::contains);
            List<String> abnormalStatus = logisticsStatusList.stream().filter(status -> status.equals(REFUSED_OR_RETURNED.value())).collect(Collectors.toList());
            hasAbnormal = !delivered && CollectionUtils.isNotEmpty(abnormalStatus);
        } else {
            hasAbnormal = false;
        }

        boolean isUpdate = LogisticsOrderInfo.generalAbnormalDetails(hasAbnormal, refusedAccept, null);

        if (hasAbnormal || isUpdate) {
            LOGGER.logInfo("执行策略：【拒收】，判断结果：" + hasAbnormal + ", 是否变更：" + isUpdate);
        }
        if (isUpdate) {
            // 存在变化，更新存单
            logisticsAbnormalInfo.setRefusedAccept(refusedAccept);
        }
        logisticsOrderInfo.checkAndSetMainAbnormalInfo(hasAbnormal, isUpdate);
        return new ExecuteResult(isUpdate, null);
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getRefusedAcceptCount();
        if (countAll == null) {
            countAll = 0;
        }
        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setRefusedAcceptCount(countAll);
    }

    @Override
    public BoolQuery.Builder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        // 异常存在且未处理
        BoolQuery.Builder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.refusedAcceptIsExists, true))
            .mustNot(termQuery(EsFields.refusedAcceptProcessStatus, AbnormalProcessStatus.PROCESSED.value()));

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }
        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termQuery(EsFields.refusedAcceptProcessStatus, AbnormalProcessStatus.PENDING.value()))
                .should(boolQuery().mustNot(existsQuery(EsFields.refusedAcceptProcessStatus)).build())
            .build());
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {

        LogisticsOrderInfo.AbnormalDetails refusedAccept = logisticsAbnormalInfo.getRefusedAccept();
        if (Objects.isNull(refusedAccept)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(refusedAccept.getProcessStatus(), newProcessStatus, appName)) {
            refusedAccept.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setRefusedAccept(refusedAccept);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails refusedAccept = logisticsAbnormalInfo.getRefusedAccept();
        return !Objects.isNull(refusedAccept) && BooleanUtils.isTrue(refusedAccept.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(refusedAccept.getProcessStatus());
    }

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.REFUSED_ACCEPT.value();
    }

    @Override
    public LocalDateTime getAbnormalDeadline(LocalDateTime checkTime) {
        return null;
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_REFUSED_ACCEPT_PROMPT, true, Boolean.class);
    }
}
