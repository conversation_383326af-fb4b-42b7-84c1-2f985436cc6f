package cn.loveapp.logistics.common.service.api;

import cn.loveapp.common.autoconfigure.platform.CommonDispatcherHandler;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.dto.request.AyLogisticNumberRecognitionRequest;
import cn.loveapp.logistics.common.dto.request.AySearchLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.AySubscribeLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.response.AyLogisticNumberRecognitionResponse;
import cn.loveapp.logistics.common.dto.response.AySearchLogisticsTraceResponse;
import cn.loveapp.logistics.common.dto.response.AySubscribeLogisticsTraceResponse;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;

/**
 * 物流API接口
 *
 * <AUTHOR>
 * @Date 2023/5/30 14:40
 */
public interface LogisticsApiPlatformHandleService extends CommonDispatcherHandler {

    /**
     * 物流轨迹订阅接口
     *
     * @param request
     * @param logisticsStoreId
     * @param logisticsAppName
     * @return
     */
    AySubscribeLogisticsTraceResponse subscribeLogisticsTrace(AySubscribeLogisticsTraceRequest request,
        String logisticsStoreId, String logisticsAppName);

    /**
     * 物流轨迹查询接口
     *
     * @param request
     * @param logisticsStoreId
     * @param appName
     * @return
     */
    AySearchLogisticsTraceResponse searchLogisticsTrace(AySearchLogisticsTraceRequest request, UserInfoDTO userInfoDTO,
        String logisticsStoreId, String appName) throws LogisticsHandlesException;

    /**
     * 识别物流（通过单号查询物流公司昵称及code）
     *
     * @param ayLogisticNumberRecognitionRequest
     * @param logisticsStoreId
     * @param appName
     * @return
     */
    default AyLogisticNumberRecognitionResponse recognitionLogisticNumber(
        AyLogisticNumberRecognitionRequest ayLogisticNumberRecognitionRequest, String logisticsStoreId,
        String appName) {
        return null;
    }
}
