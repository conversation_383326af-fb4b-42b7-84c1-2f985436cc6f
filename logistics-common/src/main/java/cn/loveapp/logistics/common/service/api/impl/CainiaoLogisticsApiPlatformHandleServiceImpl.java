package cn.loveapp.logistics.common.service.api.impl;

import java.util.List;

import cn.loveapp.logistics.common.service.UserInfoService;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import cn.loveapp.uac.domain.UserSettingDTO;
import cn.loveapp.uac.request.BatchSettingGetRequest;
import cn.loveapp.common.dto.LogisticsCallbackDataDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.taobao.pac.sdk.cp.dataobject.request.TRACK_LD_QUERY.TrackLdQueryRequest;
import com.taobao.pac.sdk.cp.dataobject.request.TRACK_LD_SUBSCRIBE.TrackLdSubscribeRequest;
import com.taobao.pac.sdk.cp.dataobject.response.TRACK_LD_QUERY.TrackInfo;
import com.taobao.pac.sdk.cp.dataobject.response.TRACK_LD_QUERY.TrackLdQueryResponse;
import com.taobao.pac.sdk.cp.dataobject.response.TRACK_LD_SUBSCRIBE.TrackLdSubscribeResponse;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.platformsdk.cainiao.CainiaoSDKService;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.constant.LogisticsPackType;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.dto.request.AySearchLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.AySubscribeLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.LogisticsMonitoringLogRequest;
import cn.loveapp.logistics.common.dto.response.AySearchLogisticsTraceResponse;
import cn.loveapp.logistics.common.dto.response.AySubscribeLogisticsTraceResponse;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.AyLogisticsQuotaService;
import cn.loveapp.logistics.common.service.LogisticsSendHandleService;
import cn.loveapp.logistics.common.service.api.LogisticsApiPlatformHandleService;

/**
 * <AUTHOR>
 * @date 2024-10-28 19:02
 * @description: 菜鸟物流接口实现类
 */
@Service
public class CainiaoLogisticsApiPlatformHandleServiceImpl implements LogisticsApiPlatformHandleService {

    private static final LoggerHelper LOGGER =
        LoggerHelper.getLogger(CainiaoLogisticsApiPlatformHandleServiceImpl.class);

    /**
     * api请求成功code
     */
    private static final Integer API_SUCCESS_CODE = 1000;

    /**
     * 已经订阅了code
     */
    private static final Integer SUBSCRIBED_CODE = 1001;

    /**
     * 物流详情查询为空code
     */
    private static final Integer NOT_LOGISTICS_INFO_CODE = 1002;

    /**
     * 可忽略的相应code
     */
    public static final List<Integer> IGNORE_RESPONSE_FAIL_CODE_LIST =
        Lists.newArrayList(SUBSCRIBED_CODE, NOT_LOGISTICS_INFO_CODE);

    /**
     * 物流订阅默认手机号
     */
    private static final String LOGISTICS_SUBSCRIPTION_DEFAULT_PHONE = "logistics.subscription.default.phone";

    /**
     * 顺丰运单号前缀
     */
    private static final String SF_PREFIX = "SF";

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private CainiaoSDKService cainiaoSDKService;

    @Autowired
    private LogisticsOrderInfoDao logisticsOrderInfoDao;

    @Autowired
    private LogisticsSendHandleService logisticsSendHandleService;

    @Autowired
    private AyLogisticsQuotaService ayLogisticsQuotaService;

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 需要校验customName字段的 物流公司编码集合
     */
    @Value("${logistics.service.cainiao.validCustomName.cpCodes:SF}")
    private List<String> cainiaoValidCustomNameCpCodes = Lists.newArrayList();

    @Override
    public AySubscribeLogisticsTraceResponse subscribeLogisticsTrace(AySubscribeLogisticsTraceRequest request,
        String logisticsStoreId, String logisticsAppName) {

        AySubscribeLogisticsTraceResponse response = new AySubscribeLogisticsTraceResponse();
        LogisticsOrderSubscribeDTO logisticsHandle = request.getLogisticsHandle();
        if (logisticsHandle == null) {
            LOGGER.logError("菜鸟订阅失败，参数为空");
            response.setSuccess(Boolean.FALSE);
            return response;
        }

        String sellerId = logisticsHandle.getSellerId();
        String storeId = logisticsHandle.getStoreId();
        String outSid = logisticsHandle.getOutSid();
        String appName = logisticsHandle.getAppName();
        String customerName = logisticsHandle.getCustomerName();
        String logisticsCompanyCode = logisticsHandle.getLogisticsCompanyCode();
        if (cainiaoValidCustomNameCpCodes.contains(logisticsCompanyCode)) {
            if (StringUtils.isEmpty(customerName)) {
                response.setSuccess(Boolean.FALSE);
                response.setErrorMsg("物流公司code为：" + logisticsCompanyCode + "时手机号不能为空: 运单号" + outSid);
                return response;
            }
        }

        LogisticsOrderInfo lastLogisticsOrderInfo =
            logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, storeId, appName);
        LogisticsPackType packType = LogisticsPackType.getPackType(logisticsHandle);
        boolean needDeductionQuota = request.isNeedDeductionQuota();
        if (LogisticsUtil.isTradeERP(appName)) {
            // erp不判断额度消耗
            needDeductionQuota = false;
        }

        if (needDeductionQuota) {
            // 需要消耗额度, 但库中已有记录，说明消耗过了,不需要再次消耗
            if (lastLogisticsOrderInfo != null && lastLogisticsOrderInfo.getSubscribeInfo() != null) {
                LogisticsOrderInfo.SubscribeInfo subscribeInfo = lastLogisticsOrderInfo.getSubscribeInfo();
                if (BooleanUtils.isTrue(subscribeInfo.getIsSuccess())
                    && logisticsStoreId.equals(lastLogisticsOrderInfo.getSaveLogisticsStoreId())) {
                    // 已订阅过快递鸟无需扣额度
                    needDeductionQuota = false;
                }
                packType = LogisticsPackType.getPackType(lastLogisticsOrderInfo);
            }
        }

        Integer monitoringNumWithholding =
            ayLogisticsQuotaService.withholdingLogisticQuota(logisticsHandle, needDeductionQuota);
        if (monitoringNumWithholding < 0) {
            response.setSuccess(Boolean.FALSE);
            response.setErrorMsg("余额不足，订阅失败");
            return response;
        }

        // 订阅物流
        TrackLdSubscribeRequest trackLdSubscribeRequest = new TrackLdSubscribeRequest();
        // 运单号
        trackLdSubscribeRequest.setMailNo(outSid);
        trackLdSubscribeRequest.setTag(CommonLogisticsConstants.PLATFORM_CAINIAO);
        trackLdSubscribeRequest.setUniqueCode(outSid + System.currentTimeMillis());
        if (logisticsHandle.getPhone() != null) {
            trackLdSubscribeRequest.setPhone(logisticsHandle.getPhone());
        } else if (outSid.startsWith(SF_PREFIX)) {
            // 顺丰需要获取手机号（寄件人或收件人手机号）
            String senderPhone = getLogisticsSenderPhone(sellerId, logisticsHandle.getSellerNick(), appName, storeId);
            trackLdSubscribeRequest.setPhone(senderPhone);
        }

        // 菜鸟cpCode可以不传，但是为保证订阅与推过来的物流公司code一致（用于入库映射）, 必须传
        trackLdSubscribeRequest.setCpCode(logisticsCompanyCode);
        LogisticsCallbackDataDTO callbackData = new LogisticsCallbackDataDTO();
        callbackData.setSellerId(logisticsHandle.getSellerId());
        callbackData.setAppName(logisticsHandle.getAppName());
        callbackData.setStoreId(logisticsHandle.getStoreId());
        // 自定义标签，可填入备注信息，回推物流详情时，会一起返回
        trackLdSubscribeRequest.setTag(callbackData.generateCallbackStr());
        boolean isSubscribeSuccess = true;
        try {
            TrackLdSubscribeResponse trackLdSubscribeResponse =
                cainiaoSDKService.excute(trackLdSubscribeRequest, logisticsAppName);
            if (trackLdSubscribeResponse == null) {
                isSubscribeSuccess = false;
            } else if (!API_SUCCESS_CODE.equals(trackLdSubscribeResponse.getCode())) {
                isSubscribeSuccess = false;
                if (SUBSCRIBED_CODE.equals(trackLdSubscribeResponse.getCode())) {
                    LOGGER.logInfo(outSid,"","已经订阅过了");
                    isSubscribeSuccess = true;
                }
            }
        } catch (Exception e) {
            LOGGER.logError("调用菜鸟订阅接口失败：" + e.getMessage(), e);
            isSubscribeSuccess = false;
        }

        response.setSuccess(isSubscribeSuccess);
        LogisticsMonitoringLogRequest monitoringLogRequest =
            LogisticsMonitoringLogRequest.generalRequest(logisticsHandle, isSubscribeSuccess, monitoringNumWithholding,
                packType, LogisticsMonitoringLogRequest.MONITORING_TYPE_SUBSCRIBE);
        logisticsSendHandleService.pushCustomNotifyMsg(
            logisticsConfig.getTradePcHost() + LogisticsMonitoringLogRequest.REQUEST_URL, monitoringLogRequest);

        return response;
    }

    @Override
    public AySearchLogisticsTraceResponse searchLogisticsTrace(AySearchLogisticsTraceRequest request,
        UserInfoDTO userInfoDTO, String logisticsStoreId, String appName) throws LogisticsHandlesException {
        String ownerSellerNick = null;
        String ownerSellerId = null;
        String ownerAppName = null;
        String ownerStoreId = null;

        switch (request.getSourceApp().value()) {
            case LOGIN_USER:
                // 淘宝交易扣除账号为当前登录账号，使用当前登录人作为扣除余额的用户
                ownerSellerId = userInfoDTO.getSellerId();
                ownerStoreId = userInfoDTO.getStoreId();
                ownerAppName = userInfoDTO.getAppName();
                ownerSellerNick = userInfoDTO.getNick();
                break;
            case TARGET_USER:
                // 多平台使用的是订阅人的信息，根据订阅人去查询其对应的爱用账号，使用爱用账号得余额进行扣除
                ownerSellerNick = request.getSellerNick();
                ownerSellerId = request.getSellerId();
                ownerAppName = request.getAppName();
                ownerStoreId = request.getStoreId();
                break;
        }

        AySearchLogisticsTraceResponse response = new AySearchLogisticsTraceResponse();
        LogisticsOrderSubscribeDTO logisticsOrderSubscribeDTO = new LogisticsOrderSubscribeDTO();
        logisticsOrderSubscribeDTO.setSellerNick(ownerSellerNick);
        logisticsOrderSubscribeDTO.setSellerId(ownerSellerId);
        logisticsOrderSubscribeDTO.setAppName(ownerAppName);
        logisticsOrderSubscribeDTO.setStoreId(ownerStoreId);
        logisticsOrderSubscribeDTO.setOutSid(request.getOutSid());
        logisticsOrderSubscribeDTO.setLogisticsCompanyCode(request.getLogisticsCompanyCode());
        logisticsOrderSubscribeDTO.setSourceApp(request.getSourceApp() != null ? request.getSourceApp().name() : null);

        String logisticsCompanyCode = request.getLogisticsCompanyCode();
        String outSid = request.getOutSid();
        String customerName = request.getCustomerName();
        if (cainiaoValidCustomNameCpCodes.contains(logisticsCompanyCode)) {
            if (org.apache.commons.lang3.StringUtils.isEmpty(customerName)) {
                throw new LogisticsHandlesException("物流公司code为：" + logisticsCompanyCode + "时手机号不能为空: 运单号" + outSid);
            }
        }

        Integer monitoringNumWithholding =
            ayLogisticsQuotaService.withholdingLogisticQuota(logisticsOrderSubscribeDTO, request.isNeedDeductionQuota());
        if (monitoringNumWithholding < 0) {
            throw new LogisticsHandlesException("快递鸟余额查询失败，参数为空");
        }

        TrackLdQueryRequest trackLdQueryRequest = new TrackLdQueryRequest();
        trackLdQueryRequest.setCpCode(logisticsCompanyCode);
        trackLdQueryRequest.setMailNo(outSid);
        trackLdQueryRequest.setUniqueCode(outSid + System.currentTimeMillis());

        if (request.getPhone() != null) {
            trackLdQueryRequest.setPhone(request.getPhone());
        } else if (outSid.startsWith(SF_PREFIX)) {
            // 顺丰需要获取手机号（寄件人或收件人手机号）
            String senderPhone = getLogisticsSenderPhone(ownerSellerId, ownerSellerNick, ownerAppName, ownerStoreId);
            trackLdQueryRequest.setPhone(senderPhone);
        }

        TrackLdQueryResponse trackLdQueryResponse = cainiaoSDKService.excute(trackLdQueryRequest, appName);
        if (trackLdQueryResponse == null || (!API_SUCCESS_CODE.equals(trackLdQueryResponse.getCode())
            && !IGNORE_RESPONSE_FAIL_CODE_LIST.contains(trackLdQueryResponse.getCode()))) {
            LOGGER.logError("调用查询api错误：" + JSON.toJSONString(trackLdQueryResponse));
            // 回滚消费
            ayLogisticsQuotaService.logisticQuotaConsumeConfirm(logisticsOrderSubscribeDTO, false,
                request.isNeedDeductionQuota(), monitoringNumWithholding, null,
                LogisticsMonitoringLogRequest.MONITORING_TYPE_LOOK);
            throw new LogisticsHandlesException("物流轨迹查询api失败");
        } else if (CollectionUtils.isNotEmpty(trackLdQueryResponse.getTrackInfoList())) {
            List<TrackInfo> trackInfoList = trackLdQueryResponse.getTrackInfoList();
            for (TrackInfo trackInfo : trackInfoList) {
                LogisticsInfoDTO logisticsInfo = new LogisticsInfoDTO();
                logisticsInfo.setLogisticsStoreId(getDispatcherId());
                logisticsInfo.setOutSid(trackLdQueryResponse.getMailNo());
                logisticsInfo.setCompanyCode(trackLdQueryResponse.getCpCode());
                logisticsInfo.setModified(DateUtil.parseDateString(trackInfo.getFormatTime()));
                logisticsInfo.setStatus(trackInfo.getActionCode());
                logisticsInfo.setAction(trackInfo.getActionCode());
                logisticsInfo.setDesc(trackInfo.getDescription());
                response.addLogisticsInfo(logisticsInfo);
            }
        }

        // 确认消费
        ayLogisticsQuotaService.logisticQuotaConsumeConfirm(logisticsOrderSubscribeDTO, true,
            request.isNeedDeductionQuota(), monitoringNumWithholding, null,
            LogisticsMonitoringLogRequest.MONITORING_TYPE_LOOK);

        return response;
    }

    private String getLogisticsSenderPhone(String sellerId, String sellerNick, String appName, String storeId) {
        BatchSettingGetRequest batchSettingGetRequest = new BatchSettingGetRequest();
        batchSettingGetRequest.setApp(appName);
        batchSettingGetRequest.setPlatformId(storeId);
        batchSettingGetRequest.setUserId(sellerId);
        batchSettingGetRequest.setSettings(Lists.newArrayList(LOGISTICS_SUBSCRIPTION_DEFAULT_PHONE));
        List<UserSettingDTO> userSettings = userInfoService.batchSettingGet(batchSettingGetRequest);

        if (CollectionUtils.isEmpty(userSettings)) {
            LOGGER.logInfo(sellerNick, "", "未设置寄件人手机号");
            return null;
        }
        String value = userSettings.get(0).getValue();
        return ConvertUtil.parseOrGetDefault(value, null, String.class);
    }

    @Override
    public String getDispatcherId() {
        return CommonLogisticsConstants.PLATFORM_CAINIAO;
    }
}
