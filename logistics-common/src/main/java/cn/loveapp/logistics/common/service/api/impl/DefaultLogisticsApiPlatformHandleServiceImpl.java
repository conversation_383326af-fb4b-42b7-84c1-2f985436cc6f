package cn.loveapp.logistics.common.service.api.impl;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.dto.request.AySearchLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.AySubscribeLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.response.AySearchLogisticsTraceResponse;
import cn.loveapp.logistics.common.dto.response.AySubscribeLogisticsTraceResponse;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.api.LogisticsApiPlatformHandleService;
import org.springframework.stereotype.Service;

/**
 * 默认物流接口实现类
 *
 * <AUTHOR>
 * @Date 2023/5/30 15:20
 */
@Service
public class DefaultLogisticsApiPlatformHandleServiceImpl implements LogisticsApiPlatformHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DefaultLogisticsApiPlatformHandleServiceImpl.class);


    @Override
    public String getDispatcherId() {
        return CommonLogisticsConstants.PLATFORM_DEFAULT;
    }

    @Override
    public AySubscribeLogisticsTraceResponse subscribeLogisticsTrace(AySubscribeLogisticsTraceRequest request, String logisticsStoreId, String appName) {
        AySubscribeLogisticsTraceResponse response = new AySubscribeLogisticsTraceResponse();
        response.setSuccess(true);
        return response;
    }

    @Override
    public AySearchLogisticsTraceResponse searchLogisticsTrace(AySearchLogisticsTraceRequest request, UserInfoDTO userInfoDTO, String logisticsStoreId, String appName) throws LogisticsHandlesException {
        LOGGER.logInfo("未配置api查询");
        return null;
    }
}
