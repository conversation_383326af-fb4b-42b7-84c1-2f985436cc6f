package cn.loveapp.logistics.common.service.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.taobao.TaobaoSDKService;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.dto.request.AySearchLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.AySubscribeLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.response.AySearchLogisticsTraceResponse;
import cn.loveapp.logistics.common.dto.response.AySubscribeLogisticsTraceResponse;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.UserInfoService;
import cn.loveapp.logistics.common.service.api.LogisticsApiPlatformHandleService;
import com.taobao.api.request.LogisticsTraceGetRequest;
import com.taobao.api.response.LogisticsTraceGetResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @program:logistics-services-group
 * @author: Zzz
 * @Time: 2024/4/8  16:19
 */
@Service
public class TaoLogisticsApiPlatformHandleServiceImpl implements LogisticsApiPlatformHandleService {

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private TaobaoSDKService taobaoSDKService;

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaoLogisticsApiPlatformHandleServiceImpl.class);

    @Override
    public AySubscribeLogisticsTraceResponse subscribeLogisticsTrace(AySubscribeLogisticsTraceRequest request, String logisticsStoreId, String appName) {
        AySubscribeLogisticsTraceResponse response = new AySubscribeLogisticsTraceResponse();
        response.setSuccess(true);
        return response;
    }

    @Override
    public AySearchLogisticsTraceResponse searchLogisticsTrace(AySearchLogisticsTraceRequest request, UserInfoDTO userInfoDTO, String logisticsStoreId, String appName) throws LogisticsHandlesException {

        LogisticsTraceGetRequest traceGetRequest = new LogisticsTraceGetRequest();
        traceGetRequest.setTid(Long.valueOf(request.getTid()));
        String topSession = request.getTopSession();
        String sellerNick = request.getSellerNick();
        String storeId = request.getStoreId();
        String sellerId = request.getSellerId();
        if (StringUtils.isEmpty(topSession)) {
            topSession = userInfoService.getTopSession(sellerNick, sellerId, storeId, appName);
        }
        if (null == topSession) {
            LOGGER.logError(sellerNick, "-", "topSession失效");
            return null;
        } else {
            request.setTopSession(topSession);
        }
        LogisticsTraceGetResponse traceGetResponse = taobaoSDKService.execute(traceGetRequest, topSession, appName);
        if (Objects.isNull(traceGetResponse)) {
            return null;
        }
        return convertAySearchLogisticsTraceResponse(request, traceGetResponse);
    }

    private AySearchLogisticsTraceResponse convertAySearchLogisticsTraceResponse(AySearchLogisticsTraceRequest request, LogisticsTraceGetResponse traceGetResponse) {
        AySearchLogisticsTraceResponse aySearchLogisticsTraceResponse = new AySearchLogisticsTraceResponse();
        List<LogisticsTraceGetResponse.TransitStepResult> result = traceGetResponse.getResult();
        String tid = request.getTid();
        String outSid = request.getOutSid();
        String storeId = request.getStoreId();
        String logisticStoreId = getDispatcherId();
        String sellerId = request.getSellerId();
        String appName = request.getAppName();

        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        Optional<LogisticsTraceGetResponse.TransitStepResult> first = result.stream().filter(transitStepResult -> Objects.equals(transitStepResult.getOutSid(), outSid)).findFirst();
        if (first.isPresent()) {
            LogisticsTraceGetResponse.TransitStepResult transitStepResult = first.get();
            List<LogisticsTraceGetResponse.TransitStepInfo> traceList = transitStepResult.getTraceList();
            if (CollectionUtils.isEmpty(traceList)) {
                return null;
            }
            String companyName = transitStepResult.getCompanyName();
            List<LogisticsInfoDTO> logisticsInfoDTOS = new ArrayList<>(traceList.size());
            for (LogisticsTraceGetResponse.TransitStepInfo trace : traceList) {
                LogisticsInfoDTO logisticsInfo = new LogisticsInfoDTO();
                logisticsInfo.setLogisticsStoreId(logisticStoreId);
                logisticsInfo.setPlatformId(storeId);
                logisticsInfo.setAppName(appName);
                logisticsInfo.setModified(DateUtil.parseDateString(trace.getStatusTime()));
                logisticsInfo.setStatus(trace.getAction());
                logisticsInfo.setAction(trace.getAction());
                logisticsInfo.setDesc(trace.getStatusDesc());
                logisticsInfo.setCompanyName(companyName);
                logisticsInfo.setOutSid(outSid);
                logisticsInfo.setTid(tid);
                logisticsInfo.setSellerId(sellerId);
                logisticsInfoDTOS.add(logisticsInfo);
            }
            aySearchLogisticsTraceResponse.setLogisticsInfos(logisticsInfoDTOS);
        }
        return aySearchLogisticsTraceResponse;
    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_TAO;
    }
}
