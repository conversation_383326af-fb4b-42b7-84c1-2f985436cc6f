package cn.loveapp.logistics.common.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.constant.LogisticsPackType;
import cn.loveapp.logistics.common.dto.request.LogisticsMonitoringLogRequest;
import cn.loveapp.logistics.common.dto.request.MonitoringNumWithholdingRequset;
import cn.loveapp.logistics.common.dto.response.MonitoringNumWithholdingResponse;
import cn.loveapp.logistics.common.service.AyLogisticsQuotaService;
import cn.loveapp.logistics.common.service.LogisticsSendHandleService;
import cn.loveapp.logistics.common.service.external.TradePcService;

/**
 * <AUTHOR>
 * @date 2024-10-29 10:42
 * @description: 爱用物流额度监控服务实现类
 */
@Service
public class AyLogisticsQuotaServiceImpl implements AyLogisticsQuotaService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyLogisticsQuotaServiceImpl.class);

    @Autowired
    private TradePcService tradePcService;

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private LogisticsSendHandleService logisticsSendHandleService;

    @Override
    public Integer withholdingLogisticQuota(LogisticsOrderSubscribeDTO logisticsHandle, boolean isNeedDeductionQuota) {
        String sellerId = logisticsHandle.getSellerId();
        String appName = logisticsHandle.getAppName();
        String storeId = logisticsHandle.getStoreId();
        String sellerNick = logisticsHandle.getSellerNick();
        Integer withholdingNum = 0;
        if (isNeedDeductionQuota) {
            withholdingNum = 1;
            // 获取额度预扣除
            MonitoringNumWithholdingRequset withholdingRequset = new MonitoringNumWithholdingRequset();
            withholdingRequset.setSellerNick(sellerNick);
            withholdingRequset.setStoreId(storeId);
            withholdingRequset.setAppName(appName);
            withholdingRequset.setSellerId(sellerId);
            withholdingRequset.setWithholdingNum(withholdingNum);
            withholdingRequset.setSourceApp(logisticsHandle.getSourceApp());
            LOGGER.logInfo("调用预扣额度接口：request：" + JSON.toJSONString(withholdingRequset));
            CommonApiResponse<MonitoringNumWithholdingResponse> response =
                tradePcService.getMonitoringNumWithholding(withholdingRequset);
            LOGGER.logInfo("调用预扣额度接口：response：" + JSON.toJSONString(response));
            if (response == null || !response.isSuccess()) {
                return -1;
            }
        }
        return withholdingNum;
    }

    @Override
    public void logisticQuotaConsumeConfirm(LogisticsOrderSubscribeDTO logisticsOrderSubscribeDTO,
        boolean isSearchSuccess, boolean isNeedDeductionQuota, Integer consumeNum, LogisticsPackType packType,
        String monitorType) {
        if (isNeedDeductionQuota) {
            // 成功或失败发送消息回退额度或确认消费（ons转发）
            LogisticsMonitoringLogRequest monitoringLogRequest = LogisticsMonitoringLogRequest
                .generalRequest(logisticsOrderSubscribeDTO, isSearchSuccess, consumeNum, packType, monitorType);
            logisticsSendHandleService.pushCustomNotifyMsg(
                logisticsConfig.getTradePcHost() + LogisticsMonitoringLogRequest.REQUEST_URL, monitoringLogRequest);
        }
    }
}
