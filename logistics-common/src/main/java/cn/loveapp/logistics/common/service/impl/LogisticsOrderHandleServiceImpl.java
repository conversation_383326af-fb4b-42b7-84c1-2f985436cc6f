package cn.loveapp.logistics.common.service.impl;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.loveapp.logistics.api.dto.*;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import cn.loveapp.logistics.common.dto.request.AyLogisticNumberRecognitionRequest;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.config.AyLogisticsStatusConfig;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.config.rocketmq.RocketMQLogisticsAbnormalConfig;
import cn.loveapp.logistics.common.dao.es.LogisticsOrderInfoSearchEsDao;
import cn.loveapp.logistics.common.dao.es.SearchResultDTO;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dao.mongo.LogisticsTraceInfoDao;
import cn.loveapp.logistics.common.dao.redis.LogisticsCompanyMappingRedisDao;
import cn.loveapp.logistics.common.dao.redis.LogisticsSaveLockRedisDao;
import cn.loveapp.logistics.common.dao.trade.AyLogisticsCompanyMappingDao;
import cn.loveapp.logistics.common.dto.*;
import cn.loveapp.logistics.common.dto.request.AySearchLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.LogisticsCompanyTransformRequest;
import cn.loveapp.logistics.common.dto.response.AyLogisticNumberRecognitionResponse;
import cn.loveapp.logistics.common.dto.response.AySearchLogisticsTraceResponse;
import cn.loveapp.logistics.common.dto.response.LogisticsCompanyTransformResponse;
import cn.loveapp.logistics.common.entity.db.AyLogisticsCompanyMapping;
import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.*;
import cn.loveapp.logistics.common.service.abnormalstrategy.LogisticsAbnormalStrategyChain;
import cn.loveapp.logistics.common.service.api.LogisticsApiPlatformHandleService;
import cn.loveapp.logistics.common.service.external.TradePcService;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;


/**
 * 物流单相关操作处理实现类
 *
 * <AUTHOR>
 * @Date 2023/5/30 16:52
 */
@Service
public class LogisticsOrderHandleServiceImpl implements LogisticsOrderHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderHandleServiceImpl.class);

    @Autowired
    private TradePcService tradePcService;

    @Autowired
    private LogisticsSendHandleService logisticsSendHandleService;

    @Autowired
    private LogisticsSaveLockRedisDao logisticsSaveLockRedisDao;

    @Autowired
    private LogisticsOrderInfoDao logisticsOrderInfoDao;

    @Autowired
    private RocketMQLogisticsAbnormalConfig rocketMQLogisticsAbnormalConfig;

    @Autowired
    private LogisticsTraceHandleService logisticsTraceHandleService;

    @Autowired
    private LogisticsAbnormalHandleService logisticsAbnormalHandleService;

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private LogisticsOrderInfoSearchEsDao logisticsOrderInfoSearchEsDao;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private AyLogisticsStatusConfig ayLogisticsStatusConfig;

    @Autowired
    private LogisticsCompanyMappingRedisDao logisticsCompanyMappingRedisDao;

    @Autowired
    private AyLogisticsCompanyMappingDao ayLogisticsCompanyMappingDao;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private LogisticsAbnormalStrategyChain logisticsAbnormalStrategyChain;

    @Autowired
    private LogisticsApiPlatformHandleService logisticsApiPlatformHandleService;

    @Autowired
    private LogisticsTraceInfoDao logisticsTraceInfoDao;

    @Value("${logistics.thirdLogistics.storeIds:KDNIAO,CAINIAO}")
    public List<String> thirdLogisticsStoreIds = new ArrayList<>();

    @Override
    public LogisticsCompanyInfoDTO logisticsCompanyTransform(LogisticsCompanyInfoDTO sourceCompanyInfo,
        String targetLogisticsStoreId) throws LogisticsHandlesException {
        return logisticsCompanyMappingRedisDao.getCache(sourceCompanyInfo, targetLogisticsStoreId,
            () -> logisticsCompanyTransformByMapping(sourceCompanyInfo, targetLogisticsStoreId));
    }

    @Override
    public void pullLogisticsData(LogisticsHandleBo logisticsHandleBo) throws LogisticsHandlesException {

        if (Objects.isNull(logisticsHandleBo)) {
            LOGGER.logError("物流入库异常：参数为空");
            return;
        }

        String outSid = logisticsHandleBo.getOutSid();
        String appName = logisticsHandleBo.getAppName();
        String sellerId = logisticsHandleBo.getSellerId();
        String sellerNick = logisticsHandleBo.getSellerNick();
        String storeId = logisticsHandleBo.getStoreId();

        // 新物流单
        LogisticsOrderInfo newLogisticsOrderInfo = null;

        boolean isInsertOrder = false;
        // 运单锁
        String lockValue = logisticsSaveLockRedisDao.lockLogistics(outSid, sellerId, storeId, appName);
        try {
            LogisticsOrderInfo lastLogisticsOrderInfo = logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, storeId, appName);
            isInsertOrder = lastLogisticsOrderInfo == null;

            // 生成物流单记录
            generalLogisticsOrderInfo(lastLogisticsOrderInfo, logisticsHandleBo);
            boolean hasChange = false;
            //判断是否需要执行物流异常拦截的更新，只有打标用户才需要操作
            Boolean isAbnormalAutoCheckUser = logisticsHandleBo.getIsAbnormalAutoCheckUser();
            if (isAbnormalAutoCheckUser == null) {
                isAbnormalAutoCheckUser =
                    userCenterService.isAbnormalAutoCheckUser(sellerId, sellerNick, storeId, appName);
            }
            if (isAbnormalAutoCheckUser) {
                // 异常物流判断,返回是否更新物流单newLogisticsOrderInfo
                hasChange = logisticsAbnormalHandleService.pullLogisticsAbnormalData(logisticsHandleBo);
            }

            newLogisticsOrderInfo = logisticsHandleBo.getLogisticsOrderInfo();
            // 异常物流未更新物流单，继续入库
            if(!hasChange) {
                if (isInsertOrder) {
                    logisticsOrderInfoDao.insert(newLogisticsOrderInfo);
                } else {
                    logisticsOrderInfoDao.update(newLogisticsOrderInfo);
                }

                if (CollectionUtils.isEmpty(logisticsHandleBo.getNewLogisticsTraceInfos())) {
                    // 物流轨迹推送无需更新es 减少es update次数
                    LogisticsOrderInfoSearchES logisticsSearchES = LogisticsOrderInfoSearchES.of(newLogisticsOrderInfo);
                    // erp需要全存, 需要记录发货时传进来的包裹(如果被删除了在通过物流轨迹创建出来的则没有发货包裹信息只有sku信息)
                    if (BooleanUtils.isTrue(logisticsConfig.getSaveAllEsEnable())
                        || LogisticsUtil.isTradeERP(appName)) {
                        // 全部保存es
                        logisticsOrderInfoSearchEsDao.insertOrUpdate(logisticsSearchES);
                    } else {
                        // 只保留异常物流到es记录, 不存在异常则清除
                        if (BooleanUtils.isTrue(newLogisticsOrderInfo.getHasAbnormal())) {
                            logisticsOrderInfoSearchEsDao.insertOrUpdate(logisticsSearchES);
                        } else {
                            logisticsOrderInfoSearchEsDao.deleteById(logisticsSearchES);
                        }
                    }
                }
            }

            // 如果是处理状态变更, 清除计数缓存
            if (StringUtils.isNotEmpty(logisticsHandleBo.getProcessStatus())) {
                logisticsAbnormalStrategyChain.clearCountCache(newLogisticsOrderInfo);
            }

            // 物流消息转发mc-router
            handleMcRouterMsgSend(logisticsHandleBo, appName, newLogisticsOrderInfo);
        } catch (Exception e) {
            LOGGER.logError("物流单入库失败：" + e.getMessage(), e);
            throw new LogisticsHandlesException("物流单入库失败：newLogisticsOrderInfo:" + JSON.toJSONString(newLogisticsOrderInfo), e);
        } finally {
            logisticsSaveLockRedisDao.unLockLogistics(outSid, sellerId, storeId, appName, lockValue);
        }

    }

    /**
     * 处理订单mcRouter消息转发
     *
     * @param logisticsHandleBo
     * @param appName
     * @param newLogisticsOrderInfo
     */
    private void handleMcRouterMsgSend(LogisticsHandleBo logisticsHandleBo, String appName,
        LogisticsOrderInfo newLogisticsOrderInfo) {
        // 消息转发（ERP所有物流消息都转发, 交易只有存在异常物流单的消息需要转发）
        boolean isSendOrderRouterMsg = LogisticsUtil.isTradeERP(appName);
        if (!isSendOrderRouterMsg && !Objects.isNull(newLogisticsOrderInfo)) {
            // 如果是自由打印，无需转发消息，兼容历史type为空的情况
            LOGGER.logInfo(newLogisticsOrderInfo.getSellerNick(), newLogisticsOrderInfo.getOutSid(),
                "当前订单业务类型：" + newLogisticsOrderInfo.getBusinessType());
            if (!BusinessType.custom_print.equals(newLogisticsOrderInfo.getBusinessType())) {
                List<LogisticsTraceInfo> newLogisticsTraceInfos = logisticsHandleBo.getNewLogisticsTraceInfos();
                // router转发（订单、售后相关）
                if (CollectionUtils.isNotEmpty(newLogisticsTraceInfos)) {
                    isSendOrderRouterMsg = true;
                }
            }
        }

        List<LogisticsTraceInfo> msgLogisticsTraceInfos = logisticsHandleBo.getMsgLogisticsTraceInfos();
        if (isSendOrderRouterMsg && CollectionUtils.isNotEmpty(msgLogisticsTraceInfos)) {
            // 循环去除重复的状态每个状态取最新的数据
            Map<String, LogisticsTraceInfo> statusAndLogisticsTraceInfoMap = msgLogisticsTraceInfos.stream()
                .peek(
                    s -> s.setStatus(ayLogisticsStatusConfig.getStatus(s.getAction(), s.getLogisticsStoreId()).value()))
                .collect(Collectors.toMap(LogisticsTraceInfo::getStatus, s -> s, (existing,
                    replacement) -> existing.getModified().after(replacement.getModified()) ? existing : replacement));
            for (String status : statusAndLogisticsTraceInfoMap.keySet()) {
                LogisticsTraceInfo logisticsTraceInfo = statusAndLogisticsTraceInfoMap.get(status);
                // 物流轨迹存在更新，进行推送转发处理
                logisticsSendHandleService.pushLogisticsToMcRouter(newLogisticsOrderInfo, logisticsTraceInfo);
            }
        }
    }

    @Override
    public boolean logisticsInfoUpdate(LogisticsOrderChangeDTO logisticsUpdate) throws LogisticsHandlesException {

        String outSid = logisticsUpdate.getOutSid();
        String storeId = logisticsUpdate.getStoreId();
        String sellerId = logisticsUpdate.getSellerId();
        String appName = logisticsUpdate.getAppName();

        LogisticsOrderInfo logisticsOrderInfo = logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, storeId, appName);
        if (Objects.isNull(logisticsOrderInfo)) {
            LOGGER.logError(sellerId, outSid, "运单物流表不存在，跳过,运单号：" + outSid);
            throw new LogisticsHandlesException("运单物流表不存在,运单号：" + outSid);
        }

        LogisticsHandleBo logisticsHandle = LogisticsHandleBo.generalLogisticsHandleBo(logisticsOrderInfo, logisticsUpdate);
        if (Objects.isNull(logisticsHandle)) {
            LOGGER.logError(sellerId, outSid, "无可更新信息，跳过,运单号：" + outSid);
            throw new LogisticsHandlesException("无可更新信息,运单号：" + outSid);
        }
        // 更新入库
        pullLogisticsData(logisticsHandle);
        return true;
    }

    @Override
    public boolean logisticsInfoUpdateByOrderInfo(OrderInfoChangeDTO logisticsOrderChangeInfo)
        throws LogisticsHandlesException {

        String sellerId = logisticsOrderChangeInfo.getSellerId();
        String storeId = logisticsOrderChangeInfo.getStoreId();
        String appName = logisticsOrderChangeInfo.getAppName();
        List<String> outSids = logisticsOrderChangeInfo.getOutSids();
        List<LogisticsOrderInfo> logisticsOrderInfos =
            logisticsOrderInfoDao.queryByOutSidListAndSeller(outSids, sellerId, storeId, appName);
        if (CollectionUtils.isEmpty(logisticsOrderInfos)) {
            return false;
        }

        for (LogisticsOrderInfo logisticsOrderInfo : logisticsOrderInfos) {
            LogisticsHandleBo logisticsHandleBo =
                LogisticsHandleBo.generalLogisticsHandleBo(logisticsOrderInfo, logisticsOrderChangeInfo);
            if (logisticsHandleBo == null) {
                LOGGER.logError(sellerId, "", "无可更新信息，跳过,运单号：" + JSON.toJSONString(outSids));
                throw new LogisticsHandlesException("无可更新信息,运单号：" + JSON.toJSONString(outSids));
            }

            // 更新入库
            pullLogisticsData(logisticsHandleBo);
        }

        return false;
    }

    @Override
    public AyLogisticsOrderInfoSearchListAndAggDto logisticsOrderInfoListQueryByLimit(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO) throws LogisticsHandlesException {
        AyLogisticsOrderInfoSearchListAndAggDto listAndAggDto = new AyLogisticsOrderInfoSearchListAndAggDto();
        listAndAggDto.setHasNext(false);
        Integer totalResults = 0;
        if (logisticsQuery.getLimit() > 0) {
            SearchResultDTO<LogisticsOrderInfoSearchES> searchESSearchResultDTO = logisticsOrderInfoSearchEsDao.logisticsListGetQueryByLimit(logisticsQuery, userInfoDTO);
            List<LogisticsOrderInfoSearchES> searchESList = searchESSearchResultDTO.getSearchResults();
            listAndAggDto.setLogisticsOrderInfoSearchESList(searchESList);
            totalResults = CollectionUtils.size(searchESList);
            if (totalResults >= logisticsQuery.getLimit() || logisticsQuery.getStart() > 0) {
                // todo 后续根据压力情况考虑是否需要增加查询数量缓存
                totalResults = logisticsOrderInfoSearchEsDao.queryCountFromDB(logisticsQuery, userInfoDTO);
            }
            listAndAggDto.setHasNext(totalResults.equals(logisticsQuery.getLimit()));
            listAndAggDto.setSearchSortValues(searchESSearchResultDTO.getSearchAfterSortValues());
        } else {
            totalResults = logisticsOrderInfoSearchEsDao.queryCountFromDB(logisticsQuery, userInfoDTO);
        }

        listAndAggDto.setTotalResults(totalResults);
        return listAndAggDto;
    }

    @Override
    public List<LogisticsPackInfo> logisticsPackListGetByOutSidList(LogisticsQueryDTO logisticsQuery, List<String> outSidList, UserInfoDTO userInfoDTO, Boolean isSearchTrace) throws LogisticsHandlesException {
        List<LogisticsOrderInfo> logisticsOrderInfos = logisticsOrderInfoDao.queryByOutSidAndSeller(outSidList, userInfoDTO);
        if (CollectionUtils.isEmpty(logisticsOrderInfos)) {
            return null;
        }

        List<LogisticsPackInfo> logisticsPackInfos = new ArrayList<>(logisticsOrderInfos.size());

        Map<String, List<LogisticsOrderInfo>> logisticsOrderInfoMap =
            logisticsOrderInfos.stream().collect(Collectors.groupingBy(LogisticsOrderInfo::getOutSid));
        Set<String> outSidSet = Sets.newHashSet();
        for (String outSid : outSidList) {
            if (outSidSet.contains(outSid)) {
                continue;
            }
            outSidSet.add(outSid);
            List<LogisticsOrderInfo> orderInfos = logisticsOrderInfoMap.get(outSid);
            if (CollectionUtils.isEmpty(orderInfos)) {
                continue;
            }
            for (LogisticsOrderInfo orderInfo : orderInfos) {
                List<LogisticsTraceInfo> logisticsTraceInfos = null;
                if (BooleanUtils.isTrue(isSearchTrace)) {
                    logisticsTraceInfos = logisticsTraceHandleService.queryLogisticsTraceList(orderInfo);
                }
                LogisticsPackInfo logisticsPackInfo = LogisticsPackInfo.of(orderInfo, logisticsTraceInfos, logisticsQuery.getTraceSortDirection(), logisticsQuery.isDistinctWithStatusField(), logisticsQuery.getIncludeProcessedAbnormal());
                if (Objects.isNull(logisticsPackInfos)) {
                    continue;
                }
                logisticsPackInfos.add(logisticsPackInfo);
            }
        }

        return logisticsPackInfos;
    }

    /**
     * 生成物流单信息
     *
     * @param lastLogisticsOrderInfo
     * @param logisticsHandleBo
     * @return
     */
    private LogisticsOrderInfo generalLogisticsOrderInfo(LogisticsOrderInfo lastLogisticsOrderInfo, LogisticsHandleBo logisticsHandleBo) {
        Date now = DateUtil.convertLocalDateTimetoDate(LocalDateTime.now());
        LogisticsOrderInfo newLogisticsOrderInfo = new LogisticsOrderInfo();
        LogisticsOrderInfo.BusinessInfo businessInfo = null;
        if (!Objects.isNull(lastLogisticsOrderInfo)) {
            BeanUtils.copyProperties(lastLogisticsOrderInfo, newLogisticsOrderInfo);
            if (lastLogisticsOrderInfo != null && CollectionUtils.isNotEmpty(lastLogisticsOrderInfo.getLogisticsStatusList())) {
                // 此处把老的放入， 比如淘宝同时存在正常物流和菜鸟订阅物流, 就可能导致这个状态一致缺失从而出现物流消息入库时间过滤条件失效,导致物流轨迹重复入库
                newLogisticsOrderInfo.setLogisticsStatusList(Lists.newArrayList(lastLogisticsOrderInfo.getLogisticsStatusList()));
            }

            businessInfo = lastLogisticsOrderInfo.getBusinessInfo();
            if (businessInfo.getConsignTime() == null && logisticsHandleBo.getConsignTime() != null) {
                businessInfo.setConsignTime(logisticsHandleBo.getConsignTime());
            }

            if (StringUtils.isEmpty(businessInfo.getBuyerNick())
                && StringUtils.isNotEmpty(logisticsHandleBo.getBuyerNick())) {
                businessInfo.setBuyerNick(logisticsHandleBo.getBuyerNick());
            }

            if (StringUtils.isEmpty(businessInfo.getBuyerOpenUid())
                && StringUtils.isNotEmpty(logisticsHandleBo.getBuyerOpenUid())) {
                businessInfo.setBuyerOpenUid(logisticsHandleBo.getBuyerOpenUid());
            }

            if (newLogisticsOrderInfo.getCreated() == null) {
                // 兼容老数据
                newLogisticsOrderInfo.setCreated(newLogisticsOrderInfo.getGmtCreate());
            }
            // 如果是指定平台的物流则入库时需要需要将物流公司转换成爱用默认的物流公司（默认以快递鸟老数据为样本，兼容菜鸟及后续其他平台）
            if (thirdLogisticsStoreIds.contains(logisticsHandleBo.getAssociatedLogisticsStoreId())) {
                // 将三方平台返回的物流公司信息转成爱用默认的物流公司信息进行入库
                setLogisticsCompanyInfo(logisticsHandleBo, newLogisticsOrderInfo);
                newLogisticsOrderInfo.setSaveLogisticsStoreId(logisticsHandleBo.getAssociatedLogisticsStoreId());
            }
        } else {
            newLogisticsOrderInfo.setGmtCreate(now);
            newLogisticsOrderInfo.setCreated(now);
            newLogisticsOrderInfo.setGmtModified(now);
            newLogisticsOrderInfo.setId(newLogisticsOrderInfo.getId());
            newLogisticsOrderInfo.setOutSid(logisticsHandleBo.getOutSid());
            newLogisticsOrderInfo.setSellerId(logisticsHandleBo.getSellerId());
            newLogisticsOrderInfo.setStoreId(logisticsHandleBo.getStoreId());
            newLogisticsOrderInfo.setAppName(StringUtils.defaultString(logisticsHandleBo.getAppName(), CommonAppConstants.APP_TRADE));
            businessInfo = new LogisticsOrderInfo.BusinessInfo();
            businessInfo.setConsignTime(logisticsHandleBo.getConsignTime());
            businessInfo.setBuyerNick(logisticsHandleBo.getBuyerNick());
            businessInfo.setBuyerOpenUid(logisticsHandleBo.getBuyerOpenUid());
            // 第一次更新
            logisticsHandleBo.setIsOrderInfoFirstUpdate(true);
            setLogisticsCompanyInfo(logisticsHandleBo, newLogisticsOrderInfo);
        }

        if (StringUtils.isEmpty(newLogisticsOrderInfo.getSellerNick())) {
            UserInfoRequest userInfoRequest = new UserInfoRequest();
            userInfoRequest.setSellerId(newLogisticsOrderInfo.getSellerId());
            userInfoRequest.setPlatformId(newLogisticsOrderInfo.getStoreId());
            userInfoRequest.setApp(newLogisticsOrderInfo.getAppName());
            UserInfoResponse sellerInfo = userInfoService.getSellerInfo(userInfoRequest);
            if (sellerInfo != null) {
                newLogisticsOrderInfo.setSellerNick(sellerInfo.getSellerNick());
            }
        }

        // 处理业务订单信息
        handleBusinessOrderInfo(logisticsHandleBo, businessInfo);

        newLogisticsOrderInfo.setBusinessInfo(businessInfo);
        newLogisticsOrderInfo.setBusinessType(logisticsHandleBo.getBusinessType());

        // 更新物流订阅信息
        if (logisticsHandleBo.getIsSubscribeSuccess() != null && BooleanUtils.isTrue(logisticsHandleBo.getIsSubscribeSuccess())) {
            LogisticsOrderInfo.SubscribeInfo subscribeInfo = newLogisticsOrderInfo.getSubscribeInfo();
            if (Objects.isNull(subscribeInfo)) {
                subscribeInfo = new LogisticsOrderInfo.SubscribeInfo();
                subscribeInfo.setFirstModified(now);
            }
            if (subscribeInfo.getFirstModified() == null) {
                // 兼容老数据
                subscribeInfo.setFirstModified(newLogisticsOrderInfo.getGmtModified());
            }
            subscribeInfo.setLastModified(now);
            subscribeInfo.setIsSuccess(logisticsHandleBo.getIsSubscribeSuccess());
            subscribeInfo.setErrorMsg(logisticsHandleBo.getErrorMsg());
            newLogisticsOrderInfo.setSubscribeInfo(subscribeInfo);
        }
        newLogisticsOrderInfo.setModified(now);

        if (!Objects.isNull(logisticsHandleBo.getAssociatedLogisticsStoreId())) {
            newLogisticsOrderInfo.setSaveLogisticsStoreId(logisticsHandleBo.getAssociatedLogisticsStoreId());
        }

        if (logisticsHandleBo.getIsTagIntercepted() != null) {
            // 更新拦截标记
            newLogisticsOrderInfo.setIsTagIntercepted(logisticsHandleBo.getIsTagIntercepted());
            newLogisticsOrderInfo.setTagInterceptedModified(logisticsHandleBo.getTagInterceptedModified());
        }

        if (logisticsHandleBo.getProcessStatus() != null) {
            // 更新处理状态
            logisticsAbnormalHandleService.setLogisticsProcessStatus(newLogisticsOrderInfo, logisticsHandleBo.getProcessStatus());
        }

        /**
         * 兼容开启物流存单前旧数据
         *  1 上次物流轨迹状态不为空（已入库过物流单），但保存的物流轨迹状态列表为空，则查询物流轨迹补齐
         *  2 物流轨迹插入时未搜到物流单，新增时会查询所有物流轨迹（allLogisticsTraceInfos ！= null时）
         */
        if ((CollectionUtils.isEmpty(newLogisticsOrderInfo.getLogisticsStatusList())
            && StringUtils.isNotEmpty(newLogisticsOrderInfo.getLastAction())) || CollectionUtils.isNotEmpty(logisticsHandleBo.getAllLogisticsTraceInfos())) {
            List<LogisticsTraceInfo> allDbLogisticsTraceInfos = ConvertUtil.getOrDefault(logisticsHandleBo.getAllLogisticsTraceInfos(), () -> logisticsTraceHandleService.queryLogisticsTraceList(newLogisticsOrderInfo));
            for (LogisticsTraceInfo traceInfo : allDbLogisticsTraceInfos) {
                String status = traceInfo.getStatus();
                // 兼容老数据判断
                if (StringUtils.isNotEmpty(status)) {
                    newLogisticsOrderInfo.setLogisticsStatusList(status);
                } else {
                    AyLogisticsStatus ayStatus = ayLogisticsStatusConfig.getStatus(traceInfo.getAction(), traceInfo.getLogisticsStoreId());
                    newLogisticsOrderInfo.setLogisticsStatusList(ayStatus.value());
                }
            }
        }

        // 获取物流单关联的新增物流轨迹
        List<LogisticsTraceInfo> newAndDbTraceList = Lists.newArrayList();
        List<LogisticsTraceInfo> newLogisticsTraceInfos = logisticsHandleBo.getNewLogisticsTraceInfos();
        if (CollectionUtils.isNotEmpty(newLogisticsTraceInfos)) {
            newAndDbTraceList.addAll(newLogisticsTraceInfos);
        }
        // 如果未比较物流轨迹的modified,最新的物流动态不一定是消息中的物流轨迹,需要将消息中的和库中的轨迹一起比较得出最新物流动态
        if (!logisticsHandleBo.isCheckModified()) {
            List<LogisticsTraceInfo> logisticsTraceInfosInDb = logisticsTraceHandleService.queryLogisticsTraceList(newLogisticsOrderInfo);
            newAndDbTraceList.addAll(logisticsTraceInfosInDb);
        }
        if (CollectionUtils.isNotEmpty(newAndDbTraceList)) {
            // 根据物流轨迹更新物流单
            LogisticsTraceInfo lastTrace = newAndDbTraceList.stream()
                .max(Comparator.comparing(LogisticsTraceInfo::getModified))
                .orElse(null);
            if (lastTrace != null) {
                newLogisticsOrderInfo.setLastAction(
                    ayLogisticsStatusConfig.getStatus(lastTrace.getAction(), lastTrace.getLogisticsStoreId()).value());
                newLogisticsOrderInfo.setLastActionModified(lastTrace.getModified());
                newLogisticsOrderInfo.setLastTraceDesc(lastTrace.getDesc());
            }

            List<String> statusList = Lists.newArrayList();
            for (LogisticsTraceInfo logisticsTraceInfo : newAndDbTraceList) {
                String status = logisticsTraceInfo.getStatus();
                if (StringUtils.isEmpty(status)) {
                    statusList.add(ayLogisticsStatusConfig.getStatus(logisticsTraceInfo.getAction(), logisticsTraceInfo.getLogisticsStoreId()).value());
                } else {
                    statusList.add(status);
                }
            }
            // 保存新增轨迹状态到物流单
            newLogisticsOrderInfo.setLogisticsStatusList(statusList);
        }
        logisticsHandleBo.setLogisticsOrderInfo(newLogisticsOrderInfo);

        return newLogisticsOrderInfo;
    }

    /**
     * 处理业务订单信息
     *
     * @param logisticsHandleBo
     * @param businessInfo
     */
    private static void handleBusinessOrderInfo(LogisticsHandleBo logisticsHandleBo,
        LogisticsOrderInfo.BusinessInfo businessInfo) {
        businessInfo.addTidList(logisticsHandleBo.getTids());
        businessInfo.addRefundIdList(logisticsHandleBo.getRefundIds());
        List<OrderInfoDTO> orderInfoChangeList = logisticsHandleBo.getOrderInfoList();
        if (CollectionUtils.isNotEmpty(orderInfoChangeList)) {
            Map<String, OrderInfoDTO> tidAndOrderInfoChange =
                orderInfoChangeList.stream().collect(Collectors.toMap(OrderInfoDTO::getTid, Function.identity()));

            List<LogisticsOrderInfo.OrderInfo> dbOrderInfoList = businessInfo.getOrderInfoList();
            if (CollectionUtils.isNotEmpty(dbOrderInfoList)) {
                for (LogisticsOrderInfo.OrderInfo dbOrderInfo : dbOrderInfoList) {
                    String tid = dbOrderInfo.getTid();
                    OrderInfoDTO orderInfoDTO = tidAndOrderInfoChange.get(tid);
                    if (orderInfoDTO != null) {
                        if (orderInfoDTO.getSellerFlag() != null) {
                            dbOrderInfo.setSellerFlag(orderInfoDTO.getSellerFlag());
                        }

                        if (orderInfoDTO.getOrderAyCustomFlag() != null) {
                            dbOrderInfo.setOrderAyCustomFlag(orderInfoDTO.getOrderAyCustomFlag());
                        }

                        if (orderInfoDTO.getSellerMemo() != null) {
                            dbOrderInfo.setSellerMemo(orderInfoDTO.getSellerMemo());
                        }

                        if (orderInfoDTO.getBuyerMessage() != null) {
                            dbOrderInfo.setBuyerMessage(orderInfoDTO.getBuyerMessage());
                        }
                        if (BooleanUtils.isTrue(orderInfoDTO.getIsRefund())) {
                            dbOrderInfo.setIsRefund(orderInfoDTO.getIsRefund());
                        }

                        dbOrderInfo.setRefundCreatedTime(orderInfoDTO.getRefundCreatedTime());
                        List<LogisticsOrderInfo.OrderSkuInfo> orderSkuInfos = Lists.newArrayList();
                        if (CollectionUtils.isNotEmpty(orderInfoDTO.getSkuInfoList())) {
                            for (OrderSkuInfoDTO orderSkuInfoDTO : orderInfoDTO.getSkuInfoList()) {
                                LogisticsOrderInfo.OrderSkuInfo orderSkuInfo = new LogisticsOrderInfo.OrderSkuInfo();
                                orderSkuInfo.setSkuId(orderSkuInfoDTO.getSkuId());
                                orderSkuInfo.setSkuName(orderSkuInfoDTO.getSkuName());
                                orderSkuInfo.setPicUrl(orderSkuInfoDTO.getPicUrl());
                                orderSkuInfo.setOuterSkuId(orderSkuInfoDTO.getOuterSkuId());
                                orderSkuInfo.setNum(orderSkuInfoDTO.getNum());
                                orderSkuInfos.add(orderSkuInfo);
                            }

                            dbOrderInfo.setSkuInfos(orderSkuInfos);
                        }
                    }
                }
            } else {
                // 正常物流单在订阅物流的时候就有了，预发的订单则是物流来的时候创建的
                List<LogisticsOrderInfo.OrderInfo> orderInfoList = Lists.newArrayList();
                Set<String> tidList = businessInfo.getTidList();
                if (CollectionUtils.isNotEmpty(tidList) && CollectionUtils.isNotEmpty(orderInfoChangeList)) {
                    for (OrderInfoDTO orderInfoDTO : orderInfoChangeList) {
                        if (tidList.contains(orderInfoDTO.getTid())) {
                            LogisticsOrderInfo.OrderInfo orderInfo = new LogisticsOrderInfo.OrderInfo();
                            orderInfo.setTid(orderInfoDTO.getTid());
                            orderInfo.setSellerFlag(orderInfoDTO.getSellerFlag());
                            orderInfo.setOrderAyCustomFlag(orderInfoDTO.getOrderAyCustomFlag());
                            orderInfo.setSellerMemo(orderInfoDTO.getSellerMemo());
                            orderInfo.setBuyerMessage(orderInfoDTO.getBuyerMessage());
                            if (BooleanUtils.isTrue(orderInfoDTO.getIsRefund())) {
                                orderInfo.setIsRefund(orderInfoDTO.getIsRefund());
                            }

                            orderInfo.setRefundCreatedTime(orderInfoDTO.getRefundCreatedTime());
                            // sku信息
                            List<LogisticsOrderInfo.OrderSkuInfo> orderSkuInfos = Lists.newArrayList();
                            if (CollectionUtils.isNotEmpty(orderInfoDTO.getSkuInfoList())) {
                                for (OrderSkuInfoDTO orderSkuInfoDTO : orderInfoDTO.getSkuInfoList()) {
                                    LogisticsOrderInfo.OrderSkuInfo orderSkuInfo =
                                        new LogisticsOrderInfo.OrderSkuInfo();
                                    orderSkuInfo.setSkuId(orderSkuInfoDTO.getSkuId());
                                    orderSkuInfo.setSkuName(orderSkuInfoDTO.getSkuName());
                                    orderSkuInfo.setPicUrl(orderSkuInfoDTO.getPicUrl());
                                    orderSkuInfo.setOuterSkuId(orderSkuInfoDTO.getOuterSkuId());
                                    orderSkuInfo.setNum(orderSkuInfoDTO.getNum());
                                    orderSkuInfos.add(orderSkuInfo);
                                }
                            }

                            orderInfo.setSkuInfos(orderSkuInfos);
                            orderInfoList.add(orderInfo);
                        }
                    }
                }

                businessInfo.setOrderInfoList(orderInfoList);
            }
        }
    }

    /**
     * 设置物流公司信息
     *
     * @param logisticsHandleBo
     * @param newLogisticsOrderInfo
     */
    private void setLogisticsCompanyInfo(LogisticsHandleBo logisticsHandleBo,
        LogisticsOrderInfo newLogisticsOrderInfo) {
        LogisticsCompanyMappingDTO logisticsCompanyMappingDTO =
            handelLogisticsCompanyMapping(logisticsHandleBo.getCompanyCode(), logisticsHandleBo.getCompanyName(),
                logisticsHandleBo.getAssociatedLogisticsStoreId(), CommonLogisticsConstants.PLATFORM_DEFAULT);

        newLogisticsOrderInfo.setCompanyCode(logisticsCompanyMappingDTO.getTargetCompanyCode());
        newLogisticsOrderInfo.setCompanyName(logisticsCompanyMappingDTO.getTargetCompanyName());
    }

    @Override
    public boolean checkLogisticsIsEnd(LogisticsOrderInfo logisticsOrderInfo) {
        String lastAction = logisticsOrderInfo.getLastAction();
        Date gmtCreate = logisticsOrderInfo.getGmtCreate();
        if (gmtCreate == null) {
            return false;
        }
        long days = ChronoUnit.DAYS.between(DateUtil.parseDate(gmtCreate), LocalDateTime.now());
        if (!StringUtils.isEmpty(lastAction) && AyLogisticsStatus.DELIVERED_LIST.contains(lastAction)) {
            // 当前物流轨迹状态已结束，不进行发送
            LOGGER.logInfo("物流单已结束，无需发送异常校验消息");
            return true;
        } else if (days > rocketMQLogisticsAbnormalConfig.getMaxCheckDays()) {
            LOGGER.logWarn("物流单已超时，无需发送异常校验消息：当前状态：" + logisticsOrderInfo.getLastAction());
            return true;
        }

        return false;
    }

    /**
     * 通过交易Rpc接口转换物流公司
     * @param sourceCompanyInfo
     * @param targetLogisticsStoreId
     * @return
     * @throws LogisticsHandlesException
     */
    @Deprecated
    private LogisticsCompanyInfoDTO logisticsCompanyTransformByTradeRpc(LogisticsCompanyInfoDTO sourceCompanyInfo, String targetLogisticsStoreId) throws LogisticsHandlesException{

        if (Objects.isNull(sourceCompanyInfo)) {
            LOGGER.logError("物流公司转换参数为空");
            throw new LogisticsHandlesException("物流公司转换参数为空");
        }

        if (StringUtils.isAllEmpty(sourceCompanyInfo.getCompanyCode(),sourceCompanyInfo.getCompanyName())) {
            LOGGER.logError("物流公司名或物流公司代码为空");
            throw new LogisticsHandlesException("参数异常：物流公司名或物流公司代码为空");
        }

        LogisticsCompanyTransformRequest request = new LogisticsCompanyTransformRequest();
        request.setSourceLogisticStoreId(sourceCompanyInfo.getLogisticsStoreId());
        request.setTargetLogisticStoreId(targetLogisticsStoreId);
        request.setLogisticsCompanyName(sourceCompanyInfo.getCompanyName());
        request.setLogisticsCompanyCode(sourceCompanyInfo.getCompanyCode());
        request.setLogisticsCompanyId(sourceCompanyInfo.getCompanyId());

        CommonApiResponse<LogisticsCompanyTransformResponse> response = tradePcService.logisticsTransformCompanyInfo(request);

        if (response == null) {
            throw new LogisticsHandlesException("转换接口异常:返回值为空" + ",转换参数：" + JSON.toJSONString(sourceCompanyInfo));
        }

        if (response.isSuccess()) {
            LogisticsCompanyTransformResponse body = response.getBody();
            LogisticsCompanyInfoDTO logisticsCompanyInfoDTO = new LogisticsCompanyInfoDTO();
            logisticsCompanyInfoDTO.setCompanyCode(body.getLogisticsCompanyCode());
            logisticsCompanyInfoDTO.setCompanyName(body.getLogisticsCompanyName());
            logisticsCompanyInfoDTO.setLogisticsStoreId(body.getLogisticStoreId());
            return logisticsCompanyInfoDTO;
        } else {
            LOGGER.logError("物流公司转换接口异常：" + response.getMessage() + ",转换参数：" + JSON.toJSONString(sourceCompanyInfo));
        }
        return null;
    }

    @Override
    public boolean checkOverdueUpdateAndPushTraceMessage(LogisticsHandleBo logisticsHandleBo) {
        LogisticsOrderInfo logisticsOrderInfo = logisticsHandleBo.getLogisticsOrderInfo();
        String saveLogisticsStoreId = logisticsOrderInfo.getSaveLogisticsStoreId();
        Date lastModified = logisticsOrderInfo.getLastActionModified() == null ? logisticsOrderInfo.getModified() : logisticsOrderInfo.getLastActionModified();
        if (lastModified == null || StringUtils.isEmpty(saveLogisticsStoreId)) {
            return false;
        }

        String sellerId = logisticsOrderInfo.getSellerId();
        String sellerNick = logisticsOrderInfo.getSellerNick();
        String storeId = logisticsOrderInfo.getStoreId();
        String appName = logisticsOrderInfo.getAppName();
        String outSid = logisticsOrderInfo.getOutSid();

        int notUpdatedTimeoutDays = logisticsConfig.getOverdueUpdateTimeoutDays();
        long updateGapDays = ChronoUnit.DAYS.between(DateUtil.parseDate(lastModified), LocalDateTime.now());
        if (updateGapDays > notUpdatedTimeoutDays) {
            if (logisticsConfig.getAbnormalPullApuLogisticsStoreIdList().contains(saveLogisticsStoreId)) {
                LOGGER.logInfo(logisticsOrderInfo.getSellerNick(), logisticsOrderInfo.getSellerId(), notUpdatedTimeoutDays + "天物流未更新,拉取api补齐物流信息");

                AySearchLogisticsTraceResponse aySearchLogisticsTraceResponse = pullLogisticsInfoFromApi(logisticsHandleBo, saveLogisticsStoreId, logisticsOrderInfo.getAppName());

                if (aySearchLogisticsTraceResponse != null && CollectionUtils.isNotEmpty(aySearchLogisticsTraceResponse.getLogisticsInfos())) {

                    // 处理物流轨迹
                    List<LogisticsInfoDTO> logisticsTraceFromApi = aySearchLogisticsTraceResponse.getLogisticsInfos();
                    LogisticsOrderInfo.BusinessInfo businessInfo = logisticsOrderInfo.getBusinessInfo();
                    List<LogisticsTraceInfo> traceInfosInDb = logisticsTraceInfoDao.queryByTid(Lists.newArrayList(businessInfo.getTidList()), sellerId, storeId, appName, saveLogisticsStoreId);
                    Set<Date> traceModifiedSet = traceInfosInDb.stream().map(LogisticsTraceInfo::getModified).collect(Collectors.toSet());
                    List<LogisticsInfoDTO> needSaveLogisticsTrace = logisticsTraceFromApi.stream()
                        .filter(trace -> !traceModifiedSet.contains(trace.getModified())).collect(Collectors.toList());

                    LOGGER.logInfo(sellerNick, outSid, "拉取api补齐的物流轨迹 => " + JSON.toJSONString(needSaveLogisticsTrace));
                    // 发送物流轨迹入库消息
                    if (CollectionUtils.isNotEmpty(needSaveLogisticsTrace)) {
                        logisticsSendHandleService.pushLogisticsSaveMsg(needSaveLogisticsTrace, false, saveLogisticsStoreId, appName);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public LogisticsCompanyMappingDTO handelLogisticsCompanyMapping(String companyCpCode, String companyName,
        String logisticsStoreId, String targetLogisticsStoreId) {
        LogisticsCompanyInfoDTO logisticsCompanyInfoDTO = new LogisticsCompanyInfoDTO();
        LogisticsCompanyMappingDTO logisticsCompanyMappingDTO = new LogisticsCompanyMappingDTO();
        logisticsCompanyInfoDTO.setCompanyCode(companyCpCode);
        logisticsCompanyInfoDTO.setCompanyName(companyName);
        logisticsCompanyInfoDTO.setLogisticsStoreId(logisticsStoreId);
        try {
            LogisticsCompanyInfoDTO targetCompanyInfo =
                logisticsCompanyTransform(logisticsCompanyInfoDTO, targetLogisticsStoreId);
            if (!Objects.isNull(targetCompanyInfo)) {
                logisticsCompanyMappingDTO.setTargetCompanyCode(targetCompanyInfo.getCompanyCode());
                logisticsCompanyMappingDTO.setTargetCompanyName(targetCompanyInfo.getCompanyName());
                return logisticsCompanyMappingDTO;
            }
            LOGGER.logInfo(companyCpCode, companyName, "无物流平台: " + logisticsStoreId + " 映射关系");
        } catch (LogisticsHandlesException e) {
            LOGGER.logError("获取映射物流公司失败，使用原始物流公司信息, companyCpCode = " + companyCpCode + ", " + e.getMessage(), e);
        }
        logisticsCompanyMappingDTO.setTargetCompanyCode(companyCpCode);
        logisticsCompanyMappingDTO.setTargetCompanyName(companyName);
        return logisticsCompanyMappingDTO;
    }

    /**
     * 拉取api获取全量物流信息
     * @param logisticsHandleBo
     * @param logisticsStoreId
     * @param appName
     */
    private AySearchLogisticsTraceResponse pullLogisticsInfoFromApi(LogisticsHandleBo logisticsHandleBo, String logisticsStoreId, String appName) {
        LogisticsOrderInfo logisticsOrderInfo = logisticsHandleBo.getLogisticsOrderInfo();
        String sellerId = logisticsOrderInfo.getSellerId();
        String sellerNick = logisticsOrderInfo.getSellerNick();
        String outSid = logisticsOrderInfo.getOutSid();
        String storeId = logisticsOrderInfo.getStoreId();
        LogisticsOrderInfo.BusinessInfo businessInfo = logisticsOrderInfo.getBusinessInfo();

        AySearchLogisticsTraceResponse aySearchLogisticsTraceResponse = null;
        if (businessInfo != null && CollectionUtils.isNotEmpty(businessInfo.getTidList())) {

            String tid = businessInfo.getTidList().stream().findFirst().get();
            AySearchLogisticsTraceRequest aySearchLogisticsTraceRequest = new AySearchLogisticsTraceRequest();
            aySearchLogisticsTraceRequest.setAppName(appName);
            aySearchLogisticsTraceRequest.setStoreId(storeId);
            aySearchLogisticsTraceRequest.setOutSid(outSid);
            aySearchLogisticsTraceRequest.setTid(tid);
            aySearchLogisticsTraceRequest.setSellerId(sellerId);
            aySearchLogisticsTraceRequest.setSellerNick(sellerNick);

            UserInfoDTO userInfoDTO = new UserInfoDTO();
            userInfoDTO.setSellerId(sellerId);
            userInfoDTO.setStoreId(storeId);
            userInfoDTO.setAppName(appName);
            userInfoDTO.setNick(sellerNick);
            try {
                // 调用api获取轨迹
                aySearchLogisticsTraceResponse =
                    logisticsApiPlatformHandleService.searchLogisticsTrace(aySearchLogisticsTraceRequest, userInfoDTO, logisticsStoreId, appName);
            } catch (Exception e) {
                LOGGER.logError(sellerNick, outSid, "拉取api物流信息异常:" + e.getMessage(), e);
            }
        }
        return aySearchLogisticsTraceResponse;
    }

    /**
     * 通过物流映射表转换物流公司(将多平台的物流公司转成需要订阅的物流公司cpCode)
     * @param sourceCompanyInfo
     * @param targetLogisticsStoreId
     * @return
     * @throws LogisticsHandlesException
     */
    private LogisticsCompanyInfoDTO logisticsCompanyTransformByMapping(LogisticsCompanyInfoDTO sourceCompanyInfo, String targetLogisticsStoreId) {
        String logisticsStoreId = sourceCompanyInfo.getLogisticsStoreId();
        String companyId = sourceCompanyInfo.getCompanyId();
        String companyCode = sourceCompanyInfo.getCompanyCode();
        String companyName = sourceCompanyInfo.getCompanyName();
        String outSid = sourceCompanyInfo.getOutSid();
        String logisticsAppName = sourceCompanyInfo.getLogisticsAppName();

        String sourceCompany = ConvertUtil.firstNotNull(companyId, companyCode, companyName);

        if (StringUtils.isAllEmpty(sourceCompany, logisticsStoreId, targetLogisticsStoreId)) {
            return null;
        }

        AyLogisticsCompanyMapping sourceMapping = null;
        if (companyCode != null) {
            sourceMapping = ayLogisticsCompanyMappingDao.queryByCompanyCodeAndStoreId(companyCode, logisticsStoreId);
        }
        if (sourceMapping == null && companyName != null) {
            sourceMapping = ayLogisticsCompanyMappingDao.queryByCompanyNameAndStoreId(companyName, logisticsStoreId);
        }
        if (sourceMapping == null && companyId != null) {
            sourceMapping = ayLogisticsCompanyMappingDao.queryByCompanyIdAndStoreId(companyId, logisticsStoreId);
        }

        LogisticsCompanyInfoDTO targetLogisticsCompany = new LogisticsCompanyInfoDTO();
        targetLogisticsCompany.setLogisticsStoreId(targetLogisticsStoreId);
        if (sourceMapping == null) {
            return handleNewLogisticsMapping(targetLogisticsStoreId, outSid, logisticsAppName, logisticsStoreId,
                companyId, companyCode, companyName, targetLogisticsCompany);
        } else if (!Objects.isNull(sourceMapping.getFlag())) {
            if (Objects.equals(AyLogisticsCompanyMapping.FLAG_PREPARE, sourceMapping.getFlag())) {
                // flag=1 表示老旧的待手动处理数据
                // 将状态为1的老数据进行映射修补 修补失败则标记为手动处理flag = 2
                LOGGER.logInfo("修补待处理的物流映射");
                return handleOldPendingMappingData(sourceMapping, targetLogisticsStoreId, outSid,
                    targetLogisticsCompany, logisticsAppName);
            } else if (Objects.equals(AyLogisticsCompanyMapping.FLAG_MANUAL, sourceMapping.getFlag())) {
                return null;
            }
        }

        // 来源物流平台与目标平台相同，直接返回
        if (logisticsStoreId.equals(targetLogisticsStoreId)) {
            targetLogisticsCompany.setCompanyId(sourceMapping.getCompanyId());
            targetLogisticsCompany.setCompanyCode(sourceMapping.getCompanyCode());
            targetLogisticsCompany.setCompanyName(sourceMapping.getCompanyName());
            return targetLogisticsCompany;
        } else if (sourceMapping.getAyLogisticsCompanyId() == null) {
            LOGGER.logError("不存在对应映射的物流公司：" + sourceCompany + "，来源平台：" + logisticsStoreId + ",目标平台：" + targetLogisticsStoreId);
            return null;
        }

        // 根据ayId找到对应
        AyLogisticsCompanyMapping targetMapping = ayLogisticsCompanyMappingDao.queryByAyIdAndStoreId(sourceMapping.getAyLogisticsCompanyId(), targetLogisticsStoreId);
        if (targetMapping == null) {
            LOGGER.logError("不存在对应映射的物流公司：" + sourceCompany + "，来源平台：" + logisticsStoreId + ",目标平台：" + targetLogisticsStoreId);
            return null;
        }

        targetLogisticsCompany.setCompanyId(targetMapping.getCompanyId());
        targetLogisticsCompany.setCompanyCode(targetMapping.getCompanyCode());
        targetLogisticsCompany.setCompanyName(targetMapping.getCompanyName());
        return targetLogisticsCompany;
    }

    /**
     * 处理老的需要手动处理的数据
     *
     * @param sourceMapping
     * @param targetLogisticsStoreId
     * @param outSid
     * @param targetLogisticsCompany
     * @param logisticsAppName
     * @return
     */
    private LogisticsCompanyInfoDTO handleOldPendingMappingData(AyLogisticsCompanyMapping sourceMapping,
        String targetLogisticsStoreId, String outSid, LogisticsCompanyInfoDTO targetLogisticsCompany,
        String logisticsAppName) {

        AyLogisticNumberRecognitionRequest ayLogisticNumberRecognitionRequest =
            new AyLogisticNumberRecognitionRequest();
        ayLogisticNumberRecognitionRequest.setOutSid(outSid);
        AyLogisticNumberRecognitionResponse ayLogisticNumberRecognition = logisticsApiPlatformHandleService
            .recognitionLogisticNumber(ayLogisticNumberRecognitionRequest, targetLogisticsStoreId, logisticsAppName);

        if (ayLogisticNumberRecognition == null) {
            return null;
        }

        String targetCompanyCode = ayLogisticNumberRecognition.getCompanyCode();
        String targetCompanyName = ayLogisticNumberRecognition.getCompanyName();
        AyLogisticsCompanyMapping targetLogisticsCompanyMapping =
            ayLogisticsCompanyMappingDao.queryByCompanyCodeAndStoreId(targetCompanyCode, targetLogisticsStoreId);

        List<AyLogisticsCompanyMapping> needInsertMapping = Lists.newArrayList();
        List<AyLogisticsCompanyMapping> needUpdateMapping = Lists.newArrayList();
        if (targetLogisticsCompanyMapping == null) {
            // 新的目标物流映射
            AyLogisticsCompanyMapping newTargetAyLogisticsCompanyMapping = new AyLogisticsCompanyMapping();
            newTargetAyLogisticsCompanyMapping.setFlag(AyLogisticsCompanyMapping.FLAG_MANUAL);
            newTargetAyLogisticsCompanyMapping.setCompanyId(null);
            newTargetAyLogisticsCompanyMapping.setCompanyCode(targetCompanyCode);
            newTargetAyLogisticsCompanyMapping.setCompanyName(targetCompanyName);
            newTargetAyLogisticsCompanyMapping.setLogisticsStoreId(targetLogisticsStoreId);
            newTargetAyLogisticsCompanyMapping.setAyLogisticsCompanyId(null);
            ayLogisticsCompanyMappingDao.insert(newTargetAyLogisticsCompanyMapping);

            // 需要更新标记的待手动处理数据
            AyLogisticsCompanyMapping logisticsCompanyMapping = new AyLogisticsCompanyMapping();
            logisticsCompanyMapping.setId(sourceMapping.getId());
            logisticsCompanyMapping.setFlag(AyLogisticsCompanyMapping.FLAG_MANUAL);
            ayLogisticsCompanyMappingDao.update(logisticsCompanyMapping);

            needUpdateMapping.add(logisticsCompanyMapping);
            needInsertMapping.add(newTargetAyLogisticsCompanyMapping);
            LOGGER.logInfo(sourceMapping.getCompanyCode(), sourceMapping.getCompanyName(),
                "该物流对应的: " + targetLogisticsStoreId + "物流不存在, 新增目标用户物流映射, 标记为待处理");
            return null;
        } else if (targetLogisticsCompanyMapping.getFlag() != null
            && targetLogisticsCompanyMapping.getAyLogisticsCompanyId() == null) {
            // 已存在目标平台映射但是没有设置ay映射id
            LOGGER.logInfo(sourceMapping.getCompanyCode(), sourceMapping.getCompanyName(),
                "该物流对应的: " + targetLogisticsStoreId + "物流需要手动处理, 标记当前物流为待手动处理");
            return null;
        } else {
            // 存在映射 直接映射
            sourceMapping.setFlag(null);
            sourceMapping.setAyLogisticsCompanyId(targetLogisticsCompanyMapping.getAyLogisticsCompanyId());
            ayLogisticsCompanyMappingDao.update(sourceMapping);
            LOGGER.logInfo(sourceMapping.getCompanyCode(), sourceMapping.getCompanyName(),
                "该物流对应的: " + targetLogisticsStoreId + "物流存在映射关系, 进行关系映射");

            targetLogisticsCompany.setCompanyId(targetLogisticsCompanyMapping.getAyLogisticsCompanyId());
            targetLogisticsCompany.setCompanyCode(targetLogisticsCompanyMapping.getCompanyCode());
            targetLogisticsCompany.setCompanyName(targetLogisticsCompanyMapping.getCompanyName());
            return targetLogisticsCompany;
        }
    }

    /**
     * 处理新的物流映射
     *
     * @param targetLogisticsStoreId 目标平台
     * @param outSid 运单号
     * @param logisticsAppName 当前请求的应用
     * @param logisticsStoreId 当前请求的平台
     * @param companyId 当前请求的物流公司id
     * @param companyCode 当前请求的物流公司code
     * @param companyName 当前请求的物流公司名称
     * @param targetLogisticsCompanyInfo 映射成目标平台的物流公司信息
     * @return
     */
    private LogisticsCompanyInfoDTO handleNewLogisticsMapping(String targetLogisticsStoreId, String outSid,
        String logisticsAppName, String logisticsStoreId, String companyId, String companyCode, String companyName,
        LogisticsCompanyInfoDTO targetLogisticsCompanyInfo) {
        List<AyLogisticsCompanyMapping> needInsertList = Lists.newArrayList();

        if (StringUtils.isNotEmpty(outSid)) {
            // 1. 调用api获取物流单号对应的物流公司
            AyLogisticNumberRecognitionRequest ayLogisticNumberRecognitionRequest =
                new AyLogisticNumberRecognitionRequest();
            ayLogisticNumberRecognitionRequest.setOutSid(outSid);
            AyLogisticNumberRecognitionResponse ayLogisticNumberRecognition =
                logisticsApiPlatformHandleService.recognitionLogisticNumber(ayLogisticNumberRecognitionRequest,
                    targetLogisticsStoreId, logisticsAppName);
            if (ayLogisticNumberRecognition != null) {
                String newCompanyCode = ayLogisticNumberRecognition.getCompanyCode();
                String newCompanyName = ayLogisticNumberRecognition.getCompanyName();
                AyLogisticsCompanyMapping ayLogisticsCompanyMapping =
                    ayLogisticsCompanyMappingDao.queryByCompanyCodeAndStoreId(newCompanyCode, targetLogisticsStoreId);
                // 2. 查询快递鸟是否存在这些映射，若存在则直接映射成对应类型
                if (ayLogisticsCompanyMapping != null && ayLogisticsCompanyMapping.getFlag() == null
                    && ayLogisticsCompanyMapping.getAyLogisticsCompanyId() != null) {

                    AyLogisticsCompanyMapping newayLogisticsCompanyMapping = new AyLogisticsCompanyMapping();
                    newayLogisticsCompanyMapping.setFlag(null);
                    newayLogisticsCompanyMapping.setCompanyId(companyId);
                    newayLogisticsCompanyMapping.setCompanyCode(companyCode);
                    newayLogisticsCompanyMapping.setCompanyName(companyName);
                    newayLogisticsCompanyMapping.setLogisticsStoreId(logisticsStoreId);
                    newayLogisticsCompanyMapping
                        .setAyLogisticsCompanyId(ayLogisticsCompanyMapping.getAyLogisticsCompanyId());
                    ayLogisticsCompanyMappingDao.insert(newayLogisticsCompanyMapping);

                    targetLogisticsCompanyInfo.setCompanyId(ayLogisticsCompanyMapping.getCompanyId());
                    targetLogisticsCompanyInfo.setCompanyCode(ayLogisticsCompanyMapping.getCompanyCode());
                    targetLogisticsCompanyInfo.setCompanyName(ayLogisticsCompanyMapping.getCompanyName());
                    return targetLogisticsCompanyInfo;
                }
            }
        }

        // 3. 无对应的映射或对应映射未处理，则插入新的待更新的数据
        AyLogisticsCompanyMapping mapping = new AyLogisticsCompanyMapping();
        mapping.setFlag(AyLogisticsCompanyMapping.FLAG_MANUAL);
        mapping.setCompanyId(companyId);
        mapping.setCompanyCode(companyCode);
        mapping.setCompanyName(companyName);
        mapping.setLogisticsStoreId(logisticsStoreId);
        needInsertList.add(mapping);

        // 不在插入数据库，前段传来的物流公司没有明确值，用户自定义导致物流公司名混乱，也无人手动维护映射表，后期新增物流（不可能有很多物流公司）时人工映射
        LOGGER.logWarn("存在待更新的物流公司映射：" + JSON.toJSONString(needInsertList));
        return null;
    }
}
