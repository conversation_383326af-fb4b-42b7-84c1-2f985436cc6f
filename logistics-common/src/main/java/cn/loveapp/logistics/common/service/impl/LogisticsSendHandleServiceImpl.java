package cn.loveapp.logistics.common.service.impl;

import java.time.LocalDateTime;
import java.util.*;

import cn.loveapp.common.utils.CommonRocketMqQueueHelper;
import cn.loveapp.orders.dto.proto.LogisticsTraceRequest;
import cn.loveapp.orders.dto.proto.OrderMcNotifyProto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.mq.CommonRocketMqAdminService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.config.LogisticsPretestConfig;
import cn.loveapp.logistics.common.config.rocketmq.RocketMQLogisticsAbnormalConfig;
import cn.loveapp.logistics.common.config.rocketmq.RocketMQLogisticsRouterConfig;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.dto.proto.LogisticsAbnormalRequestProto;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.service.LogisticsSendHandleService;

/**
 * 物流消息发送实现类
 *
 * <AUTHOR>
 * @Date 2023/6/6 18:00
 */
@Service
public class LogisticsSendHandleServiceImpl implements LogisticsSendHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderHandleServiceImpl.class);

    /**
     * 默认router转发配置
     */
    private static final String DEFAULT_ROUTER_CONFIG = "DEFAULT";

    public static final String CUSTOM_V_2 = "custom_v2";

    @Autowired
    private CommonRocketMqAdminService commonRocketMqAdminService;

    @Autowired
    private CommonRocketMqQueueHelper commonRocketMqQueueHelper;

    @Autowired
    @Qualifier("defaultProducer")
    private DefaultMQProducer logisticsProducer;

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private RocketMQLogisticsRouterConfig rocketMQLogisticsRouterConfig;

    @Autowired
    private LogisticsPretestConfig logisticsPretestConfig;

    @Autowired
    private RocketMQLogisticsAbnormalConfig rocketMQLogisticsAbnormalConfig;

    @Override
    public boolean pushLogisticsPretest(LogisticsTraceRequestProto oMsg, List<LogisticsInfoDTO> logisticsInfos, String sourceTopic) {
        List<String> pretestUsers = logisticsPretestConfig.getPretestUsers();
        String logisticsTopic = logisticsPretestConfig.getLogisticsTopic();

        if (sourceTopic.equals(logisticsTopic)) {
            // 当前为预发队列，跳过
            return false;
        }

        if (CollectionUtils.isEmpty(logisticsInfos) || StringUtils.isEmpty(logisticsTopic) || CollectionUtils.isEmpty(pretestUsers)) {
            return false;
        }
        LogisticsInfoDTO logisticsInfoDTO = logisticsInfos.get(0);
        String sellerId = logisticsInfoDTO.getSellerId();
        String sellerNick = logisticsInfoDTO.getSellerNick();
        if (!pretestUsers.contains(sellerId) && !pretestUsers.contains(sellerNick)) {
            // 不是灰度用户，跳过
            return false;
        }
        LOGGER.logInfo("灰度用户转发,sellerId:" + sellerId + ", sellerNick:" + sellerNick);
        commonRocketMqQueueHelper.push(logisticsTopic, "*", oMsg, logisticsProducer);
        return true;
    }

    @Override
    public void pushLogisticsSaveMsg(List<LogisticsInfoDTO> logisticsInfos, boolean checkModified,
        String logisticsStoreId, String logisticsAppName) {

        if (CollectionUtils.isEmpty(logisticsInfos)) {
            return;
        }

        String topic = logisticsConfig.getTopic();
        LogisticsTraceRequestProto logisticsMessage = new LogisticsTraceRequestProto();
        logisticsMessage.setNotifyLogistics(logisticsInfos);
        logisticsMessage.setLogisticsStoreId(logisticsStoreId);
        logisticsMessage.setLogisticsAppName(logisticsAppName);
        logisticsMessage.setCheckModified(checkModified);

        commonRocketMqQueueHelper.push(topic, "*", logisticsMessage, logisticsProducer);

    }

    @Override
    public void pushLogisticsToMcRouter(LogisticsOrderInfo logisticsOrderInfo, LogisticsTraceInfo logisticsTraceInfo) {
        if (Objects.isNull(logisticsTraceInfo) || Objects.isNull(logisticsOrderInfo)) {
            return;
        }

        String topic = null;

        Map<String, RocketMQLogisticsRouterConfig.Config> targets = rocketMQLogisticsRouterConfig.getTargets();
        if (Objects.isNull(targets)) {
            LOGGER.logError("未配置转发");
            return;
        }

        if (!Objects.isNull(targets.get(logisticsOrderInfo.getStoreId()))) {
            RocketMQLogisticsRouterConfig.Config config = targets.get(logisticsOrderInfo.getStoreId());
            if (!Objects.isNull(config) && StringUtils.isNotEmpty(config.getToTopic())) {
                topic = config.getToTopic();
            }
        } else if (!Objects.isNull(targets.get(DEFAULT_ROUTER_CONFIG))) {
            // 未配置走默认
            RocketMQLogisticsRouterConfig.Config config = targets.get(DEFAULT_ROUTER_CONFIG);
            if (!Objects.isNull(config) && StringUtils.isNotEmpty(config.getToTopic())) {
                topic = config.getToTopic();
            }
        }

        if (StringUtils.isEmpty(topic)) {
            LOGGER.logError("未配置转发topic");
            return;
        }

        LogisticsTraceRequest logisticsTraceRequest = LogisticsTraceInfo.toLogisticsTraceRequest(logisticsTraceInfo);
        logisticsTraceRequest.setSellerId(logisticsOrderInfo.getSellerId());
        logisticsTraceRequest.setSellerNick(logisticsOrderInfo.getSellerNick());

        LogisticsOrderInfo.BusinessInfo businessInfo = logisticsOrderInfo.getBusinessInfo();
        if (!Objects.isNull(businessInfo) && Objects.nonNull(logisticsOrderInfo.getBusinessType())) {
            Set<String> refundIdList = businessInfo.getRefundIdList();
            Set<String> tidList = businessInfo.getTidList();
            if (CollectionUtils.isNotEmpty(refundIdList)) {
                logisticsTraceRequest.setRefundList(Lists.newArrayList(refundIdList));
            } else if (CollectionUtils.isNotEmpty(tidList)) {
                logisticsTraceRequest.setTidList(Lists.newArrayList(tidList));
            } else {
                LOGGER.logError("缺少业务信息，不转发");
                return;
            }
        } else if (CommonLogisticsConstants.PLATFORM_PDD.equals(logisticsOrderInfo.getSaveLogisticsStoreId())) {
            LOGGER.logInfo("PDD物流消息无订单号，特殊处理:" + logisticsOrderInfo.getOutSid());
        } else {
            LOGGER.logError("缺少业务信息，不转发");
            return;
        }

        OrderMcNotifyProto orderMcNotifyProto = new OrderMcNotifyProto();
        orderMcNotifyProto.setLogisticsTraceRequest(logisticsTraceRequest);
        commonRocketMqQueueHelper.push(topic, "*", orderMcNotifyProto, logisticsProducer);
    }

    public boolean pushLogisticsAbnormalNotifyMsg(LogisticsOrderInfo newLogisticsOrderInfo, List<String> abnormalTypeNotifyList) {
        return this.pushLogisticsAbnormalMsg(newLogisticsOrderInfo, 0, abnormalTypeNotifyList);
    }

    public boolean pushLogisticsAbnormalResendMsg(LogisticsOrderInfo newLogisticsOrderInfo, LocalDateTime deadLine) {
        if (deadLine == null) {
            LOGGER.logInfo("运单号：" + newLogisticsOrderInfo.getOutSid() + ",无可判断deadLine，无需发送");
            return false;
        }
        int defaultDelayTimeLevel = rocketMQLogisticsAbnormalConfig.getDelayTimeLevel();
        int delayTimeLevel = commonRocketMqAdminService.calculateBestDelayLevel(deadLine, defaultDelayTimeLevel);
        return this.pushLogisticsAbnormalMsg(newLogisticsOrderInfo, delayTimeLevel, null);
    }


    private boolean pushLogisticsAbnormalMsg(LogisticsOrderInfo newLogisticsOrderInfo, int delayTimeLevel, List<String> abnormalTypeNotifyList) {
        String sellerId = newLogisticsOrderInfo.getSellerId();
        String storeId = newLogisticsOrderInfo.getStoreId();
        String appName = newLogisticsOrderInfo.getAppName();
        String sellerNick = newLogisticsOrderInfo.getSellerNick();
        String topic = rocketMQLogisticsAbnormalConfig.getTopic();

        LogisticsAbnormalRequestProto proto = new LogisticsAbnormalRequestProto();
        proto.setOutSid(newLogisticsOrderInfo.getOutSid());
        proto.setSellerId(sellerId);
        proto.setStoreId(storeId);
        proto.setAppName(appName);
        proto.setCompanyCode(newLogisticsOrderInfo.getCompanyCode());
        proto.setCompanyName(newLogisticsOrderInfo.getCompanyName());
        if (CommonLogisticsConstants.PLATFORM_TAO.equals(newLogisticsOrderInfo.getSaveLogisticsStoreId())) {
            LogisticsOrderInfo.BusinessInfo businessInfo = newLogisticsOrderInfo.getBusinessInfo();
            if (businessInfo == null || CollectionUtils.isEmpty(businessInfo.getTidList())) {
                LOGGER.logError("淘宝物流缺少tid，无法校验异常物流");
                return false;
            }
            Set<String> tidList = businessInfo.getTidList();
            proto.setTidList(Lists.newArrayList(tidList));
        }
        if (CollectionUtils.isEmpty(abnormalTypeNotifyList)) {
            // 异常校验队列
            proto.setCheckAbnormal(true);
        } else {
            // 弹窗消息，包含需要弹窗的异常，并且设置为0延迟
            proto.setAbnormalTypeNotifyList(abnormalTypeNotifyList);
        }

        commonRocketMqQueueHelper.push(topic, "*", proto, logisticsProducer, delayTimeLevel);

        return true;
    }

    @Override
    public void pushCustomNotifyMsg(String consumerUrl, Object data) {
        JSONObject customMessage = new JSONObject();
        JSONObject customV2 = new JSONObject();
        customMessage.put(CUSTOM_V_2, customV2);
        customV2.put("consumer_url", consumerUrl);
        customV2.put("data", data);
        commonRocketMqQueueHelper.push(logisticsConfig.getCustomTopic(), CUSTOM_V_2, customMessage, logisticsProducer, 0);
    }
}
