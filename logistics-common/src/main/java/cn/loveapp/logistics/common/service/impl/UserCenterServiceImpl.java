package cn.loveapp.logistics.common.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.constant.LogisticsShopTag;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.service.UserCenterService;
import cn.loveapp.logistics.common.service.UserInfoService;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import cn.loveapp.uac.domain.UserSettingDTO;
import cn.loveapp.uac.request.BatchSettingGetRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserCacheInfoResponse;

/**
 * <AUTHOR>
 * @Date 2023/8/15 15:47
 */
@Service
public class UserCenterServiceImpl implements UserCenterService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserCenterServiceImpl.class);

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private LogisticsConfig logisticsConfig;

    /**
     * 无需校验监控开关的物流平台
     */
    @Value("${logistics.notCheck.monitor.enable.logisticsStoreIdList:PDD}")
    private List<String> notCheckMonitorEnableLogisticsStoreIdList;

    /**
     * 无需校验监控开关的物流平台
     */
    @Value("${logistics.notCheck.monitor.enable.logisticsAppNameList:}")
    private List<String> notCheckMonitorEnableLogisticsAppNameList;

    @Override
    public boolean checkLogisticsMonitorEnable(BusinessType type, UserInfoRequest userInfoRequest, List<String> extraSettingCheckList, String logisticsAppName, String logisticsStoreId) {

        String sellerId = userInfoRequest.getSellerId();
        String appName = userInfoRequest.getApp();
        String storeId = userInfoRequest.getPlatformId();
        String sellerNick = userInfoRequest.getSellerNick();

        if (StringUtils.isAnyEmpty(sellerId, appName, storeId)) {
            return false;
        }

        if (notCheckMonitorEnableLogisticsAppNameList.contains(appName)) {
            return true;
        }

        if (notCheckMonitorEnableLogisticsStoreIdList.contains(logisticsStoreId)) {
            LOGGER.logInfo("该平台无需校验开关，跳过：" + logisticsStoreId);
            return true;
        }

        if (!isAbnormalAutoCheckUser(sellerId, sellerNick, storeId, appName)) {
            LOGGER.logInfo("非打标爱用用户，跳过");
            return false;
        }

        BatchSettingGetRequest batchSettingGetRequest = new BatchSettingGetRequest();
        batchSettingGetRequest.setApp(appName);
        batchSettingGetRequest.setPlatformId(storeId);
        batchSettingGetRequest.setUserId(sellerId);
        if (CollectionUtils.isNotEmpty(extraSettingCheckList)) {
            extraSettingCheckList.addAll(UserAbnormalSettingConstant.MONITORING_ENABLE_SETTING_STR_LIST);
            batchSettingGetRequest.setSettings(extraSettingCheckList);
        } else {
            batchSettingGetRequest.setSettings(UserAbnormalSettingConstant.MONITORING_ENABLE_SETTING_STR_LIST);
        }

        List<UserSettingDTO> userSettings = userInfoService.batchSettingGet(batchSettingGetRequest);
        if (CollectionUtils.isEmpty(userSettings)) {
            return false;
        }

        Map<String, String> settingMap = userSettings.stream().collect(Collectors.toMap(UserSettingDTO::getKey, UserSettingDTO::getValue));
        if (LogisticsUtil.isTradeERP(appName)) {
            // erp只判断运营开关
            String value = settingMap.get(UserAbnormalSettingConstant.TRADE_ERP_LOGISTICS_WARNING_WHITE);
            Integer whiteFlag = ConvertUtil.parseOrGetDefault(value, 0, Integer.class);
            return whiteFlag == 1;
        }

        // 1.检查主要开关是否开启
        if (BusinessType.refund.equals(type)) {
            // 退款单
            String value = settingMap.get(UserAbnormalSettingConstant.LOGISTICS_REFUND_MONITORING_ENABLE);
            if (!ConvertUtil.parseOrGetDefault(value, false, Boolean.class)) {
                return false;
            }
        } else if (CommonPlatformConstants.PLATFORM_AIYONG.equals(storeId)){
            // 线下单
            String value = settingMap.get(UserAbnormalSettingConstant.LOGISTICS_AY_CUSTOM_MONITORING_ENABLE);
            if (!ConvertUtil.parseOrGetDefault(value, false, Boolean.class)) {
                return false;
            }
        } else {
            // 普通单
            String multiValue = settingMap.get(UserAbnormalSettingConstant.LOGISTICS_ORDER_MONITORING_ENABLE);
            String tradeValue = settingMap.get(UserAbnormalSettingConstant.LOGISTICS_TRADE_ORDER_MONITORING_ENABLE);
            if (!(ConvertUtil.parseOrGetDefault(multiValue, false, Boolean.class)
                || ConvertUtil.parseOrGetDefault(tradeValue, false, Boolean.class))) {
                return false;
            }
        }

        // 2. 校验指定开关（子开关）是否开启
        if (CollectionUtils.isNotEmpty(extraSettingCheckList) && extraSettingCheckList
            .contains(UserAbnormalSettingConstant.LOGISTICS_REFUND_ORDER_AUTO_MARK_PROCESSED_ENABLE)) {
            String subValue =
                settingMap.get(UserAbnormalSettingConstant.LOGISTICS_REFUND_ORDER_AUTO_MARK_PROCESSED_ENABLE);
            return ConvertUtil.parseOrGetDefault(subValue, false, Boolean.class);
        }

        return true;
    }


    /**
     * 判断是否异常打标用户
     * @param sellerId
     * @param sellerNick
     * @param appName
     * @param storeId
     * @return
     */
    @Override
    public boolean isAbnormalAutoCheckUser(String sellerId, String sellerNick, String storeId, String appName) {
        if (LogisticsUtil.isTradeERP(appName)) {
            return true;
        }

        UserInfoRequest userInfoRequest = new UserInfoRequest();
        userInfoRequest.setApp(appName);
        userInfoRequest.setSellerId(sellerId);
        userInfoRequest.setSellerNick(sellerNick);
        userInfoRequest.setPlatformId(storeId);
        UserCacheInfoResponse userTagsInfo = userInfoService.getShopTags(userInfoRequest);
        if (Objects.isNull(userTagsInfo)) {
            LOGGER.logInfo("用户信息获取异常");
            return false;
        }
        String shopTags = userTagsInfo.getCacheValue();

        if (StringUtils.isEmpty(shopTags)) {
            return false;
        }
        boolean isEnabled = false;
        if (shopTags.contains(LogisticsShopTag.LOGISTICS_AUTO_CHECK_TAG.value())) {
            // 多店异常物流打标
            LOGGER.logInfo(sellerNick, "", "多店打标用户，需发送队列校验物流. storeId=" + storeId);
            isEnabled = true;
        } else if (shopTags.contains(LogisticsShopTag.TRADE_PROFESSIONAL.value())) {
            if (!logisticsConfig.isLogisticsTradeProfessionalEnable()) {
                LOGGER.logInfo("交易物流保存开关关闭");
                return false;
            }
            // 交易专业版打标
            isEnabled = isTradeOrderMonitorSettingsEnabled(userInfoRequest);
            if (isEnabled) {
                LOGGER.logInfo(sellerNick, "", "交易专业版打标用户，需发送队列校验物流. storeId=" + storeId);
            }
        }
        return isEnabled;
    }

    /**
     * 交易用户是否开启了监控开关
     * @param userInfoRequest
     * @return
     */
    private boolean isTradeOrderMonitorSettingsEnabled(UserInfoRequest userInfoRequest) {
        BatchSettingGetRequest batchSettingGetRequest = new BatchSettingGetRequest();
        batchSettingGetRequest.setApp(userInfoRequest.getApp());
        batchSettingGetRequest.setPlatformId(userInfoRequest.getPlatformId());
        batchSettingGetRequest.setUserId(userInfoRequest.getSellerId());
        batchSettingGetRequest.setSettings(Lists.newArrayList(UserAbnormalSettingConstant.LOGISTICS_TRADE_ORDER_MONITORING_ENABLE));
        List<UserSettingDTO> userSettings = userInfoService.batchSettingGet(batchSettingGetRequest);

        if (CollectionUtils.isEmpty(userSettings)) {
            return false;
        }
        String value = userSettings.get(0).getValue();
        return BooleanUtils.isTrue(ConvertUtil.parseOrGetDefault(value, false, Boolean.class));
    }

}
