package cn.loveapp.logistics.common.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.common.service.UserInfoService;
import cn.loveapp.uac.domain.UserSettingDTO;
import cn.loveapp.uac.request.BatchGetUserCacheInfoRequest;
import cn.loveapp.uac.request.BatchSettingGetRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserCacheInfoResponse;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.UserCenterInnerApiService;
import cn.loveapp.uac.utils.UacRpcUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.filter.ValueFilter;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Since 2020/6/23 21:43
 */
@CacheConfig(cacheNames = "userCache")
@Service
public class UserInfoServiceImpl implements UserInfoService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserInfoServiceImpl.class);

    /**
     * 店铺群tag
     */
    public static final String SHOPS_TAG = "shops_tag";

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private UserCenterInnerApiService userCenterInnerApiService;

    private final HttpTraceLogJsonValueFilter jsonValueFilter = new HttpTraceLogJsonValueFilter();

    @Override
    @Cacheable
    public UserInfoResponse getSellerInfo(UserInfoRequest userInfoRequest) {
//        LOGGER.logInfo("uac getSellerInfo request => " + JSON.toJSONString(userInfoRequest));
        long time = System.currentTimeMillis();
        CommonApiResponse<UserInfoResponse> response = userCenterInnerApiService.getUserInfo(userInfoRequest);
        LOGGER.logInfo("uac getSellerInfo response => " + JSON.toJSONString(response) + "; cost="
            + (System.currentTimeMillis() - time));
        if (response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
            return response.getBody();
        }
        return null;
    }


    @Override
    public UserCacheInfoResponse getShopTags(UserInfoRequest userInfoRequest) {

        if (userInfoRequest == null) {
            return null;
        }
        List<UserInfoRequest> userInfoRequestList = Lists.newArrayList(userInfoRequest);
        BatchGetUserCacheInfoRequest batchGetUserCacheInfoRequest = new BatchGetUserCacheInfoRequest();
        batchGetUserCacheInfoRequest.setCacheHkey(SHOPS_TAG);
        batchGetUserCacheInfoRequest.setRequestList(userInfoRequestList);

        long time = System.currentTimeMillis();
        try {
            CommonApiResponse<List<UserCacheInfoResponse>> response = UacRpcUtils
                .batchGetUserCacheInfo(batchGetUserCacheInfoRequest, userCenterInnerApiService, stringRedisTemplate);
            time = System.currentTimeMillis() - time;
            if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                List<UserCacheInfoResponse> userCacheInfoResponses = response.getBody();
                if (!userCacheInfoResponses.isEmpty()) {
                    UserCacheInfoResponse userCacheInfo = userCacheInfoResponses.get(0);
                    if (response.getRequestId() != null) {
                        LOGGER.logInfo(
                            "uac batchGetUserCacheInfo request => " + JSON.toJSONString(batchGetUserCacheInfoRequest)
                                + ", response => " + JSON.toJSONString(response) + ", cost：" + time);
                    } else {
                        // 简化从 缓存获取的日志
                        LOGGER.logInfo(userInfoRequest.getSellerNick(), userInfoRequest.getSellerId(),
                            "uac batchGetUserCacheInfo request => " + SHOPS_TAG + ", response => "
                                + userCacheInfo.getCacheValue() + ", cost：" + time);
                    }
                    return userCacheInfoResponses.get(0);
                }
            }
        } catch (Exception e) {
            time = System.currentTimeMillis() - time;
            LOGGER.logError("uac batchGetUserCacheInfo 请求失败 request => "
                + JSON.toJSONString(batchGetUserCacheInfoRequest) + ", cost：" + time + ", 异常：" + e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<UserInfoResponse> getUserInfo(List<TargetSellerInfo> targetSellerInfoList) {
        long time = System.currentTimeMillis();
        try {
            List<UserInfoRequest> collect = targetSellerInfoList.stream().map(m -> {
                UserInfoRequest request = new UserInfoRequest();
                request.setPlatformId(m.getTargetStoreId());
                request.setApp(m.getTargetAppName());
                request.setSellerNick(m.getTargetNick());
                request.setSellerId(m.getTargetSellerId());
                return request;
            }).collect(Collectors.toList());
//            LOGGER.logInfo("uac getUserInfo, request=" + JSON.toJSONString(targetSellerInfoList));
            CommonApiResponse<List<UserInfoResponse>> response = userCenterInnerApiService.batchGetUserInfo(collect);
            LOGGER.logInfo("uac getUserInfo, response=" + JSON.toJSONString(response) + ", cost="
                + (System.currentTimeMillis() - time));
            if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                return response.getBody();
            }
        } catch (Exception e) {
            LOGGER.logError("uac getUserInfo 请求失败 request=" + JSON.toJSONString(targetSellerInfoList) + ", cost="
                + (System.currentTimeMillis() - time) + "，异常: " + e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<UserSettingDTO> batchSettingGet(BatchSettingGetRequest request) {
        long time = System.currentTimeMillis();
        try {
//            LOGGER.logInfo("uac batchSettingGet, request=" + JSON.toJSONString(request, jsonValueFilter));
            CommonApiResponse<List<UserSettingDTO>> response = userCenterInnerApiService.batchSettingGet(request);
            LOGGER.logInfo("uac batchSettingGet, response=" + JSON.toJSONString(response, jsonValueFilter) + ", cost="
                + (System.currentTimeMillis() - time));
            if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                return response.getBody();
            }
        } catch (Exception e) {
            LOGGER.logError("uac batchSettingGet 请求失败 request=" + JSON.toJSONString(request, jsonValueFilter) + ", cost="
                + (System.currentTimeMillis() - time) + "，异常: " + e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public String getTopSession(String sellerNick, String sellerId, String platformId, String appName) {
        UserInfoRequest request = new UserInfoRequest();
        request.setPlatformId(platformId);
        request.setApp(appName);
        request.setSellerNick(sellerNick);
        request.setSellerId(sellerId);

        String topSession = null;
        long time = System.currentTimeMillis();
        try {
//            LOGGER.logInfo(sellerNick, "-", "uac getTopSession, request=" + JSON.toJSONString(request));
            CommonApiResponse<UserInfoResponse> response = userCenterInnerApiService.getTopSession(request);
            LOGGER.logInfo(sellerNick, "-", "uac getTopSession, response=" + JSON.toJSONString(response) + ", cost="
                + (System.currentTimeMillis() - time));
            if (response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                topSession = response.getBody().getTopSession();
            }
        } catch (Exception e) {
            LOGGER.logError(sellerNick, "-", "uac getTopSession 网络异常 request=" + JSON.toJSONString(request) + ", cost="
                + (System.currentTimeMillis() - time) + "，异常: " + e.getMessage(), e);
        }
        return topSession;
    }

    /**
     * 优化请求响应日志，忽略或缩减一些无用日志
     */
    private static class HttpTraceLogJsonValueFilter implements ValueFilter {
        @Override
        public Object apply(Object object, String name, Object value) {
            if ("settings".equals(name) && value instanceof ArrayList) {
                // 替换缩写掉过长的 设置 信息
                return ((ArrayList<?>) value).stream()
                    .map(HttpTraceLogJsonValueFilter::abbreviatedSettingsName).collect(Collectors.toList());
            } else if ("key".equals(name) && value instanceof String) {
                return abbreviatedSettingsName(value);
            }
            return value;
        }

        private static String abbreviatedSettingsName(Object value) {
            if(value == null) {
                return null;
            }
            return value.toString().replace("abnormal.logistics", "a*.lo*");
        }
    }
}
