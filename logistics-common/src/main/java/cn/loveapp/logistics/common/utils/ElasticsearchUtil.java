package cn.loveapp.logistics.common.utils;

import cn.loveapp.common.utils.LoggerHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * ESUtil
 *
 * <AUTHOR>
 * @date 2020/2/22
 */
public class ElasticsearchUtil {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElasticsearchUtil.class);

    public static <E> ArrayList<E> toList(E ob) {
        if (ob == null) {
            return null;
        }
        return Lists.newArrayList(ob);
    }

    public static <E> ArrayList<E> toList(Collection<E> cl) {
        if (CollectionUtils.isEmpty(cl)) {
            return null;
        }
        return Lists.newArrayList(cl);
    }

    public static String toString(Collection<String> cl) {
        if (CollectionUtils.isEmpty(cl)) {
            return null;
        }
        return String.join(",", cl);
    }

    /**
     * 在数字和字母离两边添加空格, 防止分词无法查询不规整的数字字母
     * 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @return
     */
    public static void splitAlphanumeric(List<String> texts) {
        if (CollectionUtils.isNotEmpty(texts)) {
            try {
                texts.replaceAll(ElasticsearchUtil::splitAlphanumeric);
            } catch (Exception e) {
                LOGGER.logError(e.getMessage(), e);
            }
        }
    }

    /**
     * 在数字和字母离两边添加空格, 防止分词无法查询不规整的数字字母
     * 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @return
     */
    public static String splitAlphanumeric(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        try {
            // 使用StringBuilder避免多次字符串创建
            StringBuilder result = new StringBuilder(text.length() * 3);

            for (int i = 0; i < text.length(); i++) {
                char c = text.charAt(i);
                // 只对数字和字母字符两侧添加空格
                if (Character.isLetterOrDigit(c)) {
                    // 字母a/A特殊处理，替换为aa/AA
                    if (c == 'a' || c == 'A') {
                        result.append(' ').append(c).append(c).append(' ');
                    } else {
                        result.append(' ').append(c).append(' ');
                    }
                } else {
                    // 非数字字母字符直接添加，不加空格
                    result.append(c);
                }
            }

            return result.toString();
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
            return text;
        }
    }

    /**
     * 在所有字符两边添加空格, 防止分词无法查询不规整的数字字母
     * 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @return
     */
    public static String splitAll(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        try {
            // 使用StringBuilder避免多次字符串创建
            StringBuilder result = new StringBuilder(text.length() * 2);
            String lowerText = text.toLowerCase();

            for (int i = 0; i < lowerText.length(); i++) {
                char c = lowerText.charAt(i);
                // 直接处理字符'a', 转为 'aa' 防止分词忽略
                if (c == 'a') {
                    result.append("aa ");
                } else {
                    result.append(c).append(' ');
                }
            }

            return result.toString().trim();
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
            return text;
        }
    }
}
