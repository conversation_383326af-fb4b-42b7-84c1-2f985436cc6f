package cn.loveapp.logistics.common.utils;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-10-29 14:24
 * @description: 物流工具类
 */
public class LogisticsUtil {

    /**
     * 获取默认物流平台
     *
     * @param LogisticsStoreId
     * @return
     */
    public static String getDefaultLogisticsStoreId(String LogisticsStoreId) {
        // todo 前段无开发人力，后端临时兼容，已建迭代，前段改造后删除
        if (CommonLogisticsConstants.PLATFORM_KDNIAO.equals(LogisticsStoreId)) {
            return CommonLogisticsConstants.PLATFORM_CAINIAO;
        }

        return LogisticsStoreId;
    }

    /**
     * 判断是否erp应用
     *
     * @param appName
     * @return
     */
    public static boolean isTradeERP(String appName) {
        return Objects.equals(appName, CommonAppConstants.APP_TRADE_ERP);
    }

    /**
     * 获取默认的appname
     * @param platformId
     * @param appName
     * @return
     */
    public static String defaultAppName(String platformId, String appName) {
        if (CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName) && StringUtils.isNotEmpty(platformId)) {
            return CommonAppConstants.APP_TRADE_SUPPLIER;
        }
        if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)
            && (StringUtils.isEmpty(appName) || CommonAppConstants.APP_TRADE.equals(appName))) {
            // 淘宝交易appName为空
            return StringUtils.EMPTY;
        } else if (CommonPlatformConstants.PLATFORM_PDD.equals(platformId) && StringUtils.isEmpty(appName)) {
            return CommonAppConstants.APP_GUANDIAN;
        }
        return appName;
    }


    /**
     * 获取默认的appname
     *
     * @param platformId
     * @param appName
     * @param defaultAppNameIfEmpty
     * @return
     */
    public static String defaultAppName(String platformId, String appName, String defaultAppNameIfEmpty) {
        if (StringUtils.isEmpty(appName)) {
            if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId) || StringUtils.isEmpty(platformId)) {
                return defaultAppNameIfEmpty;
            }
        }

        String finalAppName = defaultAppName(platformId, appName);
        if (StringUtils.isEmpty(finalAppName)) {
            return defaultAppNameIfEmpty;
        }

        return finalAppName;
    }
}
