<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.logistics.common.dao.trade.AyLogisticsCompanyDao">

    <resultMap type="cn.loveapp.logistics.common.entity.db.AyLogisticsCompany" id="AyLogisticsCompanyMap">
       <result property="id" column="id"/>
       <result property="companyName" column="company_name"/>
    </resultMap>

    <sql id="tableName">
        zzbtrade.ay_logistics_company
    </sql>

    <sql id="fields">
        id, company_name
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>(id, company_name)
        values (#{id}, #{companyName})
    </insert>


</mapper>
