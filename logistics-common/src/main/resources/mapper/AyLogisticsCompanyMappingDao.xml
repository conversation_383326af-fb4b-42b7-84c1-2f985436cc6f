<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.logistics.common.dao.trade.AyLogisticsCompanyMappingDao">

    <resultMap type="cn.loveapp.logistics.common.entity.db.AyLogisticsCompanyMapping" id="AyLogisticsCompanyMappingMap">
       <result property="id" column="id"/>
       <result property="companyCode" column="company_code"/>
       <result property="companyName" column="company_name"/>
       <result property="companyId" column="company_id"/>
       <result property="logisticsStoreId" column="logistics_store_id"/>
       <result property="ayLogisticsCompanyId" column="ay_logistics_company_id"/>
       <result property="flag" column="flag"/>
    </resultMap>

    <sql id="tableName">
        zzbtrade.ay_logistics_company_mapping
    </sql>

    <sql id="fields">
        id, company_code, company_name, company_id, ay_logistics_company_id, flag
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>(id, company_code, company_name, company_id, ay_logistics_company_id, logistics_store_id, flag)
        values (#{id}, #{companyCode}, #{companyName}, #{companyId}, #{ayLogisticsCompanyId}, #{logisticsStoreId}, #{flag})
    </insert>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/> (company_code, company_name, company_id, ay_logistics_company_id, logistics_store_id, flag)
        values
        <foreach collection="mappingList" item="item" separator=",">
            (#{item.companyCode}, #{item.companyName}, #{item.companyId}, #{item.ayLogisticsCompanyId}, #{item.logisticsStoreId}, #{item.flag})
        </foreach>
    </insert>

    <update id="update">
        update
        <include refid="tableName"/>
        <set>
            <if test="mapping.companyCode != null and mapping.companyCode !=''">
                company_code = #{mapping.companyCode},
            </if>
            <if test="mapping.companyName != null and mapping.companyName !=''">
                company_name = #{mapping.companyName},
            </if>
            <if test="mapping.ayLogisticsCompanyId != null and mapping.ayLogisticsCompanyId !=''">
                ay_logistics_company_id = #{mapping.ayLogisticsCompanyId},
            </if>
            <if test="mapping.companyId != null and mapping.companyId !=''">
                company_id = #{mapping.companyId},
            </if>
            <if test="mapping.logisticsStoreId != null and mapping.logisticsStoreId !=''">
                logistics_store_id = #{mapping.logisticsStoreId},
            </if>
            flag = #{mapping.flag}
        </set>
        where id = #{mapping.id}
    </update>

    <select id="queryByCompanyIdAndStoreId" resultMap="AyLogisticsCompanyMappingMap">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where logistics_store_id = #{logisticsStoreId}
        and company_id = #{sourceCompanyId}
        limit 1;
    </select>

    <select id="queryByCompanyNameAndStoreId" resultMap="AyLogisticsCompanyMappingMap">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where logistics_store_id = #{logisticsStoreId}
        and company_name = #{sourceCompanyName}
        limit 1;
    </select>

    <select id="queryByCompanyCodeAndStoreId" resultMap="AyLogisticsCompanyMappingMap">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where logistics_store_id = #{logisticsStoreId}
        and company_code = #{sourceCompanyCode}
        limit 1;
    </select>

    <select id="queryByAyIdAndStoreId" resultMap="AyLogisticsCompanyMappingMap">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where logistics_store_id = #{targetLogisticsStoreId}
        and ay_logistics_company_id = #{ayLogisticsCompanyId}
        limit 1;
    </select>

</mapper>
