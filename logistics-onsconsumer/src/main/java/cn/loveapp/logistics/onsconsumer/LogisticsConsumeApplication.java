package cn.loveapp.logistics.onsconsumer;

import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 *
 * @version 1.0 删除冗余代码 2018-11-23
 */

@EnableScheduling
@EnableCaching
@EnableFeignClients(basePackages = {"cn.loveapp.uac","cn.loveapp.logistics.common.service.external"})
@SpringBootApplication(exclude = {MybatisAutoConfiguration.class, DataSourceHealthContributorAutoConfiguration.class},
    scanBasePackages = {"cn.loveapp.logistics"})
public class LogisticsConsumeApplication {
    /**
     * Description 程序主入口
     *
     * <AUTHOR>
     * @date 2018-09-21 23:37
     */
    public static void main(String[] args) {
        new SpringApplicationBuilder(LogisticsConsumeApplication.class).application().run(args);
    }
}
