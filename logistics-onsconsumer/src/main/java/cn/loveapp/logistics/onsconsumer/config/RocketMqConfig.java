package cn.loveapp.logistics.onsconsumer.config;

import cn.loveapp.common.mq.rocketmq.CommonDefaultMQPushConsumer;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.config.rocketmq.RocketMQLogisticsOrderConfig;
import cn.loveapp.logistics.onsconsumer.consumer.LogisticConsumer;
import cn.loveapp.logistics.onsconsumer.consumer.LogisticsOrderConsumer;
import lombok.Setter;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ONSConfig
 *
 * <AUTHOR>
 * @date 2018/9/21
 */
@Configuration
public class RocketMqConfig {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RocketMqConfig.class);

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private RocketMQLogisticsOrderConfig rocketMQLogisticsOrderConfig;

    @Bean(destroyMethod = "shutdown", name = "smsDefaultProducer")
    public DefaultMQProducer smsDefaultProducer() throws Exception {
        // 启动ONS消息队列
        DefaultMQProducer smsProducer = new DefaultMQProducer(logisticsConfig.getSmsProducerId());
        smsProducer.setSendMsgTimeout(5000);
        smsProducer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
        smsProducer.start();
        LOGGER.logInfo("Sms Producer started");
        return smsProducer;
    }

    @Bean(destroyMethod = "shutdown", name = "logisticsMQConsumer")
    public DefaultMQPushConsumer logisticsRocketMqConsumer() {
        DefaultMQPushConsumer logisticsMQConsumer = new CommonDefaultMQPushConsumer(logisticsConfig.getGroupId());
        logisticsMQConsumer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
        logisticsMQConsumer.setConsumeThreadMax(logisticsConfig.getLogisticsConsumeThreadNums());
        logisticsMQConsumer.setConsumeThreadMin(logisticsConfig.getLogisticsConsumeThreadNums());
        logisticsMQConsumer.setAwaitTerminationMillisWhenShutdown(60_000);
        return logisticsMQConsumer;
    }


    @Bean(destroyMethod = "shutdown", name = "logisticsOrderMQConsumer")
    public DefaultMQPushConsumer logisticsOrderRocketMqConsumer() {
        DefaultMQPushConsumer logisticsOrderMQConsumer = new CommonDefaultMQPushConsumer(rocketMQLogisticsOrderConfig.getConsumerId());
        logisticsOrderMQConsumer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
        logisticsOrderMQConsumer.setConsumeThreadMax(rocketMQLogisticsOrderConfig.getMaxThreadNum());
        logisticsOrderMQConsumer.setConsumeThreadMin(rocketMQLogisticsOrderConfig.getMaxThreadNum());
        logisticsOrderMQConsumer.setAwaitTerminationMillisWhenShutdown(60_000);
        return logisticsOrderMQConsumer;
    }

    @Bean
    public RocketMqLifeCycleManager rocketMqLifeCycleManager() {
        return new RocketMqLifeCycleManager();
    }

    /**
     * Ons 生命周期管理
     *
     * <AUTHOR>
     * @date 2018/11/9
     */
    @Setter
    public static class RocketMqLifeCycleManager implements CommandLineRunner {
        private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RocketMqLifeCycleManager.class);

        @Autowired
        @Qualifier("smsDefaultProducer")
        private DefaultMQProducer smsProducer;

        @Autowired
        @Qualifier("logisticsMQConsumer")
        private DefaultMQPushConsumer logisticsRocketMqConsumer;

        @Autowired
        @Qualifier("logisticsOrderMQConsumer")
        private DefaultMQPushConsumer logisticsOrderRocketMqConsumer;

        @Autowired
        private LogisticConsumer logisticConsumer;

        @Autowired
        private LogisticsOrderConsumer logisticsOrderConsumer;

        @Autowired
        private LogisticsConfig logisticsConfig;

        @Autowired
        private RocketMQLogisticsOrderConfig rocketMQLogisticsOrderConfig;

        @Override
        public void run(String... args) throws Exception {
            // 启动物流轨迹ONS消费者
            if (logisticsRocketMqConsumer != null) {
                logisticsRocketMqConsumer.subscribe(logisticsConfig.getTopic(), "*");
                logisticsRocketMqConsumer.setMessageListener(logisticConsumer);
                logisticsRocketMqConsumer.start();
                LOGGER.logInfo("Logistic RocketMq Consumer is started, topic:" + logisticsConfig.getTopic());
            }

            // 启动物流单ONS消费者
            if (logisticsOrderRocketMqConsumer != null) {
                logisticsOrderRocketMqConsumer.subscribe(rocketMQLogisticsOrderConfig.getTopic(), "*");
                logisticsOrderRocketMqConsumer.setMessageListener(logisticsOrderConsumer);
                logisticsOrderRocketMqConsumer.start();
                LOGGER.logInfo("LogisticOrder RocketMq Consumer is started, topic:" + rocketMQLogisticsOrderConfig.getTopic());
            }
        }
    }
}
