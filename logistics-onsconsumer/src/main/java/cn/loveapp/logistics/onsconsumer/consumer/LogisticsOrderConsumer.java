package cn.loveapp.logistics.onsconsumer.consumer;

import java.util.List;
import java.util.Objects;

import cn.loveapp.logistics.api.dto.OrderInfoChangeDTO;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import cn.loveapp.common.serialization.MessageDeserializationResult;
import cn.loveapp.common.mq.AbstractCommonMQBaseConsumer;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.dto.LogisticsOrderChangeDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsOrderRequestProto;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.LogisticsOrderHandleService;
import cn.loveapp.logistics.common.service.LogisticsTraceHandleService;
import cn.loveapp.logistics.common.service.UserCenterService;
import cn.loveapp.uac.request.UserInfoRequest;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * LogisticsOrderConsumer 异常物流消费consumer
 *
 * <AUTHOR>
 * @date 2023/6/21
 */
@Component
public class LogisticsOrderConsumer extends AbstractCommonMQBaseConsumer {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderConsumer.class);

    /**
     * ONS限流配置Key
     */
    protected static final String LIMIT_CONFIG_KEY = "logistics.order.consumer.ratelimit";


    @Autowired
    private LogisticsTraceHandleService logisticsTraceHandleService;

    @Autowired
    private LogisticsOrderHandleService logisticsOrderHandleService;

    @Autowired
    private UserCenterService userCenterService;

    public LogisticsOrderConsumer(MeterRegistry registry, Environment environment) {
        super(registry, "物流单消息消费.QPS", LIMIT_CONFIG_KEY,
            environment, false, true);
    }

    @Override
    protected ConsumeConcurrentlyStatus execute(MessageExt message, MessageDeserializationResult messageDeserializationResult) throws Exception {
        LogisticsOrderRequestProto proto = (LogisticsOrderRequestProto) messageDeserializationResult.getContent();
        if (Objects.isNull(proto)) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        List<LogisticsOrderSubscribeDTO> logisticsHandles = proto.getLogisticsHandles();
        List<LogisticsOrderChangeDTO> logisticsUpdateInfos = proto.getLogisticsUpdateInfos();
        OrderInfoChangeDTO orderInfoChangeDTO = proto.getOrderInfoChange();
        if (CollectionUtils.isNotEmpty(logisticsHandles)) {
            handleLogisticsHandles(logisticsHandles);
        } else if (CollectionUtils.isNotEmpty(logisticsUpdateInfos)) {
            handleLogisticsUpdateInfos(logisticsUpdateInfos);
        } else if (orderInfoChangeDTO != null) {
            // 物流单标记已处理消息
            handleLogisticsOrderChange(orderInfoChangeDTO);
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理物流订单信息变更
     *
     * @param logisticsOderChangeInfo
     * @throws LogisticsHandlesException
     */
    private void handleLogisticsOrderChange(OrderInfoChangeDTO logisticsOderChangeInfo)
        throws LogisticsHandlesException {
        List<String> outSids = logisticsOderChangeInfo.getOutSids();
        if (CollectionUtils.isEmpty(outSids)) {
            return;
        }

        UserInfoRequest userInfoRequest = new UserInfoRequest();
        userInfoRequest.setApp(logisticsOderChangeInfo.getAppName());
        userInfoRequest.setSellerId(logisticsOderChangeInfo.getSellerId());
        userInfoRequest.setPlatformId(logisticsOderChangeInfo.getStoreId());
        userInfoRequest.setSellerNick(logisticsOderChangeInfo.getSellerNick());
        boolean isLogisticsToSubscribe = userCenterService.checkLogisticsMonitorEnable(BusinessType.order,
            userInfoRequest, null, logisticsOderChangeInfo.getAppName(), logisticsOderChangeInfo.getStoreId());

        if (!isLogisticsToSubscribe) {
            LOGGER.logInfo("监控开关未打开，忽略消息");
        } else {
            logisticsOrderHandleService.logisticsInfoUpdateByOrderInfo(logisticsOderChangeInfo);
        }
    }

    /**
     * 处理物流信息
     *
     * @param logisticsHandles
     * @throws LogisticsHandlesException
     */
    private void handleLogisticsHandles(List<LogisticsOrderSubscribeDTO> logisticsHandles) throws LogisticsHandlesException {
        for (LogisticsOrderSubscribeDTO logisticsHandle : logisticsHandles) {
            String sellerId = logisticsHandle.getSellerId();
            String appName = logisticsHandle.getAppName();
            String storeId = logisticsHandle.getStoreId();
            String sellerNick = logisticsHandle.getSellerNick();

            UserInfoRequest userInfoRequest = new UserInfoRequest();
            userInfoRequest.setApp(appName);
            userInfoRequest.setSellerId(sellerId);
            userInfoRequest.setPlatformId(storeId);
            userInfoRequest.setSellerNick(sellerNick);
            String logisticsAppName = logisticsHandle.getLogisticsAppName();
            String logisticsStoreId = LogisticsUtil.getDefaultLogisticsStoreId(logisticsHandle.getLogisticsStoreId());
            logisticsHandle.setLogisticsStoreId(logisticsStoreId);
            boolean isLogisticsToSubscribe = userCenterService.checkLogisticsMonitorEnable(
                logisticsHandle.getBusinessType(), userInfoRequest, null, logisticsAppName, logisticsStoreId);
            if (!isLogisticsToSubscribe) {
                LOGGER.logInfo("监控开关未打开，跳过");
                continue;
            }

            logisticsTraceHandleService.subscribeLogisticsTrace(logisticsHandle);
        }
    }

    /**
     * 处理物流变更消息
     *
      * @param logisticsUpdateInfos
     */
    private void handleLogisticsUpdateInfos(List<LogisticsOrderChangeDTO> logisticsUpdateInfos) {
        for (LogisticsOrderChangeDTO logisticsUpdateInfo : logisticsUpdateInfos) {
            // 物流单标记已处理消息
            UserInfoRequest userInfoRequest = new UserInfoRequest();
            userInfoRequest.setApp(logisticsUpdateInfo.getAppName());
            userInfoRequest.setSellerId(logisticsUpdateInfo.getSellerId());
            userInfoRequest.setPlatformId(logisticsUpdateInfo.getStoreId());
            userInfoRequest.setSellerNick(logisticsUpdateInfo.getSellerNick());
            boolean isLogisticsToSubscribe =
                userCenterService.checkLogisticsMonitorEnable(BusinessType.order, userInfoRequest,
                    Lists.newArrayList(
                        UserAbnormalSettingConstant.LOGISTICS_REFUND_ORDER_AUTO_MARK_PROCESSED_ENABLE),
                    logisticsUpdateInfo.getAppName(), logisticsUpdateInfo.getStoreId());

            if (!isLogisticsToSubscribe) {
                LOGGER.logInfo("监控开关未打开，忽略消息");
            } else {
                try {
                    logisticsOrderHandleService.logisticsInfoUpdate(logisticsUpdateInfo);
                } catch (LogisticsHandlesException e) {
                    LOGGER.logInfo("物流单状态处理消息消费异常: " + e.getMessage());
                }
            }
        }
    }

    @Override
    protected Class<?> getClassForDeserialization(MessageExt msg) {
        return LogisticsOrderRequestProto.class;
    }

}
