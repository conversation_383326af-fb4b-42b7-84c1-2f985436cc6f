package cn.loveapp.logistics.onsconsumer.controller;

import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import cn.loveapp.logistics.common.controller.BaseCloudNativeController;

/**
 * 探活Controller
 *
 * <AUTHOR>
 * @date 2019-04-16
 */
@Controller
@RequestMapping("/")
public class CloudNativeController extends BaseCloudNativeController {

    public CloudNativeController(ObjectProvider<List<DataSource>> provider) {
        super(provider);
    }
}
