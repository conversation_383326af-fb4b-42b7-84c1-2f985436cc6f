package cn.loveapp.logistics.onsconsumer.dao.dream;

import cn.loveapp.logistics.onsconsumer.entity.UserProductinfoTrade;

/**
 * 爱用交易用户相关信息(user_productinfo_trade)表数据库访问层
 *
 * <AUTHOR>
 * @date 2018-10-26 14:51:25
 */
public interface UserProductinfoTradeDao {

    /**
     * 通过nick查询单条数据的UserId和Vipflag
     *
     * @param nick
     *            昵称
     * @return 实例对象
     */
    UserProductinfoTrade queryUserIdAndVipflagByNick(String nick);

}
