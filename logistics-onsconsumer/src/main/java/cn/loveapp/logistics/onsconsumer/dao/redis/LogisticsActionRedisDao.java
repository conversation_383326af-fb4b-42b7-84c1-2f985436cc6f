package cn.loveapp.logistics.onsconsumer.dao.redis;

import cn.loveapp.common.utils.LoggerHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 物流事件统计redis缓存DAO
 * <AUTHOR>
 * @Date 2023/5/19 12:12
 */
@Component
public class LogisticsActionRedisDao {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsActionRedisDao.class);

    private static final String PREFIX_ORDER_KEY = "logistics:action:storeId:";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private String initKey(String action, String storeId) {
        return PREFIX_ORDER_KEY + action + ";" + storeId;
    }

    /**
     * 判断当前action是否已保存
     *
     * @param action
     * @param storeId
     * @return
     */
    public boolean hasExistAction(String action, String storeId) {
        if (StringUtils.isAllEmpty(action, storeId)) {
            return false;
        }
        return this.hasExistCollection(initKey(action, storeId));
    }

    public boolean setActionToRedis(String action, String storeId, String desc) {
        String k = initKey(action, storeId);
        return put(k, desc);
    }

    /**
     * 判断是否存在
     *
     * @param collection
     * @return
     */
    private Boolean hasExistCollection(String collection) {
        try {
            if (StringUtils.isEmpty(collection)) {
                return false;
            }
            return stringRedisTemplate.hasKey(collection);
        } catch (Exception e) {
            if (e.getMessage() == null) {
                LOGGER.logError("Entry '" + collection + "' does not exist in cache", e);
            } else {
                LOGGER.logError("Unable to find entry '" + collection + "' in cache collection '" + collection + "': " + e.getMessage() + "", e);
            }
            return false;
        }
    }


    private boolean put(String collection, String values) {
        if (StringUtils.isEmpty(collection)) {
            return false;
        }
        try {
            stringRedisTemplate.opsForValue().set(collection, values, 14, TimeUnit.DAYS);
            return true;
        } catch (Exception e) {
            LOGGER.logError("Unable to add object of key " + collection + " to cache collection '" + collection + "': " + e.getMessage() + "", e);
            return false;
        }
    }


}
