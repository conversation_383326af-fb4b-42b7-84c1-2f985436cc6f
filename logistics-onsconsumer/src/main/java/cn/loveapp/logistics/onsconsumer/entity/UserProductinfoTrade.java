package cn.loveapp.logistics.onsconsumer.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 爱用交易用户相关信息(user_productinfo_trade)实体类
 *
 * <AUTHOR>
 * @date 2018-10-26 14:51:25
 */
@Data
public class UserProductinfoTrade implements Serializable {
    private static final long serialVersionUID = 448343881509918818L;

    private Integer id;

    /**
     * 用户数字ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String nick;

    /**
     * 用户第一次使用爱用交易的时间
     */
    private Date createdate;

    /**
     * 最后一次活动时间，用触发器更新主表
     */
    private Date lastactivedt;

    /**
     * 0-初级班，1-高级版
     */
    private Integer vipflag;

    /**
     * 创建用户时用的IP地址
     */
    private String createipaddress;

    /**
     * 最后一次IP地址
     */
    private String lastipaddress;

    /**
     * TOP 初级版、高级版都有这个字段 订购到期时间
     */
    private Date orderCycleEnd;

    /**
     * 高级版-加上赠送时间的最终到期时间 初级班-就是TOP到期时间
     */
    private Date subdatetime;

    /**
     * TOP的Session
     */
    private String topsessionkey;

    /**
     * TOP的刷新Key
     */
    private String toprefreshkey;

    /**
     * 用于灰度发布时控制用户发布区域
     */
    private String roleid;

    /**
     * 0-非默认插件 1-默认插件
     */
    private Integer isdefault;

    /**
     * 是否已经试用过旺旺催付功能 1-表示还没有 0-表示已经试用过了
     */
    private Integer wwpayflag;

    /**
     * 本记录最后编辑的时间
     */
    private Date lastupdatetime;

    /**
     * 累计PC登陆次数
     */
    private Integer logincountPc;

    /**
     * 累计手机登陆次数
     */
    private Integer logincountMp;

    /**
     * 累计旺旺插件打开测试
     */
    private Integer logincountWw;

    /**
     * 30天内累计PC登陆次数
     */
    private Integer mauPc;

    /**
     * 30天内累计手机登陆次数
     */
    private Integer mauMp;

    /**
     * 30天内累计旺旺插件登陆次数
     */
    private Integer mauWw;

    private Integer sessionver;

    /**
     * H5/QAP/PC/PCWW
     */
    private String lastactiveplatform;

    /**
     * 最后活跃的版本号
     */
    private String lastactivever;

    /**
     * 在哪个库
     */
    private String db;

    /**
     * 0不是多店铺，1是多店铺
     */
    private Integer isMany;

    /**
     * 是否是主店铺 0 否，1是主店铺
     */
    private Integer isMain;

    /**
     * 主店铺id
     */
    private String corpId;

    /**
     * tmark打标
     */
    private String mark;

    /**
     * 是否沉默
     */
    private Integer isSilent;

    /**
     * 是否需要授权字段
     */
    private Integer isNeedauth;

    /**
     * w1授权到期时间
     */
    private Date w1Deadline;

    /**
     * w2授权到期时间
     */
    private Date w2Deadline;

}
