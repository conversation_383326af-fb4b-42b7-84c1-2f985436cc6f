package cn.loveapp.logistics.onsconsumer.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.logistics.onsconsumer.dao.dream.UserProductinfoTradeDao;
import cn.loveapp.logistics.onsconsumer.entity.UserProductinfoTrade;
import cn.loveapp.logistics.onsconsumer.service.TaobaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.net.URLEncoder;

/**
 * TaobaoService
 *
 * <AUTHOR>
 * @date 2018/11/27
 */
@Component
public class TaobaoServiceImpl implements TaobaoService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaobaoService.class);
    protected static final String TAOBAO_USER_ID = "taobao_user_id";

    @Value("${logistics.redis.rebuilduser.url:}")
    private String redisRebuildUserUrl;

    @Autowired
    private StringRedisTemplate redisHelper;

    @Autowired
    private UserProductinfoTradeDao userProductinfoTradeDao;


    private void rebuild(String nick) throws IOException {
        if (!StringUtils.isEmpty(redisRebuildUserUrl)) {
            // 重建用户Redis信息
            String url = redisRebuildUserUrl + "?nick=" + nick;
            http(url);
            LOGGER.logInfo(nick, "-", "重建用户Redis信息");
        }
    }

    protected void http(String url) throws IOException {
        NetworkUtil.http(url, null, false, null, null, false, false, null);
    }


    @Override
    public String getUserId(String nick, String tid) {
        try {
            try {
                HashOperations<String, String, String> op = redisHelper.opsForHash();
                String key = URLEncoder.encode(nick, "utf-8");
                String userIdStr = op.get(key, TAOBAO_USER_ID);
                if (!StringUtils.isEmpty(userIdStr)) {
                    return userIdStr;
                }
                rebuild(nick);
                userIdStr = op.get(key, TAOBAO_USER_ID);
                if (!StringUtils.isEmpty(userIdStr)) {
                    return userIdStr;
                }
            } catch (Exception e) {
                LOGGER.logError(nick, "-", "redis获取sellerId失败: " + e.getMessage(), e);
            }
            UserProductinfoTrade user = userProductinfoTradeDao.queryUserIdAndVipflagByNick(nick);
            if (user != null && user.getUserId() != null) {
                return String.valueOf(user.getUserId());
            } else {
                LOGGER.logError(nick, tid, "数据库中不存在user_id");
                return EMPTY_USER_ID;
            }
        } catch (DataAccessException e) {
            LOGGER.logError(nick, tid, "获取user_id连接数据库失败", e);
            return EMPTY_USER_ID;
        }
    }
}
