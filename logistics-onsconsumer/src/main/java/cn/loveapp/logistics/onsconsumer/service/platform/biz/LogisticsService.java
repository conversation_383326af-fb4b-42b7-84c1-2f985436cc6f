package cn.loveapp.logistics.onsconsumer.service.platform.biz;

import java.io.IOException;
import java.util.List;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;

/**
 * 物流服务
 *
 * <AUTHOR>
 * @date 2018/12/3
 */
public interface LogisticsService extends CommonPlatformHandler {

    /**
     * 擦护性能最后的action
     *
     * @param logisticsInfo
     * @return
     */
    String getLastLogisticsAction(LogisticsInfoDTO logisticsInfo, String logisticsStoreId, String logisticsAppName);

    /**
     * 批量保存同一运单号的多条物流轨迹
     *
     * @param logisticsInfos
     * @param oMsg
     * @param isAbnormalAutoCheckUser
     * @param checkModified
     * @param logisticsStoreId
     * @param logisticsAppName
     * @return
     * @throws IOException
     */
    boolean saveLogisticInfo(List<LogisticsInfoDTO> logisticsInfos, LogisticsTraceRequestProto oMsg,
        Boolean isAbnormalAutoCheckUser, boolean checkModified, String logisticsStoreId, String logisticsAppName)
        throws IOException;

    /**
     * 停止物流存储
     */
    void stop(String logisticsStoreId, String logisticsAppName);
}
