package cn.loveapp.logistics.onsconsumer.service.platform.biz.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.config.AyLogisticsStatusConfig;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dao.mongo.LogisticsTraceInfoDao;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.LogisticsTraceHandleService;
import cn.loveapp.logistics.onsconsumer.service.platform.biz.LogisticsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/6/26 10:58
 */
public abstract class AbstractLogisticsServiceImpl implements LogisticsService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractLogisticsServiceImpl.class);

    @Autowired
    private LogisticsTraceInfoDao logisticsTraceInfoDao;

    @Autowired
    private LogisticsOrderInfoDao logisticsOrderInfoDao;

    @Autowired
    private LogisticsTraceHandleService logisticsTraceHandleService;

    @Autowired
    private AyLogisticsStatusConfig ayLogisticsStatusConfig;

    @Override
    public String getLastLogisticsAction(LogisticsInfoDTO logisticsInfo, String logisticsStoreId, String logisticsAppName) {
        if (logisticsInfo == null) {
            return null;
        }
        LogisticsTraceInfo traceInfo =
            logisticsTraceInfoDao.queryLastByTidAndOutSid(LogisticsTraceInfo.fromLogisticsInfoDto(logisticsInfo));
        if (traceInfo == null) {
            return null;
        }
        return traceInfo.getAction();
    }


    @Override
    public boolean saveLogisticInfo(List<LogisticsInfoDTO> logisticsInfos, LogisticsTraceRequestProto oMsg,
        Boolean isAbnormalAutoCheckUser, boolean checkModified, String logisticsStoreId, String logisticsAppName)
        throws IOException {
        if (CollectionUtils.isEmpty(logisticsInfos)) {
            return false;
        }

        LogisticsInfoDTO logisticsInfoDTO = logisticsInfos.get(0);
        String outSid = logisticsInfoDTO.getOutSid();
        String sellerId = logisticsInfoDTO.getSellerId();
        String appName = logisticsInfoDTO.getAppName();
        String platformId = logisticsInfoDTO.getPlatformId();
        String companyCode = logisticsInfoDTO.getCompanyCode();
        String companyName = logisticsInfoDTO.getCompanyName();

        if (StringUtils.isEmpty(outSid)) {
            LOGGER.logError("缺少outSid，跳过");
            return false;
        }
        LogisticsOrderInfo logisticsOrderInfo = null;
        if (!StringUtils.isAnyEmpty(sellerId, appName, platformId)) {
            logisticsOrderInfo = logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, platformId, appName);
        } else {
            LOGGER.logError("", outSid, " 没有物流信息 outSid:" + outSid + ",companyCode:" + companyCode);
            return false;
        }
        boolean isHaveLastInfos = true;
        if (Objects.isNull(logisticsOrderInfo)) {
            // 没有订阅逻辑的如果存在物流单不存在时，更新物流轨迹时补充入库物流单
            LOGGER.logWarn("-", outSid, "无物流单记录，补充物流单信息");
            logisticsOrderInfo = new LogisticsOrderInfo();
            logisticsOrderInfo.setOutSid(outSid);
            logisticsOrderInfo.setSellerId(sellerId);
            logisticsOrderInfo.setStoreId(platformId);
            logisticsOrderInfo.setAppName(appName);
            logisticsOrderInfo.setCompanyCode(companyCode);
            logisticsOrderInfo.setCompanyName(companyName);
            LogisticsOrderInfo.BusinessInfo businessInfo = new LogisticsOrderInfo.BusinessInfo();
            // 售后物流各平台都依赖与订阅，此时入库只存在正向物流只有tid
            Set<String> tidSet = new HashSet<>();
            tidSet.add(logisticsInfoDTO.getTid());
            businessInfo.setTidList(tidSet);
            logisticsOrderInfo.setBusinessInfo(businessInfo);
            logisticsOrderInfo.setSaveLogisticsStoreId(getPlatformId());
            isHaveLastInfos = false;
        }

        List<LogisticsTraceInfo> logisticsTraces = new ArrayList<>();
        logisticsInfos.forEach(logisticsInfo -> {
            // 拉平物流轨迹状态
            logisticsInfo.setStatus(conversionActionToStatus(logisticsInfo.getAction()));
            logisticsInfo.setLogisticsStoreId(getPlatformId());
            LogisticsTraceInfo logisticsTraceInfo = generalLogisticsTraceInfo(logisticsInfo);
            logisticsTraces.add(logisticsTraceInfo);
        });

        LogisticsHandleBo logisticsHandleBo =
            LogisticsHandleBo.generalLogisticsHandleBo(logisticsOrderInfo, logisticsTraces, isAbnormalAutoCheckUser);
        logisticsHandleBo.setCheckModified(checkModified);
        if (!isHaveLastInfos) {
            logisticsHandleBo.setLogisticsOrderInfo(logisticsOrderInfo);
            logisticsHandleBo.setAllLogisticsTraceInfos(logisticsTraceHandleService.queryLogisticsTraceList(logisticsOrderInfo));
        }
        try {
            logisticsTraceHandleService.pullLogisticsTraceData(logisticsHandleBo);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError("物流入库失败：" + e.getMessage());
        }
        return true;
    }

    /**
     * 物流轨迹状态拉平
     *
     * @param action
     * @return
     */
    protected String conversionActionToStatus(String action) {
        AyLogisticsStatus status = ayLogisticsStatusConfig.getStatus(action, getPlatformId());
        return status.value();
    }

    /**
     * 生成物流轨迹入库信息
     *
     * @param logisticsInfo
     * @return
     */
    protected abstract LogisticsTraceInfo generalLogisticsTraceInfo(LogisticsInfoDTO logisticsInfo);


    @Override
    public void stop(String logisticsStoreId, String logisticsAppName) {

    }

}
