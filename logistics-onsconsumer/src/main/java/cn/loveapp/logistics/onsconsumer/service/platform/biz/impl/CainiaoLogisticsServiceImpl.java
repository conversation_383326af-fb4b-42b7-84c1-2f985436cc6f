package cn.loveapp.logistics.onsconsumer.service.platform.biz.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import cn.loveapp.logistics.common.dto.LogisticsCompanyMappingDTO;
import cn.loveapp.logistics.common.service.LogisticsOrderHandleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dao.mongo.LogisticsTraceInfoDao;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.LogisticsTraceHandleService;
import cn.loveapp.logistics.onsconsumer.service.platform.biz.LogisticsService;

/**
 * <AUTHOR>
 * @date 2024-10-29 14:41
 * @description: 菜鸟物流实现service
 */
@Service
public class CainiaoLogisticsServiceImpl implements LogisticsService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CainiaoLogisticsServiceImpl.class);

    @Autowired
    private LogisticsTraceInfoDao logisticsTraceInfoDao;

    @Autowired
    private LogisticsOrderInfoDao logisticsOrderInfoDao;

    @Autowired
    private LogisticsTraceHandleService logisticsTraceHandleService;

    @Autowired
    private LogisticsOrderHandleService logisticsOrderHandleService;

    @Override
    public String getLastLogisticsAction(LogisticsInfoDTO logisticsInfo, String logisticsStoreId,
        String logisticsAppName) {
        if (logisticsInfo == null) {
            return null;
        }
        LogisticsTraceInfo traceInfo = logisticsTraceInfoDao
            .queryLastByTidAndOutSid(LogisticsTraceInfo.fromLogisticsInfoDtoMulti(logisticsInfo));
        if (traceInfo == null) {
            return null;
        }
        return traceInfo.getAction();
    }

    @Override
    public boolean saveLogisticInfo(List<LogisticsInfoDTO> logisticsInfos, LogisticsTraceRequestProto oMsg,
        Boolean isAbnormalAutoCheckUser, boolean checkModified, String logisticsStoreId, String logisticsAppName)
        throws IOException {
        if (CollectionUtils.isEmpty(logisticsInfos)) {
            return false;
        }

        LogisticsInfoDTO logisticsInfoDTO = logisticsInfos.get(0);
        String outSid = logisticsInfoDTO.getOutSid();
        Boolean needReSubscribe = logisticsInfoDTO.getIsNeedReSubscribe();
        String companyCode = logisticsInfoDTO.getCompanyCode();
        if (StringUtils.isAllEmpty(outSid, companyCode)) {
            LOGGER.logError("", outSid, " 没有物流信息 outSid:" + outSid + ",companyCode:" + companyCode);
            return false;
        }

        if (BooleanUtils.isTrue(needReSubscribe)) {
            // 重新订阅
            return reSubscribe(logisticsInfoDTO, logisticsStoreId, logisticsAppName);
        }

        // 菜鸟推送的物流格式为1个单号的所有记录所以物流公司是一致的 查一次物流公司信息映射
        LogisticsInfoDTO lastLogisticsInfoDTO = logisticsInfos.get(0);
        LogisticsCompanyMappingDTO logisticsCompanyMappingDTO =
            logisticsOrderHandleService.handelLogisticsCompanyMapping(lastLogisticsInfoDTO.getCompanyCode(),
                lastLogisticsInfoDTO.getCompanyName(), logisticsStoreId, CommonLogisticsConstants.PLATFORM_DEFAULT);

        List<LogisticsOrderInfo> logisticsOrderInfos =
            logisticsOrderInfoDao.queryByOutSidAndCompany(outSid, logisticsCompanyMappingDTO.getTargetCompanyCode(),
                null, logisticsInfoDTO.getPlatformId(), logisticsInfoDTO.getAppName());
        if (CollectionUtils.isEmpty(logisticsOrderInfos)) {
            LOGGER.logWarn("-", outSid, "无订阅记录，尝试重新订阅");
            return reSubscribe(logisticsInfoDTO, logisticsStoreId, logisticsAppName);
        }

        List<LogisticsTraceInfo> logisticsTraces = new ArrayList<>();
        logisticsInfos.forEach(logisticsInfo -> {
            LogisticsTraceInfo logisticsTraceInfo = LogisticsTraceInfo.fromLogisticsInfoDtoMulti(logisticsInfo);
            logisticsTraceInfo.setCompanyCode(logisticsCompanyMappingDTO.getTargetCompanyCode());
            logisticsTraceInfo.setCompanyName(logisticsCompanyMappingDTO.getTargetCompanyName());
            logisticsTraces.add(logisticsTraceInfo);
        });


        for (LogisticsOrderInfo logisticsOrderInfo : logisticsOrderInfos) {
            if (!CommonLogisticsConstants.PLATFORM_CAINIAO.equals(logisticsOrderInfo.getSaveLogisticsStoreId())) {
                // 入库是发现库里数据是快递鸟 说明之前订阅的是快递鸟，修正为菜鸟
                logisticsOrderInfo.setSaveLogisticsStoreId(CommonLogisticsConstants.PLATFORM_CAINIAO);
            }

            LogisticsHandleBo logisticsHandleBo =
                LogisticsHandleBo.generalLogisticsHandleBo(logisticsOrderInfo, logisticsTraces, isAbnormalAutoCheckUser);
            try {
                // 物流轨迹入库
                logisticsTraceHandleService.pullLogisticsTraceData(logisticsHandleBo);
            } catch (LogisticsHandlesException e) {
                LOGGER.logError("菜鸟：" + e.getMessage(), e);
            }
        }
        return true;
    }

    /**
     * 从新订阅物流
     *
     * @param logisticsInfoDTO
     * @param logisticsStoreId
     * @param logisticsAppName
     * @return
     */
    private boolean reSubscribe(LogisticsInfoDTO logisticsInfoDTO, String logisticsStoreId, String logisticsAppName) {
        String outSid = logisticsInfoDTO.getOutSid();
        String sellerId = logisticsInfoDTO.getSellerId();
        String appName = logisticsInfoDTO.getAppName();
        String storeId = logisticsInfoDTO.getPlatformId();

        LOGGER.logInfo("重新订阅菜鸟物流：" + outSid);
        LogisticsOrderSubscribeDTO logisticsHandle = new LogisticsOrderSubscribeDTO();
        logisticsHandle.setLogisticsStoreId(logisticsStoreId);
        logisticsHandle.setLogisticsAppName(logisticsAppName);
        logisticsHandle.setStoreId(storeId);
        logisticsHandle.setAppName(appName);
        logisticsHandle.setSellerId(sellerId);
        logisticsHandle.setSellerNick(logisticsInfoDTO.getSellerNick());
        logisticsHandle.setLogisticsCompanyCode(logisticsInfoDTO.getCompanyCode());
        logisticsHandle.setOutSid(logisticsInfoDTO.getOutSid());

        try {
            return logisticsTraceHandleService.subscribeLogisticsTrace(logisticsHandle);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError(sellerId, outSid, "重新订阅菜鸟物流失败：" + e.getMessage(), e);
        }

        return false;
    }

    @Override
    public void stop(String logisticsStoreId, String logisticsAppName) {

    }

    @Override
    public String getPlatformId() {
        return CommonLogisticsConstants.PLATFORM_CAINIAO;
    }
}
