package cn.loveapp.logistics.onsconsumer.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.config.LogisticsActions;
import cn.loveapp.logistics.common.config.LogisticsConsumerProperties;
import cn.loveapp.logistics.common.dao.mongo.LogisticsTraceInfoDao;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.service.UserInfoService;
import cn.loveapp.logistics.onsconsumer.dao.redis.LogisticsActionRedisDao;
import cn.loveapp.logistics.onsconsumer.dao.trade.LogisticsActionTmpDao;
import cn.loveapp.logistics.onsconsumer.service.TaobaoService;
import cn.loveapp.logistics.onsconsumer.service.platform.biz.LogisticsService;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * LogisticsService 默认物流入库逻辑
 *
 * <AUTHOR>
 * @date 2018年10月11日 下午8:31:47
 */
@Component
public class DefaultLogisticsServiceImpl implements LogisticsService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DefaultLogisticsServiceImpl.class);

    public static final String SIGNED = "SIGNED";
    public static final String STA_TOWN_IN = "STA_TOWN_IN";


    @Autowired
    private LogisticsConsumerProperties logisticsConsumerProperties;

    @Autowired
    private LogisticsTraceInfoDao logisticsTraceInfoDao;

    @Autowired
    private LogisticsActionTmpDao logisticsActionTmpDao;

    @Autowired
    private LogisticsActionRedisDao logisticsActionRedisDao;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private TaobaoService taobaoService;

    @Autowired
    private LogisticsActions logisticsActions;

    @Autowired
    @Qualifier("smsDefaultProducer")
    private DefaultMQProducer smsProducer;

    private Timer save2DbTimer;

    private ThreadLocal<List<LogisticsTraceInfo>> threadLocalLogisticsInfoList = new ThreadLocal<>();

    private CopyOnWriteArrayList<List<LogisticsTraceInfo>> logisticsInfoListQueue = new CopyOnWriteArrayList<>();

    private volatile boolean stopped = false;

    private FastDateFormat sdf = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    /**
     * 物流映射保存开关
     */
    @Value("${logistics.action.save.enable:true}")
    private boolean actionSaveEnable;

    @Value("${logistics.sms.enable:false}")
    protected boolean enableSms;

    @Value("${logistics.sms.checkSigned:false}")
    protected boolean enableCheckSigned;

    @Value("${logistics.taobao.ons.sms.topic}")
    private String smsTopic;

    @Value("${logistics.taobao.ons.sms.tag}")
    private String smsTag;

    @Value("${logistics.taobao.ons.sms.consumerurl}")
    private String smsConsumerUrl;

    public DefaultLogisticsServiceImpl(MeterRegistry registry) {
        save2DbTimer = registry.timer("物流入库.QPS");
    }

    @Override
    public void stop(String logisticsStoreId, String logisticsAppName) {
        if (stopped) {
            // 已经停止
            return;
        }
        stopped = true;
        try {
            Thread.sleep(500);
        } catch (InterruptedException ignored) {
        }
        for (List<LogisticsTraceInfo> logisticsTraceInfoList : logisticsInfoListQueue) {
            if (CollectionUtils.isNotEmpty(logisticsTraceInfoList)) {
                try {
                    batchSave(logisticsTraceInfoList);
                } catch (IOException e) {
                    LOGGER.logError("退出时保存数据异常: " + e.getMessage(), e);
                }
            }
        }
        LOGGER.logInfo("退出时保存数据结束");
    }


    public boolean saveLogisticInfo(LogisticsInfoDTO logisticsInfo) throws IOException {
        int batchSize = logisticsConsumerProperties.getBatchSize();
        List<LogisticsTraceInfo> list = threadLocalLogisticsInfoList.get();
        if (list == null) {
            list = new ArrayList<>(batchSize + 10);
            threadLocalLogisticsInfoList.set(list);
            logisticsInfoListQueue.add(list);
        }
        list.add(LogisticsTraceInfo.fromLogisticsInfoDto(logisticsInfo));
        if (stopped || list.size() >= batchSize) {
            batchSave(list);
            list.clear();
        }
        return true;
    }

    private void batchSave(List<LogisticsTraceInfo> list) throws IOException {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Timer.Sample sample = Timer.start();
        logisticsTraceInfoDao.save(list);
        saveAction2Tmp(list);
        sample.stop(save2DbTimer);
    }

    /**
     * 保存物流action
     *
     * @param list
     */
    private void saveAction2Tmp(List<LogisticsTraceInfo> list) {
        if (!actionSaveEnable) {
            return;
        }
        try {
            List<LogisticsTraceInfo> distinctList = new ArrayList<>(list.size());
            for (LogisticsTraceInfo traceInfo : list) {
                // 去除相同平台的相同action
                if (logisticsActionRedisDao.hasExistAction(traceInfo.getAction(), traceInfo.getPlatformId())) {
                    continue;
                }
                logisticsActionRedisDao.setActionToRedis(traceInfo.getAction(), traceInfo.getPlatformId(), traceInfo.getDesc());
                distinctList.add(traceInfo);
            }
            if (CollectionUtils.isNotEmpty(distinctList)) {
                logisticsActionTmpDao.insertOrUpdateBatch(distinctList);
            }
        } catch (Exception e) {
            LOGGER.logError("保存logistics_action_tmp失败：" + e.getMessage(), e);
        }
    }

    @Override
    public String getLastLogisticsAction(LogisticsInfoDTO logisticsInfo, String logisticsStoreId, String logisticsAppName) {
        if (logisticsInfo == null) {
            return null;
        }
        LogisticsTraceInfo traceInfo =
            logisticsTraceInfoDao.queryLastByTidAndOutSid(LogisticsTraceInfo.fromLogisticsInfoDto(logisticsInfo));
        if (traceInfo == null) {
            return null;
        }
        return traceInfo.getAction();
    }

    @Override
    public boolean saveLogisticInfo(List<LogisticsInfoDTO> logisticsInfos, LogisticsTraceRequestProto oMsg,
        Boolean isAbnormalAutoCheckUser, boolean checkModified, String logisticsStoreId, String logisticsAppName)
        throws IOException {
        for (LogisticsInfoDTO logisticsInfo : logisticsInfos) {
            String platform = logisticsInfo.getPlatformId();
            String tid = logisticsInfo.getTid();
            String appName = logisticsInfo.getAppName();
            String sellerNick = "";
            if (CommonPlatformConstants.PLATFORM_PDD.equals(platform)) {
                if (StringUtils.isEmpty(logisticsInfo.getSellerId())) {
                    LOGGER.logError("", tid, "PDD 没有sellerId, 跳过");
                    continue;
                }
                // 封装请求参数
                UserInfoRequest userInfoRequest = new UserInfoRequest();
                userInfoRequest.setApp(appName);
                userInfoRequest.setPlatformId(CommonPlatformConstants.PLATFORM_PDD);
                userInfoRequest.setSellerId(logisticsInfo.getSellerId());
                // 封装了uac的请求 先去缓存中查下
                UserInfoResponse userInfo = null;
                try {
                    userInfo = userInfoService.getSellerInfo(userInfoRequest);
                } catch (Exception e) {
                    LOGGER.logError("", tid, "从uac获取sellerNick异常: " + e.getMessage(), e);
                    continue;
                }
                if (userInfo == null || StringUtils.isEmpty(userInfo.getSellerNick())) {
                    // 没有取到 sellerNick
                    LOGGER.logError("", tid, "从uac获取sellerNick为空");
                    continue;
                }
                sellerNick = userInfo.getSellerNick();
                // 拼多多没有对省份的处理
                logisticsInfo.setProvince(StringUtils.EMPTY);
                logisticsInfo.setSellerNick(sellerNick);
            } else {
                sellerNick = logisticsInfo.getSellerNick();
                if (StringUtils.EMPTY.equals(sellerNick)) {
                    // 没有取到 sellerNick
                    LOGGER.logError("tid: " + tid, "", "sellerNick为空");
                    continue;
                }
            }
            Date modified = logisticsInfo.getModified();
            MDC.put("sellerNick", sellerNick);
            if (modified != null) {
                MDC.put("modified", sdf.format(modified));
            }
            MDC.put("tid", tid);

            if (StringUtils.isEmpty(logisticsInfo.getSellerId())
                && !CommonPlatformConstants.PLATFORM_PDD.equals(platform)) {
                // pdd的就不用查找
                logisticsInfo.setSellerId(taobaoService.getUserId(sellerNick, tid));
            }
            // pdd的物流信息不用发送短信
            if (CommonPlatformConstants.PLATFORM_PDD.equals(platform)) {
                // pdd的物流 没有订单号 用的是运单号 所以不用out_sid == tid验证
                if (StringUtils.isEmpty(logisticsInfo.getOutSid())) {
                    LOGGER.logInfo(sellerNick, tid, " 没有物流单号,跳过 ");
                    continue;
                }
            } else {
                sendSms(oMsg, logisticsInfo);
                if (StringUtils.isEmpty(logisticsInfo.getOutSid()) || logisticsInfo.getOutSid().equals(tid)) {
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.logInfo(sellerNick, tid, " 没有物流单号，尚未发货,跳过 ");
                    }
                    continue;
                }
            }

            if (StringUtils.isEmpty(logisticsInfo.getSellerId())
                || TaobaoService.EMPTY_USER_ID.equals(logisticsInfo.getSellerId())) {
                // 那就是根本没有拿到sellerId，那么需要跳过的
                LOGGER.logError(sellerNick, tid, " 没有卖家sellerId 流程无法执行，跳过 ");
                continue;
            }


            saveLogisticInfo(logisticsInfo);
        }
        return true;
    }


    /**
     * 发送短信请求
     *
     * @param logisticsTraceRequestProto
     * @param logisticsNotify
     * <AUTHOR>
     * @date 2018年10月11日 下午8:43:41
     */
    protected void sendSms(LogisticsTraceRequestProto logisticsTraceRequestProto, LogisticsInfoDTO logisticsNotify) {
        if (needSendSms(logisticsNotify)) {
            String sellerNick = logisticsNotify.getSellerNick();
            String tid = logisticsNotify.getTid();

            JSONObject oMsg = LogisticsTraceRequestProto.toOriginalJson(logisticsTraceRequestProto);
            oMsg.put("nick", sellerNick);
            oMsg.put("tid", tid);
            // 当符合这几种情况时，需要发送物流短信，提交ONS
            int retry = 4;
            for (int i = 0; i < retry; i++) {
                try {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("consumerurl", smsConsumerUrl);
                    map.put("parm", oMsg);
                    map.put("nick", sellerNick);
                    Map<String, Object> mapParm = Maps.newHashMap();
                    mapParm.put("custom", map);
                    mapParm.put("nick", sellerNick);
                    String jsonParm = JSON.toJSONString(mapParm);
                    byte[] msgBytes = jsonParm.getBytes(StandardCharsets.UTF_8);
                    Message onsMsg = new Message(
                        // Message Topic
                        smsTopic,
                        // Message Tag,
                        // 可理解为Gmail中的标签，对消息进行再归类，方便Consumer指定过滤条件在ONS服务器过滤
                        smsTag,
                        // Message Body
                        // 任何二进制形式的数据，ONS不做任何干预，需要Producer与Consumer协商好一致的序列化和反序列化方式
                        msgBytes);
                    SendResult sr = smsProducer.send(onsMsg);
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.logDebug(sellerNick, tid, "HTTP Send to Sms: " + jsonParm + ", return " + sr.getMsgId());
                    }
                } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
                    if (i < retry - 1) {
                        // 失败重试
                        LOGGER.logWarn(sellerNick, tid, "RocketMQ 发送短信消息失败, 开始第" + (i + 1) + "重试 :" + JSONObject.toJSONString(oMsg));
                        try {
                            Thread.sleep(1000 * (i + 1));
                        } catch (InterruptedException ex) {
                        }
                    } else {
                        // 失败重试
                        LOGGER.logError(sellerNick, tid, "RocketMQ 发送短信消息最终失败 :" + JSONObject.toJSONString(oMsg), e);
                    }
                    continue;
                }
                break;
            }
        }
    }

    /**
     * 是否需要发送短信
     *
     * @param logisticsNotify
     * @return
     */
    protected boolean needSendSms(LogisticsInfoDTO logisticsNotify) {
        if (!enableSms || !logisticsActions.getSms().contains(logisticsNotify.getAction())) {
            return false;
        }
        if (enableCheckSigned && SIGNED.equalsIgnoreCase(logisticsNotify.getAction())) {
            String lastAction = getLastLogisticsAction(logisticsNotify, logisticsNotify.getPlatformId(), logisticsNotify.getAppName());
            // 上个action是STA_TOWN_IN时, 基本是菜鸟乡村的快递员自己代收的情况, 不发短信
            if (STA_TOWN_IN.equalsIgnoreCase(lastAction)) {
                LOGGER.logInfo(logisticsNotify.getSellerNick(), logisticsNotify.getTid(),
                    "上次是STA_TOWN_IN, 快递员代收, 不发送签收短信");
                return false;
            }
        }
        return true;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_DEFAULT;
    }
}
