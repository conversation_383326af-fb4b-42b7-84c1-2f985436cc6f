package cn.loveapp.logistics.onsconsumer.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dao.mongo.LogisticsTraceInfoDao;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.LogisticsTraceHandleService;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.onsconsumer.service.platform.biz.LogisticsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

/**
 * 快递鸟物流实现service
 *
 * <AUTHOR>
 * @Date 2023/5/29 17:00
 */
@Service
public class KdniaoLogisticsServiceImpl implements LogisticsService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(KdniaoLogisticsServiceImpl.class);

    @Autowired
    private LogisticsTraceInfoDao logisticsTraceInfoDao;

    @Autowired
    private LogisticsOrderInfoDao logisticsOrderInfoDao;

    @Autowired
    private LogisticsTraceHandleService logisticsTraceHandleService;

    @Override
    public String getLastLogisticsAction(LogisticsInfoDTO logisticsInfo, String logisticsStoreId, String logisticsAppName) {
        if (logisticsInfo == null) {
            return null;
        }
        LogisticsTraceInfo traceInfo =
            logisticsTraceInfoDao.queryLastByTidAndOutSid(LogisticsTraceInfo.fromLogisticsInfoDtoMulti(logisticsInfo));
        if (traceInfo == null) {
            return null;
        }
        return traceInfo.getAction();
    }

    @Override
    public boolean saveLogisticInfo(List<LogisticsInfoDTO> logisticsInfos, LogisticsTraceRequestProto oMsg,
        Boolean isAbnormalAutoCheckUser, boolean checkModified, String logisticsStoreId, String logisticsAppName)
        throws IOException {
        if (CollectionUtils.isEmpty(logisticsInfos)) {
            return false;
        }

        LogisticsInfoDTO logisticsInfoDTO = logisticsInfos.get(0);
        String outSid = logisticsInfoDTO.getOutSid();
        Boolean needReSubscribe = logisticsInfoDTO.getIsNeedReSubscribe();
        String companyCode = logisticsInfoDTO.getCompanyCode();

        if (StringUtils.isAllEmpty(outSid, companyCode)) {
            LOGGER.logError("", outSid, " 没有物流信息 outSid:" + outSid + ",companyCode:" + companyCode);
            return false;
        }

        if (BooleanUtils.isTrue(needReSubscribe)) {
            // 重新订阅
            return reSubscribe(logisticsInfoDTO, logisticsStoreId, logisticsAppName);
        }

        List<LogisticsOrderInfo> logisticsOrderInfos = logisticsOrderInfoDao.queryByOutSidAndCompany(outSid, companyCode, null);
        if (CollectionUtils.isEmpty(logisticsOrderInfos)) {
            LOGGER.logWarn("-", outSid, "无订阅记录，尝试重新订阅");
            return reSubscribe(logisticsInfoDTO, logisticsStoreId, logisticsAppName);
        }

        List<LogisticsTraceInfo> logisticsTraces = new ArrayList<>();
        logisticsInfos.forEach(logisticsInfo -> {
            LogisticsTraceInfo logisticsTraceInfo = LogisticsTraceInfo.fromLogisticsInfoDtoMulti(logisticsInfo);
            logisticsTraces.add(logisticsTraceInfo);
        });

        for (LogisticsOrderInfo logisticsOrderInfo : logisticsOrderInfos) {
            LogisticsHandleBo logisticsHandleBo = LogisticsHandleBo.generalLogisticsHandleBo(logisticsOrderInfo,
                logisticsTraces, isAbnormalAutoCheckUser);
            try {
                //物流轨迹入库
                logisticsTraceHandleService.pullLogisticsTraceData(logisticsHandleBo);
            } catch (LogisticsHandlesException e) {
                LOGGER.logError("快递鸟：" + e.getMessage(), e);
            }
        }
        return true;
    }

    private boolean reSubscribe(LogisticsInfoDTO logisticsInfoDTO, String logisticsStoreId, String logisticsAppName) {
        String outSid = logisticsInfoDTO.getOutSid();
        String sellerId = logisticsInfoDTO.getSellerId();
        String appName = logisticsInfoDTO.getAppName();
        String storeId = logisticsInfoDTO.getPlatformId();
        // 重新订阅
        LOGGER.logInfo("重新订阅：" + outSid);
        LogisticsOrderSubscribeDTO logisticsHandle = new LogisticsOrderSubscribeDTO();
        logisticsHandle.setLogisticsStoreId(logisticsStoreId);
        logisticsHandle.setLogisticsAppName(logisticsAppName);
        logisticsHandle.setStoreId(storeId);
        logisticsHandle.setAppName(appName);
        logisticsHandle.setSellerId(sellerId);
        logisticsHandle.setSellerNick(logisticsInfoDTO.getSellerNick());
        logisticsHandle.setLogisticsCompanyCode(logisticsInfoDTO.getCompanyCode());
        logisticsHandle.setOutSid(logisticsInfoDTO.getOutSid());
        try {
            return logisticsTraceHandleService.subscribeLogisticsTrace(logisticsHandle);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError(sellerId, outSid, "重新订阅失败：" + e.getMessage(), e);
        }
        return false;
    }

    @Override
    public void stop(String logisticsStoreId, String logisticsAppName) {

    }

    @Override
    public String getPlatformId() {
        return CommonLogisticsConstants.PLATFORM_KDNIAO;
    }
}
