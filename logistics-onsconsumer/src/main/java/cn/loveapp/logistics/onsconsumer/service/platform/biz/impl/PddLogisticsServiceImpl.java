package cn.loveapp.logistics.onsconsumer.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import org.springframework.stereotype.Service;

/**
 * Pdd物流入库前处理
 *
 * <AUTHOR>
 * @Date 2023/6/26 10:02
 */
@Service
public class PddLogisticsServiceImpl extends AbstractLogisticsServiceImpl {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PddLogisticsServiceImpl.class);


    @Override
    protected LogisticsTraceInfo generalLogisticsTraceInfo(LogisticsInfoDTO logisticsInfo) {
        return LogisticsTraceInfo.fromLogisticsInfoDtoMulti(logisticsInfo);
    }

    @Override
    public String getPlatformId() {
        return CommonLogisticsConstants.PLATFORM_PDD;
    }
}
