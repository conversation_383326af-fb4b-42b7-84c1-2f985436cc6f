spring.application.name=logistics-onsconsumer
spring.profiles.active=dev
loveapp.apollo.enabled=true
app.id=cn.loveapp.logistics
apollo.bootstrap.enabled=${loveapp.apollo.enabled}
apollo.bootstrap.namespaces=logistics-onsconsumer,logistics-mongodb,application,service-registry
env=${spring.profiles.active}

server.tomcat.max-part-count=-1

spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=10000,expireAfterAccess=5m

spring.data.mongodb.repositories.type=none
spring.data.elasticsearch.repositories.enabled=false
