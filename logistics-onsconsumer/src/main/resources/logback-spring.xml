<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOG_INFO_FILE" value="logs/info.log"/>
    <property name="LOG_INFO_FILE_BACK" value="logs/info.%i.log"/>
    <property name="LOG_PATTERN" value="[%d{HH:mm:ss}] [%level] [%logger] %msg%n"/>
    <property name="MAX_FILE_SIZE" value="64MB"/>

    <springProperty scope="context" name="app.name" source="spring.application.name" defaultValue=""/>

    <!-- 控制台 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 全日志文件 -->
    <appender name="rollingFileInfo" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_INFO_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_INFO_FILE_BACK}</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>1</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
        </triggeringPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <shortenedLoggerNameLength>36</shortenedLoggerNameLength>
        </encoder>
    </appender>

    <!-- 异步输出全日志文件 -->
    <appender name="asyncRollingFileInfo" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>1024</queueSize>
        <!-- 超过队列长度，扔掉信息，不阻塞应用线程-->
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="rollingFileInfo"/>
    </appender>

    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <!--<shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook" />-->

    <!-- 开发环境 -->
    <springProfile name="dev,local">
        <!-- logger 配置 -->
        <logger name="RocketmqCommon" level="info" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
        <logger name="RocketmqClient" level="error" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
        <logger name="RocketmqRemoting" level="error" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
        <logger name="io.netty" level="error" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
        <logger name="com.cainiao.link.sdk" level="warn" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
        <logger name="cn.loveapp.logistics.onsconsumer.dao" level="info" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
        <logger name="cn.loveapp.logistics.onsconsumer" level="info" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
<!--        <logger name="cn.loveapp.uac" level="debug" additivity="false">-->
<!--            <appender-ref ref="STDOUT"/>-->
<!--        </logger>-->
        <logger name="cn.loveapp" level="info" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
        <root level="info">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>

    <!-- 生产环境 -->
    <springProfile name="prod">
        <logger name="RocketmqCommon" level="info" additivity="false">
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
        <logger name="RocketmqClient" level="error" additivity="false">
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
        <logger name="RocketmqRemoting" level="error" additivity="false">
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
        <logger name="io.netty" level="error" additivity="false">
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
        <logger name="com.cainiao.link.sdk" level="warn" additivity="false">
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
        <logger name="cn.loveapp.uac.utils.UserCacheUtils" level="warn" additivity="false">
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
        <logger name="cn.loveapp" level="info" additivity="false">
            <appender-ref ref="asyncRollingFileInfo"/>
        </logger>
        <root level="info">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="asyncRollingFileInfo"/>
        </root>
    </springProfile>

</configuration>
