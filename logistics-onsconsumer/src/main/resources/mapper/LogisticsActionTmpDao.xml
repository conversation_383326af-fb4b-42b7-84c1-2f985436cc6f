<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.logistics.onsconsumer.dao.trade.LogisticsActionTmpDao">


    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        insert into
        zzbtrade.logistics_action_tmp(action, description, store_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.action},#{item.desc},#{item.platformId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        `action` = values(`action`), description = values(description), store_id = values(store_id)
    </insert>
</mapper>
