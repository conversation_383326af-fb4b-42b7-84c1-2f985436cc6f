<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.logistics.onsconsumer.dao.dream.UserProductinfoTradeDao">

    <resultMap type="cn.loveapp.logistics.onsconsumer.entity.UserProductinfoTrade" id="UserProductinfoTradeMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="nick" column="nick"/>
        <result property="createdate" column="createdate"/>
        <result property="lastactivedt" column="lastactivedt"/>
        <result property="vipflag" column="vipflag"/>
        <result property="createipaddress" column="createipaddress"/>
        <result property="lastipaddress" column="lastipaddress"/>
        <result property="orderCycleEnd" column="order_cycle_end"/>
        <result property="subdatetime" column="subdatetime"/>
        <result property="topsessionkey" column="topsessionkey"/>
        <result property="toprefreshkey" column="toprefreshkey"/>
        <result property="roleid" column="roleid"/>
        <result property="isdefault" column="isdefault"/>
        <result property="wwpayflag" column="wwpayflag"/>
        <result property="lastupdatetime" column="lastupdatetime"/>
        <result property="logincountPc" column="logincount_pc"/>
        <result property="logincountMp" column="logincount_mp"/>
        <result property="logincountWw" column="logincount_ww"/>
        <result property="mauPc" column="mau_pc"/>
        <result property="mauMp" column="mau_mp"/>
        <result property="mauWw" column="mau_ww"/>
        <result property="sessionver" column="sessionver"/>
        <result property="lastactiveplatform" column="lastactiveplatform"/>
        <result property="lastactivever" column="lastactivever"/>
        <result property="db" column="db"/>
        <result property="isMany" column="is_many"/>
        <result property="isMain" column="is_main"/>
        <result property="corpId" column="corp_id"/>
        <result property="mark" column="mark"/>
        <result property="isSilent" column="is_silent"/>
        <result property="isNeedauth" column="is_needauth"/>
        <result property="w1Deadline" column="w1_deadline"/>
        <result property="w2Deadline" column="w2_deadline"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryUserIdAndVipflagByNick" resultMap="UserProductinfoTradeMap">
        select id, user_id, vipflag
        from user_productinfo_trade
        where nick = #{nick}
    </select>


</mapper>
