// package cn.loveapp.logistics.onsconsumer.task;
//
// import cn.loveapp.logistics.onsconsumer.TestConfig;
// import cn.loveapp.logistics.onsconsumer.service.LogisticsService;
// import org.apache.commons.lang3.time.FastDateFormat;
// import org.junit.Before;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.boot.test.mock.mockito.MockBean;
// import org.springframework.boot.test.mock.mockito.SpyBean;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.test.context.junit4.SpringRunner;
//
// import javax.sql.DataSource;
// import java.io.File;
// import java.io.IOException;
// import java.sql.Connection;
// import java.sql.SQLException;
// import java.util.ArrayList;
// import java.util.Calendar;
// import java.util.List;
//
// import static org.junit.Assert.*;
// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.BDDMockito.*;
//
/// **
// * CopyLogisticFromCsvJobTest
// *
// * <AUTHOR>
// * @date 2018年10月10日 下午8:32:52
// */
// @RunWith(SpringRunner.class)
// @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
// classes = {LogisticsBatch2DbFromFileJob.class, TestConfig.class})
// @ActiveProfiles("test")
// public class LogisticsBatch2DbFromFileJobTest {
//
// @MockBean(name = "batch2LogisticDataSources")
// private List<DataSource> batch2LogisticDataSources;
//
// private List<DataSource> mockedDataSources = new ArrayList<>();
//
// @MockBean
// private LogisticsService pgLogisticsService;
//
// @SpyBean
// private LogisticsBatch2DbFromFileJob logisticsBatch2DbFromFileJob;
//
// @Before
// public void setUp() throws Exception {
// logisticsBatch2DbFromFileJob.init();
// logisticsBatch2DbFromFileJob.setCsvDir(
// new File(LogisticsBatch2DbFromFileJobTest.class.getResource("/template/batch").toURI()).getAbsolutePath());
// }
//
// @Test
// public void stop() {
// doNothing().when(logisticsBatch2DbFromFileJob).batch2Db(anyList());
// logisticsBatch2DbFromFileJob.stop();
// verify(logisticsBatch2DbFromFileJob).batch2Db(anyList());
// assertTrue(logisticsBatch2DbFromFileJob.isStopJob);
// assertTrue(logisticsBatch2DbFromFileJob.getExecutor().isTerminated());
// }
//
// @Test
// public void run() {
// doNothing().when(logisticsBatch2DbFromFileJob).batch2Db(anyList());
//
// logisticsBatch2DbFromFileJob.isStopJob = true;
// logisticsBatch2DbFromFileJob.run();
//
// verify(logisticsBatch2DbFromFileJob, never()).batch2Db(anyList());
// }
//
// @Test
// public void run2() {
// doNothing().when(logisticsBatch2DbFromFileJob).batch2Db(anyList());
//
// logisticsBatch2DbFromFileJob.run();
//
// verify(logisticsBatch2DbFromFileJob).batch2Db(anyList());
// }
//
// @Test
// public void findFiles() {
// List<File> files = logisticsBatch2DbFromFileJob.findFiles();
// assertNotNull(files);
// assertEquals(2, files.size());
// }
//
// @Test
// public void findFiles2() throws IOException {
// String sThisTime = FastDateFormat.getInstance("yyyy-MM-dd-HH-mm").format(Calendar.getInstance());
// String exceptFileName = LogisticsBatch2DbFromFileJob.LOGSTIC_FILENAME_PREFIX + sThisTime;
// File file = new File(logisticsBatch2DbFromFileJob.getCsvDir(), exceptFileName);
// try {
// file.createNewFile();
// List<File> files = logisticsBatch2DbFromFileJob.findFiles();
// assertNotNull(files);
// assertEquals(2, files.size());
// } finally {
// file.delete();
// }
// }
//
// @Test
// public void batch2Db() {
// doReturn(mock(Connection.class)).when(logisticsBatch2DbFromFileJob).getConnection(anyString());
// logisticsBatch2DbFromFileJob.batch2Db(null);
// verify(pgLogisticsService, never()).batch2DbFromFile(any(File.class), any(Connection.class));
// }
//
// @Test
// public void batch2Db2() {
// doReturn(mock(Connection.class)).when(logisticsBatch2DbFromFileJob).getConnection(anyString());
// logisticsBatch2DbFromFileJob.batch2Db(logisticsBatch2DbFromFileJob.findFiles());
// verify(pgLogisticsService, times(2)).batch2DbFromFile(any(File.class), any(Connection.class));
// }
//
// @Test
// public void batch2Db3() {
// doReturn(mock(Connection.class)).when(logisticsBatch2DbFromFileJob).getConnection(anyString());
// logisticsBatch2DbFromFileJob.getExecutor().shutdown();
//
// logisticsBatch2DbFromFileJob.batch2Db(logisticsBatch2DbFromFileJob.findFiles());
// verify(pgLogisticsService, never()).batch2DbFromFile(any(File.class), any(Connection.class));
// }
//
// @Test
// public void getConnection() throws SQLException {
// mockDataSource(0);
// Connection connection = logisticsBatch2DbFromFileJob.getConnection("1");
// assertNull(connection);
// }
//
// @Test
// public void getConnection2() throws SQLException {
// mockDataSource(1);
// Connection connection = logisticsBatch2DbFromFileJob.getConnection("0");
// assertNotNull(connection);
// verify(mockedDataSources.get(0)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("1");
// assertNotNull(connection);
// verify(mockedDataSources.get(0), times(2)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("500");
// assertNotNull(connection);
// verify(mockedDataSources.get(0), times(3)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("999");
// assertNotNull(connection);
// verify(mockedDataSources.get(0), times(4)).getConnection();
// }
//
// @Test
// public void getConnection3() throws SQLException {
// mockDataSource(2);
// Connection connection = logisticsBatch2DbFromFileJob.getConnection("0");
// assertNotNull(connection);
// verify(mockedDataSources.get(0)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("1");
// assertNotNull(connection);
// verify(mockedDataSources.get(0), times(2)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("500");
// assertNotNull(connection);
// verify(mockedDataSources.get(1)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("999");
// assertNotNull(connection);
// verify(mockedDataSources.get(1), times(2)).getConnection();
// }
//
// @Test
// public void getConnection4() throws SQLException {
// mockDataSource(3);
// Connection connection = logisticsBatch2DbFromFileJob.getConnection("0");
// assertNotNull(connection);
// verify(mockedDataSources.get(0)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("1");
// assertNotNull(connection);
// verify(mockedDataSources.get(0), times(2)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("500");
// assertNotNull(connection);
// verify(mockedDataSources.get(1)).getConnection();
//
// connection = logisticsBatch2DbFromFileJob.getConnection("999");
// assertNotNull(connection);
// verify(mockedDataSources.get(2)).getConnection();
// }
//
// private void mockDataSource(int size) throws SQLException {
// when(batch2LogisticDataSources.size()).thenReturn(size);
// when(batch2LogisticDataSources.isEmpty()).thenReturn(size <= 0);
//
// mockedDataSources.clear();
// for (int i = 0; i < size; i++) {
// DataSource dataSource = mock(DataSource.class);
// when(dataSource.getConnection()).thenReturn(mock(Connection.class));
// mockedDataSources.add(dataSource);
// when(batch2LogisticDataSources.get(eq(i))).thenReturn(dataSource);
// }
// }
// }
