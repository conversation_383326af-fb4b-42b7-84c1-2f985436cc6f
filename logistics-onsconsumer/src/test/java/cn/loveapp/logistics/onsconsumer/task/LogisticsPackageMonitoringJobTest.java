// package cn.loveapp.logistics.onsconsumer.task;
//
// import cn.loveapp.logistics.onsconsumer.TestConfig;
// import cn.loveapp.logistics.onsconsumer.dto.LogisticsInfoDTO;
// import cn.loveapp.logistics.onsconsumer.dto.LogisticsStatus;
// import cn.loveapp.logistics.onsconsumer.entity.LogisticsErrorPackage;
// import cn.loveapp.logistics.onsconsumer.entity.LogisticsPackageListen;
// import cn.loveapp.logistics.onsconsumer.entity.TradeLogisticsRule;
// import cn.loveapp.logistics.onsconsumer.service.LogisticsService;
// import cn.loveapp.logistics.onsconsumer.service.TaobaoService;
// import com.google.common.collect.Collections2;
// import com.google.common.collect.Lists;
// import org.apache.commons.collections.CollectionUtils;
// import org.apache.commons.lang3.ArrayUtils;
// import org.apache.commons.lang3.StringUtils;
// import org.apache.commons.lang3.tuple.Pair;
// import org.junit.Before;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.boot.test.mock.mockito.MockBean;
// import org.springframework.boot.test.mock.mockito.SpyBean;
// import org.springframework.context.event.ContextClosedEvent;
// import org.springframework.data.domain.PageRequest;
// import org.springframework.data.domain.Pageable;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.test.context.junit4.SpringRunner;
//
// import java.time.LocalDateTime;
// import java.time.ZoneId;
// import java.time.temporal.ChronoUnit;
// import java.time.temporal.TemporalUnit;
// import java.util.*;
// import java.util.concurrent.TimeUnit;
// import java.util.stream.Collectors;
// import java.util.stream.IntStream;
//
// import static org.junit.Assert.*;
// import static org.mockito.BDDMockito.*;
//
// @RunWith(SpringRunner.class)
// @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
// classes = {LogisticsPackageMonitoringJob.class, TestConfig.class},
// properties = {"logistics.monitoring.pool-size=1"})
// @ActiveProfiles("test")
// public class LogisticsPackageMonitoringJobTest {
//
// private String sellerId = "111111";
// private int listId = Integer.parseInt(StringUtils.substring(sellerId, -2));
// private String corpId = sellerId;
// private String sellerNick = "222222";
// private String tid = "33333";
// private String outSid = "33333";
// private String companyName = "33333";
// private Date modified = Date.from(LocalDateTime.of(2019, 1, 24, 0, 0).atZone(ZoneId.systemDefault()).toInstant());
// private String action = "TRADE_SUCCESS";
// private LogisticsStatus status = LogisticsStatus.NOPACKAGE;
// private String desc = "干线到达 | 晟邦";
// private String remark = LogisticsPackageListen.REMARK_LISTEN;
//
// @MockBean
// private LogisticsService logisticsService;
//
// @MockBean
// private TaobaoService taobaoService;
//
// @SpyBean
// private LogisticsPackageMonitoringJob logisticsPackageMonitoringJob;
//
// @Value("${logistics.monitoring.pool-size}")
// private int threadPoolSize;
//
// @Before
// public void setUp() {
// logisticsPackageMonitoringJob.init();
// logisticsPackageMonitoringJob.setEnableJob(true);
// }
//
// @Test
// public void onApplicationEvent() {
// logisticsPackageMonitoringJob.onApplicationEvent(mock(ContextClosedEvent.class));
// assertFalse(logisticsPackageMonitoringJob.getEnableJob());
// assertTrue(logisticsPackageMonitoringJob.getExecutor().isTerminated());
// }
//
// @Test
// public void run() {
// doReturn(mock(LogisticsPackageMonitoringJob.PackageMonitoringRunner.class)).when(logisticsPackageMonitoringJob)
// .createRunnable(anyInt(), anyInt());
// logisticsPackageMonitoringJob.setEnableJob(false);
// logisticsPackageMonitoringJob.run();
// verify(logisticsPackageMonitoringJob, never()).createRunnable(anyInt(), anyInt());
// }
//
// @Test
// public void run2() {
// LogisticsPackageMonitoringJob.PackageMonitoringRunner packageMonitoringRunner =
// mock(LogisticsPackageMonitoringJob.PackageMonitoringRunner.class);
// doReturn(packageMonitoringRunner).when(logisticsPackageMonitoringJob).createRunnable(anyInt(), anyInt());
// logisticsPackageMonitoringJob.run();
// verify(logisticsPackageMonitoringJob, times(threadPoolSize)).createRunnable(anyInt(), anyInt());
// verify(packageMonitoringRunner, times(threadPoolSize)).run();
// assertEquals(0, logisticsPackageMonitoringJob.getExecutor().getActiveCount());
// }
//
// @Test
// public void runnerRun() {
// int start = 0;
// int end = 1;
// int pageSize = 100;
// LogisticsPackageMonitoringJob.PackageMonitoringRunner runner =
// createPackageMonitoringRunner(start, end, pageSize, true);
// doNothing().when(runner).errorPackageDetection(any(LogisticsPackageListen.class), anyInt());
// runner.run();
// verify(logisticsService, never()).findAllFromPackageListen(anyString(), anyInt(), any(Pageable.class));
// verify(runner, never()).errorPackageDetection(any(LogisticsPackageListen.class), anyInt());
// }
//
// @Test
// public void runnerRun2() {
// int start = 0;
// int end = 1;
// int pageSize = 100;
// LogisticsPackageMonitoringJob.PackageMonitoringRunner runner =
// createPackageMonitoringRunner(start, end, pageSize, false);
// doNothing().when(runner).errorPackageDetection(any(LogisticsPackageListen.class), anyInt());
// runner.run();
// verify(logisticsService).findAllFromPackageListen(anyString(), anyInt(), any(Pageable.class));
// verify(runner, never()).errorPackageDetection(any(LogisticsPackageListen.class), anyInt());
// }
//
// @Test
// public void runnerRun3() {
// int start = 0;
// int end = 2;
// int pageSize = 10;
// LogisticsPackageMonitoringJob.PackageMonitoringRunner runner =
// createPackageMonitoringRunner(start, end, pageSize, false);
// doNothing().when(runner).errorPackageDetection(any(LogisticsPackageListen.class), anyInt());
//
// List<LogisticsPackageListen> list =
// IntStream.range(0, pageSize).boxed().map(index -> new LogisticsPackageListen())
// .collect(Collectors.toList());
//
// when(logisticsService.findAllFromPackageListen(anyString(), anyInt(), eq(PageRequest.of(0, pageSize))))
// .thenReturn(list);
//
// when(logisticsService.findAllFromPackageListen(anyString(), anyInt(), eq(PageRequest.of(1, pageSize))))
// .thenReturn(null);
//
// runner.run();
//
// verify(logisticsService, times(end)).findAllFromPackageListen(eq(LogisticsPackageListen.REMARK_LISTEN), eq(0),
// argThat((Pageable page) -> page.getPageSize() == pageSize && page.getPageNumber() <= 1));
//
// verify(logisticsService, times(end)).findAllFromPackageListen(eq(LogisticsPackageListen.REMARK_LISTEN), eq(1),
// argThat((Pageable page) -> page.getPageSize() == pageSize && page.getPageNumber() <= 1));
//
// verify(runner, times(list.size())).errorPackageDetection(any(LogisticsPackageListen.class), eq(0));
//
// verify(runner, times(list.size())).errorPackageDetection(any(LogisticsPackageListen.class), eq(1));
// }
//
// @Test
// public void runnerErrorPackageDetection() {
// int start = 0;
// int end = 2;
// int pageSize = 10;
// LogisticsPackageMonitoringJob.PackageMonitoringRunner runner =
// createPackageMonitoringRunner(start, end, pageSize, false);
//
// LogisticsPackageListen logisticsPackageListen = createLogisticsPackageListen();
//
// runner.errorPackageDetection(logisticsPackageListen, listId);
//
// verify(logisticsService)
// .updatePackageListenRemark(eq(sellerNick), eq(tid), eq(outSid), eq(LogisticsPackageListen.REMARK_CLOSE),
// eq(listId));
//
// verify(logisticsService, never()).saveErrorPackage(any(LogisticsInfoDTO.class), anyString(), anyInt());
// }
//
// @Test
// public void runnerErrorPackageDetection1() {
// int start = 0;
// int end = 2;
// int pageSize = 10;
// LogisticsPackageMonitoringJob.PackageMonitoringRunner runner =
// createPackageMonitoringRunner(start, end, pageSize, false);
//
// LogisticsPackageListen logisticsPackageListen = createLogisticsPackageListen();
//
// when(taobaoService.getVipflag(eq(sellerNick))).thenReturn(true);
//
// when(logisticsService.getMinStatusTimeErrorRule(anyString(), any(LogisticsInfoDTO.class))).thenReturn(Pair.of(null,
// null));
//
// runner.errorPackageDetection(logisticsPackageListen, listId);
//
// verify(logisticsService, never())
// .updatePackageListenRemark(eq(sellerNick), eq(tid), eq(outSid), eq(LogisticsPackageListen.REMARK_CLOSE),
// eq(listId));
//
// verify(logisticsService, never()).saveErrorPackage(any(LogisticsInfoDTO.class), anyString(), anyInt());
// }
//
// @Test
// public void runnerErrorPackageDetection2() {
// int start = 0;
// int end = 2;
// int pageSize = 10;
// LogisticsPackageMonitoringJob.PackageMonitoringRunner runner =
// createPackageMonitoringRunner(start, end, pageSize, false);
//
// LogisticsPackageListen logisticsPackageListen = createLogisticsPackageListen();
//
// when(taobaoService.getVipflag(eq(sellerNick))).thenReturn(true);
//
// when(logisticsService.getMinStatusTimeErrorRule(anyString(), any(LogisticsInfoDTO.class))).thenReturn(Pair.of(
// TradeLogisticsRule.newDefault(), "province"));
//
// Date date = new Date();
// when(logisticsService.getLogisticsRuleEndTime(eq(modified), anyInt())).thenReturn(date);
//
// runner.errorPackageDetection(logisticsPackageListen, listId);
//
// verify(logisticsService, never()).saveErrorPackage(any(LogisticsInfoDTO.class), anyString(), anyInt());
//
// date = Date.from(date.toInstant().minus(1, ChronoUnit.DAYS));
// when(logisticsService.getLogisticsRuleEndTime(eq(modified), anyInt())).thenReturn(date);
//
// runner.errorPackageDetection(logisticsPackageListen, listId);
//
// verify(logisticsService, never())
// .updatePackageListenRemark(eq(sellerNick), eq(tid), eq(outSid), eq(LogisticsPackageListen.REMARK_CLOSE),
// eq(listId));
//
// verify(logisticsService).saveErrorPackage(eq(LogisticsInfoDTO.of(logisticsPackageListen)), anyString(),
// eq(LogisticsErrorPackage.TYPE_RULE));
// }
//
// private LogisticsPackageMonitoringJob.PackageMonitoringRunner createPackageMonitoringRunner(int start, int end,
// int pageSize, boolean stop) {
// return spy(new LogisticsPackageMonitoringJob.PackageMonitoringRunner(logisticsPackageMonitoringJob.getTimer(),
// logisticsService, taobaoService, start, end, pageSize, () -> stop));
// }
//
// private LogisticsPackageListen createLogisticsPackageListen() {
// return new LogisticsPackageListen(corpId, sellerNick, sellerId, tid, outSid, companyName, modified, action,
// status.value(), desc, "province", remark, new Date(), listId);
// }
// }
