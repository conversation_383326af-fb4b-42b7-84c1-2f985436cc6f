// package cn.loveapp.logistics.onsconsumer.task;
//
// import cn.loveapp.common.utils.NetworkUtil;
// import org.junit.Before;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.boot.test.mock.mockito.MockBean;
// import org.springframework.boot.test.mock.mockito.SpyBean;
// import org.springframework.data.redis.core.HashOperations;
// import org.springframework.data.redis.core.StringRedisTemplate;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.test.context.junit4.SpringRunner;
//
// import static org.junit.Assert.assertEquals;
// import static org.mockito.BDDMockito.*;
//
// @RunWith(SpringRunner.class)
// @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = {QpsJob.class}, properties =
// {"logistics.batch.dir=/home"})
// @ActiveProfiles("test")
// public class QpsJobTest {
// @MockBean
// private StringRedisTemplate redisHelper;
//
// @MockBean
// private HashOperations hashOperations;
//
// @SpyBean
// private QpsJob qpsJob;
//
// @Before
// public void setUp() {
// qpsJob.QPS.reset();
// when(redisHelper.opsForHash()).thenReturn(hashOperations);
// }
//
// @Test
// public void run() {
// qpsJob.run();
// verify(hashOperations)
// .put(eq("apm"), eq("wuliu_" + NetworkUtil.localIP()), argThat((String argument) ->
// argument.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\|0")));
// }
//
// @Test
// public void increment() {
// qpsJob.increment();
// assertEquals(1, qpsJob.QPS.intValue());
// }
// }
