<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>logistics-services-group</artifactId>
        <groupId>cn.loveapp.logistics</groupId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>爱用宝基础服务-物流服务-前端服务</name>
    <description>爱用宝基础服务-物流服务-前端服务</description>
    <artifactId>logistics-service</artifactId>

    <properties>

    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.loveapp.logistics</groupId>
            <artifactId>logistics-common</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-user-session-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.loveapp.shops</groupId>
            <artifactId>shops-api</artifactId>
        </dependency>

        <dependency>
            <groupId>net.spy</groupId>
            <artifactId>spymemcached</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
