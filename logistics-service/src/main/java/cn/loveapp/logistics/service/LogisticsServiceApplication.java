package cn.loveapp.logistics.service;

import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;

import cn.loveapp.common.utils.LoggerHelper;

/**
 * 物流对外服务
 *
 * <AUTHOR>
 * @date 2018/10/19
 */
@EnableCaching
@EnableFeignClients(basePackages = {"cn.loveapp.uac", "cn.loveapp.shops","cn.loveapp.logistics.common.service.external"})
@SpringBootApplication(exclude = {MybatisAutoConfiguration.class, DataSourceHealthContributorAutoConfiguration.class},
    scanBasePackages = {"cn.loveapp.logistics"})
public class LogisticsServiceApplication {
    LoggerHelper logger = LoggerHelper.getLogger(LogisticsServiceApplication.class);

    /**
     * Description 程序主入口
     *
     * <AUTHOR> Jiang
     * @date 2018-09-21 23:37
     */
    public static void main(String[] args) {
        SpringApplication.run(LogisticsServiceApplication.class, args);
    }
}
