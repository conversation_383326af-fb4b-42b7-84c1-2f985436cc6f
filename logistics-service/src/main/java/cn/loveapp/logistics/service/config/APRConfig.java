package cn.loveapp.logistics.service.config;

import org.apache.catalina.core.AprLifecycleListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * tomcat APR支持
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-12-07 下午3:57
 */

@Configuration
@ConditionalOnProperty(name = "logistics.apr.enable", havingValue = "true", matchIfMissing = true)
public class APRConfig {

    @Bean
    public ConfigurableServletWebServerFactory configurableServletWebServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.setProtocol("org.apache.coyote.http11.Http11AprProtocol");
        factory.addContextLifecycleListeners(new AprLifecycleListener());
        return factory;
    }

}
