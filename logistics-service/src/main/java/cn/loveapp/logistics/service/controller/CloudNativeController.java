package cn.loveapp.logistics.service.controller;

import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.controller.BaseCloudNativeController;
import cn.loveapp.logistics.service.utils.MemcacheUtil;

/**
 * 探活Controller
 *
 * <AUTHOR>
 * @date 2019-04-16
 */
@Controller
@RequestMapping("/")
public class CloudNativeController extends BaseCloudNativeController {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CloudNativeController.class);
    @Autowired
    private MemcacheUtil memcacheUtil;

    public CloudNativeController(ObjectProvider<List<DataSource>> provider) {
        super(provider);
    }

    @Override
    protected HttpStatus checkReadNess() {
        HttpStatus status = super.checkReadNess();
        if (status == HttpStatus.OK) {
            try {
                memcacheUtil.getCache(System.currentTimeMillis() + "_readness");
            } catch (Exception e) {
                LOGGER.logError("memcached readness失败: " + e.getMessage(), e);
                status = HttpStatus.INTERNAL_SERVER_ERROR;
            }
        }
        return status;
    }
}
