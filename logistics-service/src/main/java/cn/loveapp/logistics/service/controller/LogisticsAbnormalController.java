package cn.loveapp.logistics.service.controller;

import cn.loveapp.common.annotation.RequestParamConvert;
import cn.loveapp.common.constant.HttpMethodsConstants;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.LogisticsAbnormalHandleService;
import cn.loveapp.logistics.service.annotation.UserAuth;
import cn.loveapp.logistics.service.dto.AbnormalProcessResult;
import cn.loveapp.logistics.service.request.LogisticsAbnormalBatchProcessRequest;
import cn.loveapp.logistics.service.request.LogisticsAbnormalListGetRequest;
import cn.loveapp.logistics.service.request.LogisticsAbnormalStatisticsRequest;
import cn.loveapp.logistics.service.response.LogisticsAbnormalBatchProcessResponse;
import cn.loveapp.logistics.service.response.LogisticsAbnormalListGetResponse;
import cn.loveapp.logistics.service.response.LogisticsAbnormalStatisticsResponse;
import cn.loveapp.logistics.service.service.LogisticsAbnormalService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;


/**
 * 物流异常相关api
 *
 * <AUTHOR>
 * @Date 2023/6/26 15:34
 */
@RestController
@RequestMapping("logistics")
public class LogisticsAbnormalController {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsAbnormalController.class);

    @Autowired
    private LogisticsAbnormalHandleService logisticsAbnormalHandleService;

    @Autowired
    private LogisticsAbnormalService logisticsAbnormalService;

    @Autowired
    private LogisticsConfig logisticsConfig;

    /**
     * 异常物流监控统计数据
     *
     * @param request
     * @param userInfoDTO
     * @return
     */
    @ApiOperation(value = "异常物流监控统计数据", httpMethod = HttpMethodsConstants.POST)
    @PostMapping("/abnormal.statistics.get")
    @RequestParamConvert(underscore = false)
    @CheckUserSession
    @UserAuth
    public CommonApiResponse<LogisticsAbnormalStatisticsResponse> abnormalStatisticsGet(LogisticsAbnormalStatisticsRequest request, UserInfoDTO userInfoDTO) {

        if (StringUtils.isEmpty(request.getFields())) {
            LOGGER.logError("轨迹查询失败：参数异常");
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError, null);
        }

        AbnormalQueryDTO abnormalQueryDTO = new AbnormalQueryDTO();
        BeanUtils.copyProperties(request, abnormalQueryDTO);
        abnormalQueryDTO.setAbnormalTypes(request.getFields());

        LogisticsAbnormalStatisticsResponse response = new LogisticsAbnormalStatisticsResponse();
        try {
            LogisticsAbnormalCountDTO abnormalCount = logisticsAbnormalHandleService.abnormalStatisticsGet(abnormalQueryDTO, userInfoDTO);
            response.setAbnormalCount(abnormalCount);
            return CommonApiResponse.success(response);
        } catch (LogisticsHandlesException e) {
            response.setErrorMsg(e.getMessage());
            LOGGER.logError("异常物流监控统计数据：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.Failed, response);
        } catch (Exception e) {
            response.setErrorMsg(e.getMessage());
            LOGGER.logError("异常物流监控统计数据：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }
    }

    /**
     * 异常物流单列表
     *
     * @param request
     * @param userInfoDTO
     * @return
     */
    @ApiOperation(value = "异常物流单列表", httpMethod = HttpMethodsConstants.POST)
    @PostMapping("/abnormal.list.get")
    @RequestParamConvert(underscore = false)
    @CheckUserSession
    @UserAuth
    public CommonApiResponse<LogisticsAbnormalListGetResponse> abnormalListGet(LogisticsAbnormalListGetRequest request, UserInfoDTO userInfoDTO) {

        if (logisticsConfig.isReturnEmpty()) {
            LOGGER.logInfo(userInfoDTO.getNick(), "", "降级直接返回空");
            return CommonApiResponse.failed(CommonApiStatus.Success, null);
        }

        if (Objects.isNull(request) || !request.checkParams()) {
            LOGGER.logError("物流单失败：参数异常");
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError, null);
        }

        LogisticsAbnormalListGetResponse response = new LogisticsAbnormalListGetResponse();
        try {

            response = logisticsAbnormalService.abnormalListGet(request, userInfoDTO);
            return CommonApiResponse.success(response);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError("异常物流单列表获取失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.Failed.code(), e.getMessage(), response);
        } catch (Exception e) {
            LOGGER.logError("异常物流单列表获取失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }
    }

    /**
     * 异常物流单处理
     *
     * @param request
     * @param userInfoDTO
     * @return
     */
    @ApiOperation(value = "异常物流单处理", httpMethod = HttpMethodsConstants.POST)
    @PostMapping("/abnormal.batch.process")
    @RequestParamConvert(underscore = false)
    @CheckUserSession
    @UserAuth
    public CommonApiResponse<LogisticsAbnormalBatchProcessResponse> abnormalBatchProcess(LogisticsAbnormalBatchProcessRequest request, UserInfoDTO userInfoDTO) {

        LogisticsAbnormalBatchProcessResponse response = new LogisticsAbnormalBatchProcessResponse();
        try {
            List<AbnormalProcessResult> resultList = logisticsAbnormalService.abnormalBatchProcess(request.getUpdatePackList());
            response.setResultList(resultList);
            return CommonApiResponse.success(response);
        } catch (Exception e) {
            LOGGER.logError("异常物流单处理失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }
    }

    /**
     * 异常物流列表导出(外部)
     *
     * @param request
     * @param userInfoDTO
     * @return
     */
    @ApiOperation(value = "异常物流单导出", httpMethod = HttpMethodsConstants.POST)
    @PostMapping("/abnormal.list.export")
    @RequestParamConvert(underscore = false)
    @CheckUserSession
    @UserAuth
    public CommonApiResponse<LogisticsAbnormalListGetResponse> abnormalListExport(LogisticsAbnormalListGetRequest request, UserInfoDTO userInfoDTO) {
        if (logisticsConfig.isReturnEmpty()) {
            LOGGER.logInfo(userInfoDTO.getNick(), "", "降级直接返回空");
            return CommonApiResponse.failed(CommonApiStatus.Success, null);
        }

        if (Objects.isNull(request) || !request.checkParams() || Objects.isNull(request.getLastSearchSortValues())) {
            LOGGER.logError("物流单失败：参数异常");
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError, null);
        }

        LogisticsAbnormalListGetResponse response = new LogisticsAbnormalListGetResponse();
        try {
            response = logisticsAbnormalService.abnormalListGet(request, userInfoDTO);
            return CommonApiResponse.success(response);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError("异常物流单列表获取失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.Failed.code(), e.getMessage(), response);
        } catch (Exception e) {
            LOGGER.logError("异常物流单列表获取失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }
    }

}
