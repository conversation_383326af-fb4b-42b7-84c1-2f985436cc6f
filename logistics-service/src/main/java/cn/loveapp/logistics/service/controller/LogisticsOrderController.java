package cn.loveapp.logistics.service.controller;

import cn.loveapp.common.annotation.RequestParamConvert;
import cn.loveapp.common.constant.HttpMethodsConstants;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.dto.*;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.service.annotation.UserAuth;
import cn.loveapp.logistics.service.request.*;
import cn.loveapp.logistics.service.response.LogisticsAbnormalListGetResponse;
import cn.loveapp.logistics.service.service.LogisticsAbnormalService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/***
 * <AUTHOR>
 * @Description 物流订阅控制器
 * @Date 17:43 2023/11/29
 **/
@RestController
@RequestMapping("logistics")
public class LogisticsOrderController {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderController.class);

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private LogisticsAbnormalService logisticsAbnormalService;


    /**
     * 物流订阅列表
     *
     * @param request
     * @param userInfoDTO
     * @return
     */
    @ApiOperation(value = "物流订阅列表", httpMethod = HttpMethodsConstants.POST)
    @PostMapping("/order.list.get")
    @RequestParamConvert(underscore = false)
    @CheckUserSession
    @UserAuth
    public CommonApiResponse<LogisticsAbnormalListGetResponse> orderListGet(LogisticsOrderListGetRequest request, UserInfoDTO userInfoDTO) {

        if (logisticsConfig.isReturnEmpty()) {
            LOGGER.logInfo(userInfoDTO.getNick(), "", "降级直接返回空");
            return CommonApiResponse.failed(CommonApiStatus.Success, null);
        }

        if (Objects.isNull(request) || !request.checkParams()) {
            LOGGER.logError("物流单失败：参数异常");
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError, null);
        }

        LogisticsAbnormalListGetResponse response = new LogisticsAbnormalListGetResponse();
        try {

            // 排除异常状态处理完成
            request.setIsExcludeProcessed(false);
            // 去除查询异常的逻辑
            request.setOnlySearchAbnormal(false);
            //去重不需要依据status字段
            request.setDistinctWithStatusField(false);
            response = logisticsAbnormalService.abnormalListGet(request, userInfoDTO);
            return CommonApiResponse.success(response);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError("物流订阅列表获取失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.Failed.code(), e.getMessage(), response);
        } catch (Exception e) {
            LOGGER.logError("物流订阅列表获取失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }
    }
}
