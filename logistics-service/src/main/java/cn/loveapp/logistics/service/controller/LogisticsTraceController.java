package cn.loveapp.logistics.service.controller;

import java.util.*;

import jakarta.validation.Valid;

import cn.loveapp.logistics.api.dto.OrderInfoDTO;
import cn.loveapp.logistics.api.dto.OrderSkuInfoDTO;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;

import cn.loveapp.common.annotation.RequestParamConvert;
import cn.loveapp.common.constant.HttpMethodsConstants;
import cn.loveapp.common.user.session.annotation.CheckUserSession;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.dto.LogisticsDetailDTO;
import cn.loveapp.logistics.common.dto.PrepareConsignDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.LogisticsTraceHandleService;
import cn.loveapp.logistics.service.annotation.UserAuth;
import cn.loveapp.logistics.service.dto.LogisticsInfoQuery;
import cn.loveapp.logistics.service.dto.UpdateLogisticsParamList;
import cn.loveapp.logistics.service.dto.UpdateLogisticsParamsDTO;
import cn.loveapp.logistics.service.exception.DatabaseException;
import cn.loveapp.logistics.service.exception.ExceptionEnum;
import cn.loveapp.logistics.service.request.LogisticsTraceApiSearchRequest;
import cn.loveapp.logistics.service.request.LogisticsTraceSubscribeRequest;
import cn.loveapp.logistics.service.response.LogisticsTraceApiSearchResponse;
import cn.loveapp.logistics.service.service.LogisticsTraceService;
import io.swagger.annotations.ApiOperation;

/**
 *
 * 物流API
 *
 * <AUTHOR> Shuaifei
 * @email <EMAIL>
 * @create 2018-11-05 下午12:13
 */
@RestController
@RequestMapping("logistics")
public class LogisticsTraceController {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsTraceController.class);

    @Autowired
    LogisticsTraceService logisticsTraceService;

    @Autowired
    private LogisticsTraceHandleService logisticsTraceHandleService;

    @Autowired
    private LogisticsConfig logisticsConfig;

    /**
     * 获取订单物流详细信息，将对应订单物流信息分包裹后返回（version为空视为向下兼容的老版调用） tid和invoiceNo分割后，根据下标对应组合
     *
     * @param oid
     *            要查询的订单号,不可为空 (已废弃, 使用tid代替),多个使用","拼接 与invoiceNo下标一一对应，数量保持一致
     * @param tid
     *            要查询的订单号,不可为空 单个订单查询 不支持批量
     * @param invoiceNo
     *            要查询的物流单号,不可为空，多个使用","拼接 与tid下标一一对应，数量保持一致
     * @param sort
     *            排序字段，可为空,默认为desc
     * @param version
     *            API版本信息，可为空，为空表示向下兼容
     * @param callback
     * @param userInfoDTO
     * @return 向下兼容直接返回包裹信息或错误信息，向上兼容时返回结果对象
     */
    @RequestMapping("/logisticsInfo")
    @CheckUserSession
    @UserAuth
    public Object getLogisticInfo(@Deprecated @RequestParam(name = "oid", required = false) String oid,
        @RequestParam(name = "tid", required = false) String tid,
        @RequestParam(name = "invoiceNo", required = false) String invoiceNo,
        @RequestParam(value = "sort", defaultValue = "desc") String sort,
        @RequestParam(value = "version", defaultValue = "") String version,
        @RequestParam(value = "callback", defaultValue = "", required = false) String callback,
        UserInfoDTO userInfoDTO) {

        // 是否向下兼容
        boolean oldFlag = StringUtils.isEmpty(version);

        if (logisticsConfig.isReturnEmpty()) {
            LOGGER.logInfo(userInfoDTO.getNick(), tid, "降级直接返回空");
            List<LogisticsDetailDTO> emptyObject = new ArrayList<>();
            if (StringUtils.isEmpty(callback)) {
                return oldFlag ? emptyObject : CommonApiResponse.success(emptyObject);
            } else {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(callback + "(");
                stringBuilder.append(JSON.toJSONString(emptyObject));
                stringBuilder.append(")");
                return oldFlag ? stringBuilder.toString() : CommonApiResponse.success(stringBuilder.toString());
            }
        }

        String[] oids = StringUtils.split(oid, ",");

        List<String> oidList = Lists.newArrayList(oids == null ? new String[] {} : oids);
        List<String> invoiceNoList = new ArrayList<>();
        if (StringUtils.isNotBlank(invoiceNo)) {
            String[] invoiceNos = StringUtils.split(invoiceNo, ",");
            invoiceNoList = Lists.newArrayList(invoiceNos);
        }

        LogisticsInfoQuery query = new LogisticsInfoQuery(oidList, invoiceNoList, tid, sort);

        List<LogisticsDetailDTO> logisticsDetailDTO = logisticsTraceService.getLogisticInfo(query, userInfoDTO);

        if (CollectionUtils.isEmpty(logisticsDetailDTO)) {
            LOGGER.logWarn(userInfoDTO.getNick(), tid, "未查询到物流信息");
        }
        if (StringUtils.isEmpty(callback)) {
            return oldFlag ? logisticsDetailDTO : CommonApiResponse.success(logisticsDetailDTO);
        } else {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(callback + "(");
            stringBuilder.append(JSON.toJSONString(logisticsDetailDTO));
            stringBuilder.append(")");
            return oldFlag ? stringBuilder.toString() : CommonApiResponse.success(stringBuilder.toString());
        }
    }

    /**
     * 更新物流信息（version为空视为向下兼容的老版调用）
     *
     * @param logisticsUpdateParamsList
     *            需要更新的物流信息,不可为空
     * @param userInfoDTO
     * @return 向下兼容直接返回空或错误信息，向上兼容时返回结果对象
     */
    @RequestMapping("/updateLogisticsPgsql")
    @RequestParamConvert(underscore = false)
    @CheckUserSession
    @UserAuth
    public Object updateLogisticsInfo(UpdateLogisticsParamList logisticsUpdateParamsList, UserInfoDTO userInfoDTO) {

        List<UpdateLogisticsParamsDTO> logisticsDatas = logisticsUpdateParamsList.getLogisticsData();
        if (CollectionUtils.isEmpty(logisticsDatas)) {
            LOGGER.logError(userInfoDTO.getNick(), "-", "缺少必要参数");
            return "缺少必要参数";
        }

        // 是否向下兼容
        boolean oldFlag = StringUtils.isEmpty(logisticsDatas.get(0).getVersion());
        String updateResult = null;
        try {
            updateResult = logisticsTraceService.updateLogisticsInfo(logisticsDatas, userInfoDTO);
        } catch (DatabaseException e) {
            LOGGER.logError(userInfoDTO.getNick(), "-", "更新订单物流信息失败: " + e.getMessage(), e);
            return getExceptionResult(oldFlag, e.getExceptionEnum());
        }
        Map<String, String> result = new HashMap<>();
        result.put("result", updateResult);

        return oldFlag ? JSON.toJSONString(result) : CommonApiResponse.success(JSON.toJSONString(result));
    }

    /**
     * 物流轨迹订阅
     *
     * @param logisticsTraceSubscribeRequest
     * @param userInfoDTO
     * @return
     */
    @ApiOperation(value = "物流轨迹订阅", httpMethod = HttpMethodsConstants.POST)
    @PostMapping("/logistics.trace.subscribe")
    @RequestParamConvert(underscore = false)
    @CheckUserSession
    @UserAuth
    public CommonApiResponse<String> logisticsTraceSubscribe(LogisticsTraceSubscribeRequest logisticsTraceSubscribeRequest, UserInfoDTO userInfoDTO) {

        String requestLogisticsStoreId = logisticsTraceSubscribeRequest.getLogisticsStoreId();
        String logisticsStoreId = LogisticsUtil.getDefaultLogisticsStoreId(requestLogisticsStoreId);
        logisticsTraceSubscribeRequest.setLogisticsStoreId(logisticsStoreId);

        LogisticsOrderSubscribeDTO logisticsOrderSubscribeDTO = new LogisticsOrderSubscribeDTO();
        BeanUtils.copyProperties(logisticsTraceSubscribeRequest, logisticsOrderSubscribeDTO);
        logisticsOrderSubscribeDTO.setAppName(userInfoDTO.getAppName());
        logisticsOrderSubscribeDTO.setSellerId(userInfoDTO.getSellerId());
        logisticsOrderSubscribeDTO.setStoreId(userInfoDTO.getStoreId());
        logisticsOrderSubscribeDTO.setSellerNick(userInfoDTO.getNick());
        logisticsOrderSubscribeDTO.setBuyerNick(logisticsTraceSubscribeRequest.getBuyerNick());
        logisticsOrderSubscribeDTO.setBuyerOpenUid(logisticsTraceSubscribeRequest.getBuyerOpenUid());
        logisticsOrderSubscribeDTO.setOriginalLogisticsStoreId(requestLogisticsStoreId);
        logisticsOrderSubscribeDTO.setPhone(logisticsTraceSubscribeRequest.getPhone());
        if (logisticsTraceSubscribeRequest.getIsSubscribeNeedDeductionQuota() != null) {
            logisticsOrderSubscribeDTO.setSubscribeNeedDeductionQuota(logisticsTraceSubscribeRequest.getIsSubscribeNeedDeductionQuota());
        }

        List<LogisticsTraceSubscribeRequest.OrderInfo> orderInfoList = logisticsTraceSubscribeRequest.getOrderInfoList();
        List<OrderInfoDTO> orderInfoChangeList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orderInfoList)) {
            for (LogisticsTraceSubscribeRequest.OrderInfo orderInfo : orderInfoList) {
                OrderInfoDTO orderInfoDTO = new OrderInfoDTO();
                orderInfoDTO.setTid(orderInfo.getTid());
                orderInfoDTO.setSellerFlag(orderInfo.getSellerFlag());
                orderInfoDTO.setOrderAyCustomFlag(orderInfo.getOrderAyCustomFlag());
                orderInfoDTO.setSellerMemo(orderInfo.getSellerMemo());
                orderInfoDTO.setBuyerMessage(orderInfo.getBuyerMessage());
                orderInfoDTO.setIsRefund(orderInfo.getIsRefund());
                orderInfoDTO.setRefundCreatedTime(orderInfo.getRefundCreatedTime());
                List<LogisticsTraceSubscribeRequest.SkuInfo> skuInfoList = orderInfo.getSkuInfoList();
                if (CollectionUtils.isNotEmpty(skuInfoList)) {
                    List<OrderSkuInfoDTO> orderSkuInfoList = Lists.newArrayList();
                    for (LogisticsTraceSubscribeRequest.SkuInfo skuInfo : skuInfoList) {
                        OrderSkuInfoDTO orderSkuInfoDTO = new OrderSkuInfoDTO();
                        orderSkuInfoDTO.setSkuId(skuInfo.getSkuId());
                        orderSkuInfoDTO.setPicUrl(skuInfo.getPicUrl());
                        orderSkuInfoDTO.setOuterSkuId(skuInfo.getOuterSkuId());
                        orderSkuInfoDTO.setSkuName(skuInfo.getSkuName());
                        orderSkuInfoDTO.setNum(skuInfo.getNum());
                        orderSkuInfoList.add(orderSkuInfoDTO);
                    }

                    orderInfoDTO.setSkuInfoList(orderSkuInfoList);
                }

                orderInfoChangeList.add(orderInfoDTO);
            }
        }

        try {
            boolean success =
                logisticsTraceHandleService.subscribeLogisticsTrace(logisticsOrderSubscribeDTO, orderInfoChangeList);
            if (success) {
                return CommonApiResponse.success();
            } else {
                return CommonApiResponse.failed(CommonApiStatus.Failed, "订阅失败");
            }
        } catch (LogisticsHandlesException e) {
            LOGGER.logError("订阅失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.Failed, "订阅失败：" + e.getMessage());
        } catch (Exception e) {
            LOGGER.logError("订阅失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, "订阅失败：" + e.getMessage());
        }
    }

    /**
     * 物流轨迹查询（api）
     *
     * @param request
     * @param userInfoDTO
     * @return
     */
    @ApiOperation(value = "物流轨迹查询（api）", httpMethod = HttpMethodsConstants.POST)
    @PostMapping("/logistics.trace.api.search")
    @RequestParamConvert(underscore = false)
    @CheckUserSession
    @UserAuth
    public CommonApiResponse<LogisticsTraceApiSearchResponse> logisticsTraceSearchFromApi(@Valid LogisticsTraceApiSearchRequest request, UserInfoDTO userInfoDTO) {

        if (CollectionUtils.isEmpty(request.getApiSearchList())) {
            LOGGER.logError("轨迹查询失败：参数异常");
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError, null);
        }


        LogisticsTraceApiSearchResponse response = new LogisticsTraceApiSearchResponse();
        try {
            PrepareConsignDTO prepareConsignDTO = new PrepareConsignDTO(request.isForceSubscribe(), request.isReportedUsedWhenSubscribe(), request.getSourceApp());
            List<LogisticsDetailDTO> logisticsDetails = logisticsTraceHandleService.searchLogisticsTraceFromApi(request.getApiSearchList(), userInfoDTO, prepareConsignDTO);
            response.setLogisticsDetails(logisticsDetails);
            return CommonApiResponse.success(response);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError("轨迹查询失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.Failed.code(), e.getMessage(), response);
        } catch (Exception e) {
            LOGGER.logError("轨迹查询失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }
    }


    /**
     *
     * 兼容返回异常结果
     *
     * @param oldFlag
     *            是否兼容老版本，true表示兼容，默认为true
     * @param exceptionEnum
     *            异常枚举，列举各种异常信息
     * @return 兼容对象
     */
    private Object getExceptionResult(boolean oldFlag, ExceptionEnum exceptionEnum) {
        if (oldFlag) {
            return exceptionEnum.message;
        }
        return CommonApiResponse.of(exceptionEnum.code, exceptionEnum.message);
    }
}
