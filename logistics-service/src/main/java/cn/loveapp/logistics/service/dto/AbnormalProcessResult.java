package cn.loveapp.logistics.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 物流更新结果
 *
 * <AUTHOR>
 * @Date 2023/7/5 11:23
 */
@ApiModel
@Data
public class AbnormalProcessResult {

    @ApiModelProperty(value = "运单号")
    private String outSid;

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    @ApiModelProperty(value = "更新异常信息")
    private String errorMsg;

}
