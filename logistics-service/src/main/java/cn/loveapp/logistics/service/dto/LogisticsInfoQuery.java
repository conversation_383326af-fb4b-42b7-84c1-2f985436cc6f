package cn.loveapp.logistics.service.dto;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/15$ 15:21$ 物流信息查询实体
 */
@Data
public class LogisticsInfoQuery {
    /**
     * 订单号查询
     */
    private String tid;
    /**
     * oid查询集合
     */
    @Deprecated
    private List<String> oidList;
    /**
     * 物流单号查询集合
     */
    private List<String> invoiceNoList;
    /**
     * 排序
     */
    private String sort;

    public LogisticsInfoQuery(List<String> oidList, List<String> invoiceNoList, String tid, String sort) {
        this.oidList = oidList;
        this.invoiceNoList = invoiceNoList;
        this.tid = tid;
        this.sort = sort;
    }

    public LogisticsInfoQuery() {

    }
}
