package cn.loveapp.logistics.service.dto;

import cn.loveapp.logistics.service.exception.ExceptionEnum;
import lombok.Data;

/**
 *
 * 请求响应对象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-10-31 下午2:47
 */

@Data
public class ResultDTO<T> {

    /**
     * 返回码 0: 成功 其他: 失败
     */
    private Integer code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 消息体, 失败时没有消息体
     */
    private T body;

    public ResultDTO() {

    }

    public ResultDTO(Integer code, String message, T body) {
        this.code = code;
        this.message = message;
        this.body = body;
    }

    public <T> ResultDTO<T> success(T body) {
        return new ResultDTO<T>(200, "成功", body);
    }

    public <T> ResultDTO<T> success() {
        return new ResultDTO<T>(200, "成功", null);
    }

    public ResultDTO error(ExceptionEnum exceptionEnum) {
        return new ResultDTO<T>(exceptionEnum.code, exceptionEnum.message, null);
    }

    public static <T> ResultDTO<T> error(ExceptionEnum exceptionEnum, T body) {
        return new ResultDTO<>(exceptionEnum.code, exceptionEnum.message, body);
    }

}
