package cn.loveapp.logistics.service.dto;

import java.util.List;

import cn.loveapp.logistics.common.dto.TracesDTO;
import lombok.Data;

/**
 * 更新物流对象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-11-02 上午9:56
 */
@Data
public class UpdateLogisticsParamsDTO {

    /**
     * 用户id
     */
    private String sellerId;

    /**
     * 用户nick
     */
    private String sellerNick;

    /**
     * 平台id
     */
    private String storeId;

    /**
     * 应用id
     */
    private String appName;

    /**
     * 物流公司名称
     */
    private String companyName;

    /**
     * 运单号
     */
    private String outSid;

    /**
     * API版本号，默认为空
     */
    private String version;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 要更新的订单号
     */
    private List<String> tid;

    /**
     * 物流信息列表
     */
    private TracesDTO traceList;

}
