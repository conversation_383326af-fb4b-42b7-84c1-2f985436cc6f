package cn.loveapp.logistics.service.exception;

/**
 * 返回前端结果枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-10-31 下午2:45
 */
public enum ExceptionEnum {

    // 订单id为空
    TID_IS_NULL(400, "订单id为空"),

    // 订单数量和物流数量不一致
    DIFF_TID_INVOICENO_LENGTH(400, "订单数量和物流数量不一致"),

    // 登陆超时，session失效
    SESSION_INVALIDATION(400, "登陆超时，session失效"),

    // 更新出错,缺少参数oid
    UPDATE_OID_IS_LOST(400, "更新出错,缺少参数oid"),

    // session解析异常
    SESSION_PARSE_ERROR(400, "session解析异常"),

    // 未查到要更新的订单
    SQL_COUNT_IS_ZERO(400, "未查到要更新的订单"),

    // 没有要更新的数据
    UPDATE_DATA_IS_EMPTY(400, "没有要更新的数据"),

    UPDATE_DATA_USER_ERROR(400, "更新用户异常"),

    // 更新出错,缺少参数oid
    UPDATE_DATA_DATE_ERROR(400, "更新出错,时间格式不正确"),

    // sessionID异常
    SESSIONID_ERROR(403, "sessionID异常"),

    // 缓存失效
    CACHE_INVALID(403, "缓存失效"),

    // 数据库插入错误
    SQL_INSERT_ERROR(500, "更新失败"),

    ;

    public Integer code;

    public String message;

    ExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

}
