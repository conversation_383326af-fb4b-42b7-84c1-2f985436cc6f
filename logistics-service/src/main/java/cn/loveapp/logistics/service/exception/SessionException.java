package cn.loveapp.logistics.service.exception;

/**
 * session信息获取异常
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-11-08 下午5:42
 */
public class SessionException extends Exception {

    private ExceptionEnum exceptionEnum;

    public SessionException() {
        super();
    }

    public SessionException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum.message);
        this.exceptionEnum = exceptionEnum;
    }

    public ExceptionEnum getExceptionEnum() {
        return exceptionEnum;
    }
}
