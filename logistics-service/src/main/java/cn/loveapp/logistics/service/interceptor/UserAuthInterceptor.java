package cn.loveapp.logistics.service.interceptor;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.service.annotation.UserAuth;
import cn.loveapp.logistics.service.exception.ShopsAuthException;
import cn.loveapp.logistics.service.service.UserAuthService;
import com.alibaba.fastjson2.JSON;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 用户鉴权拦截器
 *
 * <AUTHOR>
 * @date 2020-02-08 16:07:13
 */
public class UserAuthInterceptor implements HandlerInterceptor, HandlerMethodArgumentResolver {
    public static final String COMMON_ATTRIBUTE_TARGETUSERINFO = "COMMON_ATTRIBUTE_TARGETUSERINFO";

    @Autowired
    private UserAuthService userAuthService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        MDC.remove("sellerNick");
        if (request.getAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO) == null) {
            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod)handler;
                if (!handlerMethod.hasMethodAnnotation(UserAuth.class)) {
                    return true;
                }
                try {
                    UserInfoDTO userInfoDTO = userAuthService.auth(request);
                    if (userInfoDTO.getNick() != null) {
                        MDC.put("sellerNick", userInfoDTO.getNick());
                    }
                    request.setAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO, userInfoDTO);
                } catch (ShopsAuthException e) {
                    // 多店鉴权失败
                    CommonApiResponse errorResponse = CommonApiResponse.of(403, e.getMessage());
                    response.setContentType("application/json; charset=utf-8");
                    response.getWriter().append(JSON.toJSONString(errorResponse)).flush();
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.getParameterType().isAssignableFrom(UserInfoDTO.class);
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer,
        NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        return (UserInfoDTO)nativeWebRequest.getAttribute(COMMON_ATTRIBUTE_TARGETUSERINFO, 0);
    }
}
