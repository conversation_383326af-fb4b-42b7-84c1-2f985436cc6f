package cn.loveapp.logistics.service.request;

import cn.loveapp.logistics.service.dto.AbnormalProcessInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 物流单处理request
 *
 * <AUTHOR>
 * @Date 2023/7/5 11:01
 */
@Data
@ApiModel
public class LogisticsAbnormalBatchProcessRequest {

    @ApiModelProperty(value = "更新列表", required = true)
    @NotEmpty
    private List<AbnormalProcessInfo> updatePackList;


}
