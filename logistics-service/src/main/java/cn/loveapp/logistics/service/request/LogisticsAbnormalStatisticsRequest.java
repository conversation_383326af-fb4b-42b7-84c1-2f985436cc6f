package cn.loveapp.logistics.service.request;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.springframework.util.StringUtils;


/**
 * 异常物流监控统计Request
 *
 * <AUTHOR>
 * @Date 2023/6/21 18:31
 */
@Data
public class LogisticsAbnormalStatisticsRequest {

    private static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    /**
     * 异常统计的类型
     */
    private String fields;

    /**
     * 物流单是否必须存在发货时间
     */
    private Boolean isExistConsignTime;

    /**
     * 是否排除交易其他异常
     */
    private Boolean isExcludeOtherAbnormalOfTradeApp;

    /**
     * 异常统计发货开始时间
     */
    private LocalDateTime startConsignTime;

    public void setStartConsignTime(String startConsignTime) {
        if (!StringUtils.isEmpty(startConsignTime)) {
            this.startConsignTime = LocalDateTime.parse(startConsignTime, DF);
        }
    }
}
