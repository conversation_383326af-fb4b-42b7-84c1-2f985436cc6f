package cn.loveapp.logistics.service.request;

import cn.loveapp.logistics.common.dto.LogisticsInfoApiSearchDTO;
import cn.loveapp.logistics.common.dto.PrepareConsignDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 物流轨迹（api版）搜索request
 *
 * <AUTHOR>
 * @Date 2023/6/5 18:13
 */
@Data
public class LogisticsTraceApiSearchRequest extends PrepareConsignDTO {

    /**
     * 搜索列表
     */
    @ApiModelProperty(value = "搜索列表", required = true)
    @NotEmpty
    @Valid
    private List<LogisticsInfoApiSearchDTO> apiSearchList;

}
