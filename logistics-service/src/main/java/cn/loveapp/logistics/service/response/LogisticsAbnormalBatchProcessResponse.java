package cn.loveapp.logistics.service.response;

import cn.loveapp.logistics.service.dto.AbnormalProcessResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 物流单处理response
 * <AUTHOR>
 * @Date 2023/7/5 11:01
 */
@Data
@ApiModel
public class LogisticsAbnormalBatchProcessResponse {

    @ApiModelProperty(value = "更新结果列表")
    private List<AbnormalProcessResult> resultList;

}
