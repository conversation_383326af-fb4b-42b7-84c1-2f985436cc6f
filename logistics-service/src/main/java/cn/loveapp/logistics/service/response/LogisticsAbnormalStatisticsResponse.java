package cn.loveapp.logistics.service.response;

import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 异常物流监控统计Response
 *
 * <AUTHOR>
 * @Date 2023/6/21 18:27
 */
@Data
public class LogisticsAbnormalStatisticsResponse {

    /**
     * 异常统计
     */
    @ApiModelProperty(value = "异常统计")
    @JSONField(name = "abnormal_count")
    @JsonProperty("abnormal_count")
    private LogisticsAbnormalCountDTO abnormalCount;

    /**
     * 异常信息
     */
    @ApiModelProperty(value = "返回异常信息")
    @JSONField(name = "error_msg")
    @JsonProperty("error_msg")
    private String errorMsg;

}
