package cn.loveapp.logistics.service.response;

import cn.loveapp.logistics.common.dto.LogisticsDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 物流轨迹（api版）搜索resopnse
 *
 * <AUTHOR>
 * @Date 2023/6/5 18:42
 */
@Data
@ApiModel
public class LogisticsTraceApiSearchResponse {

    /**
     * 物流详情列表
     */
    @ApiModelProperty(value = "物流详情列表")
    private List<LogisticsDetailDTO> logisticsDetails;
}
