package cn.loveapp.logistics.service.rpc;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.api.dto.LogisticsOrderChangeDTO;
import cn.loveapp.logistics.api.request.*;
import cn.loveapp.logistics.api.response.LogisticsActionTransformResponse;
import cn.loveapp.logistics.api.response.LogisticsUpdateResponse;
import cn.loveapp.logistics.api.response.LogisticsTraceSubscribeResponse;
import cn.loveapp.logistics.api.service.LogisticsRpcApiService;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.common.config.AyLogisticsStatusConfig;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.dto.request.LogisticsInfoRequest;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.LogisticsOrderHandleService;
import cn.loveapp.logistics.common.service.LogisticsTraceHandleService;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import cn.loveapp.logistics.service.request.LogisticsAbnormalListGetRequest;
import cn.loveapp.logistics.service.response.LogisticsAbnormalListGetResponse;
import cn.loveapp.logistics.service.service.LogisticsAbnormalService;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 物流相关RPC接口实现Controller
 *
 * <AUTHOR>
 * @Date 2023/5/31 18:32
 */
@RestController
@RequestMapping(LogisticsRpcApiService.PATH)
public class LogisticsRpcApiServiceImpl implements LogisticsRpcApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsRpcApiServiceImpl.class);

    @Autowired
    private LogisticsOrderHandleService logisticsOrderHandleService;

    @Autowired
    private LogisticsTraceHandleService logisticsTraceHandleService;

    @Autowired
    private AyLogisticsStatusConfig ayLogisticsStatusConfig;

    @Autowired
    private LogisticsAbnormalService logisticsAbnormalService;

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Override
    public CommonApiResponse<LogisticsTraceSubscribeResponse> logisticsTraceSubscribe(LogisticsTraceSubscribeRequest request) {

        LOGGER.logInfo("调用物流轨迹订阅RPC接口，入参：" + JSON.toJSONString(request));

        List<LogisticsOrderSubscribeDTO> logisticsHandles = request.getLogisticsHandles();

        LogisticsTraceSubscribeResponse response = new LogisticsTraceSubscribeResponse();

        if (CollectionUtils.isEmpty(logisticsHandles)) {
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }

        for (LogisticsOrderSubscribeDTO logisticsHandle : logisticsHandles) {
            String logisticsStoreId = LogisticsUtil.getDefaultLogisticsStoreId(logisticsHandle.getLogisticsStoreId());
            logisticsHandle.setLogisticsStoreId(logisticsStoreId);

            LogisticsTraceSubscribeResponse.TraceSubscribeResult subscribeResult = new LogisticsTraceSubscribeResponse.TraceSubscribeResult();
            subscribeResult.setOutSid(logisticsHandle.getOutSid());
            try {
                boolean success = logisticsTraceHandleService.subscribeLogisticsTrace(logisticsHandle);
                subscribeResult.setSuccess(success);
                response.addTraceSubscribeResult(subscribeResult);
            } catch (Exception e) {
                LOGGER.logError("订阅失败：" + e.getMessage(), e);
                subscribeResult.setSuccess(Boolean.FALSE);
                subscribeResult.setErrorMessage("订阅失败：" + e.getMessage());
                response.addTraceSubscribeResult(subscribeResult);
            }
        }
        return CommonApiResponse.success(response);
    }


    @Override
    public CommonApiResponse<LogisticsUpdateResponse> logisticsUpdate(LogisticsUpdateRequest request) {

        LOGGER.logInfo("调用物流同步接口，入参：" + JSON.toJSONString(request));

        LogisticsUpdateResponse response = new LogisticsUpdateResponse();

        List<LogisticsOrderChangeDTO> logisticsUpdates = request.getLogisticsUpdates();
        if (CollectionUtils.isEmpty(logisticsUpdates)) {
            response.setSuccess(false);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }

        boolean success = true;

        for (LogisticsOrderChangeDTO logisticsUpdate : logisticsUpdates) {

            try {
                boolean changeSuccess = logisticsOrderHandleService.logisticsInfoUpdate(logisticsUpdate);
                success &= changeSuccess;
                if (!changeSuccess) {
                    response.addErrorOutSid(logisticsUpdate.getOutSid());
                }
            } catch (LogisticsHandlesException e) {
                LOGGER.logError("更新失败：" + e.getMessage(), e);
                success = false;
                response.addErrorOutSid(logisticsUpdate.getOutSid());
            }
        }

        response.setSuccess(success);

        return CommonApiResponse.success(response);
    }

    @Override
    public CommonApiResponse<LogisticsActionTransformResponse> logisticsActionTransformGet(LogisticsActionTransformRequest request) {
        if (request == null) {
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError);
        }

        LOGGER.logInfo("物流状态映射关系获取接口，入参：" + JSON.toJSONString(request));

        LogisticsActionTransformResponse response = new LogisticsActionTransformResponse();
        if (StringUtils.isEmpty(request.getSourceLogisticsStoreId()) || CollectionUtils.isEmpty(request.getActionList())) {
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError);
        }

        Map<String, AyLogisticsStatus> ayLogisticsStatusMap = new HashMap<>();
        for (String action : request.getActionList()) {
            AyLogisticsStatus status = ayLogisticsStatusConfig.getStatus(action, request.getSourceLogisticsStoreId());
            ayLogisticsStatusMap.put(action, status);
        }
        response.setActionToStatusMap(ayLogisticsStatusMap);

        return CommonApiResponse.success(response);
    }

    @RequestMapping(value = "/abnormal.list.export", method = RequestMethod.POST)
    public CommonApiResponse<LogisticsAbnormalListGetResponse> abnormalListExport(@Validated @RequestBody LogisticsInfoRequest<LogisticsAbnormalListGetRequest> request) {
        UserInfoDTO userInfoDTO = request.getUser();
        LogisticsAbnormalListGetRequest logisticsAbnormalListGetRequest = request.getParameters();

        if (logisticsConfig.isReturnEmpty()) {
            LOGGER.logInfo(userInfoDTO.getNick(), "", "降级直接返回空");
            return CommonApiResponse.failed(CommonApiStatus.Success, null);
        }

        if (Objects.isNull(logisticsAbnormalListGetRequest) || !logisticsAbnormalListGetRequest.checkParams()) {
            LOGGER.logError("物流单失败：参数异常");
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError, null);
        }

        LogisticsAbnormalListGetResponse response = new LogisticsAbnormalListGetResponse();
        try {
            response = logisticsAbnormalService.abnormalListGet(logisticsAbnormalListGetRequest, userInfoDTO);
            return CommonApiResponse.success(response);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError("异常物流单列表获取失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.Failed.code(), e.getMessage(), response);
        } catch (Exception e) {
            LOGGER.logError("异常物流单列表获取失败：" + e.getMessage(), e);
            return CommonApiResponse.failed(CommonApiStatus.ServerError, response);
        }
    }

}
