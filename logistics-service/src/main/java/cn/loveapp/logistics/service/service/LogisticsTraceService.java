package cn.loveapp.logistics.service.service;

import java.util.List;

import cn.loveapp.logistics.common.dto.LogisticsDetailDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.service.dto.LogisticsInfoQuery;
import cn.loveapp.logistics.service.dto.UpdateLogisticsParamsDTO;
import cn.loveapp.logistics.service.exception.DatabaseException;

/**
 *
 * 物流追踪服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-11-05 下午12:10
 */
public interface LogisticsTraceService {
    String SUCCESS = "success";

    /**
     *
     * 获取与oid相关的包裹信息
     *
     * @param query
     *            要查询物流信息的数据,不可为空
     * @return
     */
    List<LogisticsDetailDTO> getLogisticInfo(LogisticsInfoQuery query, UserInfoDTO userInfo);

    /**
     * 更新与logisticsUpdateParams.getOid相关的物流数据
     *
     * @param logisticsDatas
     * @param userInfo
     * @return
     * @throws DatabaseException
     */
    String updateLogisticsInfo(List<UpdateLogisticsParamsDTO> logisticsDatas, UserInfoDTO userInfo)
        throws DatabaseException;

    // List<Map<String,Object>> getOutsid(Integer count);

}
