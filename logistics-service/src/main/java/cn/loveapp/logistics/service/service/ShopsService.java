package cn.loveapp.logistics.service.service;


import cn.loveapp.logistics.common.entity.TargetSellerInfo;

import java.util.List;

/**
 * 多店相关服务
 *
 * <AUTHOR>
 */
public interface ShopsService {
    /**
     * 多店鉴权
     *
     * @param sellerNick
     * @param storeId
     * @param appName
     * @param targetNick
     * @param targetStoreId
     * @param targetAppName
     * @return
     */
    boolean shopsAuth(String sellerNick, String storeId, String appName, String targetNick, String targetStoreId, String targetAppName);

    /**
     * 多个绑定店铺鉴权
     *
     * @param sellerNick
     * @param storeId
     * @param targetSellerList
     * @return
     */
    boolean multiShopsAuth(String sellerNick, String storeId, String appName, List<TargetSellerInfo> targetSellerList);
}
