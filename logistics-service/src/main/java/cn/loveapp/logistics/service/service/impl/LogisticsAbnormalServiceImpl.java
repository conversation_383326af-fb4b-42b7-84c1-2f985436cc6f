package cn.loveapp.logistics.service.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsOrderChangeDTO;
import cn.loveapp.logistics.common.dto.LogisticsPackInfo;
import cn.loveapp.logistics.common.dto.LogisticsQueryDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.LogisticsOrderHandleService;
import cn.loveapp.logistics.common.dto.AyLogisticsOrderInfoSearchListAndAggDto;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import cn.loveapp.logistics.service.dto.AbnormalProcessInfo;
import cn.loveapp.logistics.service.dto.AbnormalProcessResult;
import cn.loveapp.logistics.service.request.LogisticsAbnormalListGetRequest;
import cn.loveapp.logistics.service.response.LogisticsAbnormalListGetResponse;
import cn.loveapp.logistics.service.service.LogisticsAbnormalService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 异常物流相关接口实现
 *
 * <AUTHOR>
 * @Date 2023/7/3 16:52
 */
@Service
public class LogisticsAbnormalServiceImpl implements LogisticsAbnormalService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsAbnormalServiceImpl.class);

    private static final String STRING_NULL = "null";

    @Lazy
    @Autowired
    private LogisticsOrderHandleService logisticsOrderHandleService;

    /**
     * erp异常物流上线时间
     */
    private static final LocalDateTime ERP_ABNORMAL_LOGISTICS_ONLINE_TIME = LocalDateTime.of(2025,4,3,21,0);

    @Override
    public LogisticsAbnormalListGetResponse abnormalListGet(LogisticsAbnormalListGetRequest request,
        UserInfoDTO userInfoDTO) throws LogisticsHandlesException {
        LogisticsAbnormalListGetResponse response = new LogisticsAbnormalListGetResponse();

        LogisticsQueryDTO logisticsQuery = joinLogisticQueryDto(request);
        if (LogisticsUtil.isTradeERP(userInfoDTO.getAppName())) {
            // 限制erp 异常物流查询范围(上线时间开始计算)
            if (logisticsQuery.getStartTime() == null
                || logisticsQuery.getStartTime().isBefore(ERP_ABNORMAL_LOGISTICS_ONLINE_TIME)) {
                logisticsQuery.setStartTime(ERP_ABNORMAL_LOGISTICS_ONLINE_TIME);
            }
        }

        List<LogisticsPackInfo> packList = null;
        List<String> outSidList = new ArrayList<>();
        if (StringUtils.isNotEmpty(logisticsQuery.getOutSid())) {
            // 运单号搜索直接查mongo
            outSidList.add(logisticsQuery.getOutSid());
            response.setTotalResults(1);
            response.setHasNext(false);
        } else {
            // 通过es搜索
            AyLogisticsOrderInfoSearchListAndAggDto listAndAggDto = this.getLogisticsOrderInfoSearchFromEs(logisticsQuery, userInfoDTO);
            Integer totalResults = listAndAggDto.getTotalResults();
            List<LogisticsOrderInfoSearchES> searchList = listAndAggDto.getLogisticsOrderInfoSearchESList();
            if (Objects.isNull(totalResults) || totalResults == 0 || CollectionUtils.isEmpty(searchList)) {
                response.setTotalResults(0);
                response.setHasNext(false);
                response.setPackList(new ArrayList<>());
                return response;
            }
            outSidList = searchList.stream().map(LogisticsOrderInfoSearchES::getOutSid).collect(Collectors.toList());
            response.setTotalResults(listAndAggDto.getTotalResults());
            response.setHasNext(listAndAggDto.getHasNext());
            response.setSearchSortValues(listAndAggDto.getSearchSortValues());
        }

        packList = logisticsOrderHandleService.logisticsPackListGetByOutSidList(logisticsQuery, outSidList, userInfoDTO, request.getIsSearchTrace());
        if (CollectionUtils.isEmpty(packList)) {
            LOGGER.logError("查询失败，ES与Mongo库数据不同步");
            response.setTotalResults(0);
            response.setHasNext(false);
            response.setPackList(new ArrayList<>());
        }

        response.setPackList(packList);

        return response;
    }

    @Override
    public List<AbnormalProcessResult> abnormalBatchProcess(List<AbnormalProcessInfo> abnormalProcessInfoList) {

        List<AbnormalProcessResult> resultList = new ArrayList<>();

        for (AbnormalProcessInfo abnormalProcessInfo : abnormalProcessInfoList) {
            if (StringUtils.isEmpty(abnormalProcessInfo.getOutSid())) {
                LOGGER.logError("运单号为空，跳过");
                continue;
            }
            AbnormalProcessResult result = new AbnormalProcessResult();
            result.setOutSid(abnormalProcessInfo.getOutSid());
            try {
                LogisticsOrderChangeDTO logisticsOrderChangeDTO = new LogisticsOrderChangeDTO();
                BeanUtils.copyProperties(abnormalProcessInfo, logisticsOrderChangeDTO);
                boolean success = logisticsOrderHandleService.logisticsInfoUpdate(logisticsOrderChangeDTO);
                result.setSuccess(success);
            } catch (LogisticsHandlesException e) {
                result.setErrorMsg(e.getMessage());
                result.setSuccess(false);
                LOGGER.logError("更新物流单异常：" + e.getMessage(), e);
            }
            resultList.add(result);
        }
        return resultList;
    }

    /**
     * 获取物流单列表（es）
     * @param logisticsQuery
     * @param userInfoDTO
     * @return
     */
    private AyLogisticsOrderInfoSearchListAndAggDto getLogisticsOrderInfoSearchFromEs(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO)  throws LogisticsHandlesException{

        AyLogisticsOrderInfoSearchListAndAggDto listAndAggDto = logisticsOrderHandleService.logisticsOrderInfoListQueryByLimit(logisticsQuery, userInfoDTO);

        if (Objects.isNull(listAndAggDto) || CollectionUtils.isEmpty(listAndAggDto.getLogisticsOrderInfoSearchESList())) {
            listAndAggDto = new AyLogisticsOrderInfoSearchListAndAggDto();
            listAndAggDto.setHasNext(false);
            listAndAggDto.setTotalResults(0);
            return listAndAggDto;
        }

        List<LogisticsOrderInfoSearchES> searchList = listAndAggDto.getLogisticsOrderInfoSearchESList();

        if (searchList.size() == logisticsQuery.getLimit()) {
            listAndAggDto.setHasNext(true);
        } else {
            listAndAggDto.setHasNext(false);
        }
        return listAndAggDto;
    }

    /**
     * 拼接物流查询参数
     *
     * @param request
     * @return
     */
    private LogisticsQueryDTO joinLogisticQueryDto(LogisticsAbnormalListGetRequest request) {
        LogisticsQueryDTO logisticsQuery = new LogisticsQueryDTO();
        BeanUtils.copyProperties(request, logisticsQuery);

        // 处理searchAfter参数
        List<Object> lastSearchSortValues = request.getLastSearchSortValues();
        if (lastSearchSortValues != null) {
            // 在Form Data格式下将Object数组中的字符串转为原始类型,防止ES类型转换错误
            for (int i = 0; i < lastSearchSortValues.size(); i++) {
                Object lastSearchSortValue = lastSearchSortValues.get(i);
                if (lastSearchSortValue instanceof String) {
                    String lastSearchSortValueStr = (String) lastSearchSortValue;
                    if (STRING_NULL.equals(lastSearchSortValueStr)) {
                        lastSearchSortValue = null;
                    } else if (NumberUtils.isParsable(lastSearchSortValueStr)) {
                        try {
                            lastSearchSortValue = Long.parseLong(lastSearchSortValueStr);
                        } catch (NumberFormatException e) {
                            lastSearchSortValue = Double.parseDouble(lastSearchSortValueStr);
                        }
                    }
                    lastSearchSortValues.set(i, lastSearchSortValue);
                }
            }
        }
        logisticsQuery.setLastSearchSortValues(lastSearchSortValues);
        return logisticsQuery;
    }
}
