package cn.loveapp.logistics.service.service.impl;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.CommonRocketMqQueueHelper;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.config.AyLogisticsStatusConfig;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import cn.loveapp.orders.dto.proto.LogisticsTraceRequest;
import cn.loveapp.orders.dto.proto.OrderMcNotifyProto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.dao.mongo.LogisticsTraceInfoDao;
import cn.loveapp.logistics.common.dto.*;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.service.UserInfoService;
import cn.loveapp.logistics.service.dto.LogisticsInfoQuery;
import cn.loveapp.logistics.service.dto.UpdateLogisticsParamsDTO;
import cn.loveapp.logistics.service.exception.DatabaseException;
import cn.loveapp.logistics.service.exception.ExceptionEnum;
import cn.loveapp.logistics.service.service.LogisticsTraceService;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

/**
 *
 * 物流服务实现
 *
 * <AUTHOR> Shuaifei
 * @email <EMAIL>
 * @create 2018-11-05 下午12:12
 */
@Service
public class LogisticsTraceServiceImpl implements LogisticsTraceService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsTraceServiceImpl.class);

    /**
     * 无效的tid
     */
    public static final String INVALID_TID = "0";

    /**
     * 物流通知
     */
    private static final String NOTIFY_LOGISTICS = "notify_logistics";

    /**
     * 商品已下单物流状态
     */
    private static final String CREATE_ORDER_LOGISTICS_STATUS = "CREATE";

    /**
     * 初级版用户vipflag
     */
    public static final Integer VIP_FLAG_0 = 0;

    @Autowired
    private LogisticsTraceInfoDao logisticsTraceInfoDao;

    @Autowired
    private UserInfoService userInfoService;

    private Timer saveGetInfoTimer;

    /**
     * 物流保存时间 (天)
     */
    @Value("${logistics.ttl.days:25}")
    private int ttlDays;

    @Value("${logistics.international.actions:CAI_,GTMS_,TD_}")
    private List<String> internationalLogistics;

    /**
     * 是否过滤多包裹订单
     */
    @Value("${logistics.filter-multiple-package:false}")
    private boolean filterMultiplePackage = false;

    /**
     * 物流校验非收费用户允许查看物流开关
     */
    @Value("${logistics.vipFlag.check.enable:true}")
    private boolean vipFlagCheckEnable;

    @Autowired
    private CommonRocketMqQueueHelper commonRocketMqQueueHelper;

    @Autowired
    @Qualifier("defaultProducer")
    private DefaultMQProducer logisticsProducer;

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private AyLogisticsStatusConfig ayLogisticsStatusConfig;

    private FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    public LogisticsTraceServiceImpl(MeterRegistry registry) {
        saveGetInfoTimer = registry.timer("物流查询API.QPS");
    }

    @Override
    public List<LogisticsDetailDTO> getLogisticInfo(LogisticsInfoQuery query, UserInfoDTO userInfo) {
        UserInfoResponse userInfoResponse = getUserInfoByNick(userInfo);
        if (userInfoResponse == null) {
            LOGGER.logError(userInfo.getNick(), "-", "getUserInfoByNick 获取不到用户信息");
            return Collections.emptyList();
        } else if (vipFlagCheckEnable && (userInfoResponse.getVipflag() == null || Objects.equals(userInfoResponse.getVipflag(), VIP_FLAG_0))) {
            // vipFlag为空或者0为普通用户
            LOGGER.logInfo(userInfo.getNick(), "-", "非高级版用户不查询");
            return Collections.emptyList();
        }
        String sellerNick = userInfoResponse.getSellerNick();
        String sellerId = userInfoResponse.getSellerId();
        String appName = userInfoResponse.getAppName();
        Timer.Sample sampler = Timer.start();
        try {
            // 返回给controller的容器
            List<LogisticsDetailDTO> logisticsDetailDTOList = new ArrayList<>();

            List<String> tidList = query.getOidList();
            // tid不为空时，优先使用tid做查询条件
            if (StringUtils.isNotEmpty(query.getTid())) {
                tidList = Lists.newArrayList(query.getTid());
            }

            if (query.getInvoiceNoList() != null) {
                if (tidList == null) {
                    tidList = new ArrayList<>();
                }
                // 除TAO订单，数据库中tid字段存的是运单号
                tidList.addAll(query.getInvoiceNoList());
            }

            List<LogisticsTraceInfo> tradeLogisticsAllInfoList = logisticsTraceInfoDao.queryByTidAndInvoiceNo(tidList,
                query.getInvoiceNoList(), sellerId, userInfoResponse.getPlatformId(), userInfoResponse.getAppName());

            // 若返回结果不为空
            if (!CollectionUtils.isEmpty(tradeLogisticsAllInfoList)) {

                // 以描述out_sid进行分组
                Map<String, List<LogisticsTraceInfo>> logisticsDetailDTOByOutSid =
                    tradeLogisticsAllInfoList.stream().collect(Collectors.groupingBy(LogisticsTraceInfo::getOutSid));

                Map<String, String> problemPackage = new HashMap<>();
                // 遍历包裹信息，即遍历每组信息
                for (Map.Entry<String, List<LogisticsTraceInfo>> entry : logisticsDetailDTOByOutSid.entrySet()) {
                    LogisticsDetailDTO logisticsDetailDTO = new LogisticsDetailDTO();

                    // 使用map的key进行去重
                    Map<String, TransitStepDTO> stepList = new HashMap<>();
                    Map<String, String> tids = new HashMap<>();

                    for (LogisticsTraceInfo logisticsTraceInfo : entry.getValue()) {
                        // 对每个包裹进行去重，根据描述去重
                        // 如果有国际物流，则直接返回空，让前端自己去调淘宝接口
                        for (String item : internationalLogistics) {
                            if (logisticsTraceInfo.getAction().startsWith(item)) {
                                LOGGER.logInfo(sellerNick, logisticsTraceInfo.getTid(), "国际物流，直接返回空，让前端自己去调淘宝接口");
                                return new ArrayList<>();
                            }
                        }

                        if (filterMultiplePackage && StringUtils.isEmpty(query.getTid())) {
                            // 若不同的包裹出现相同的订单id，则返回空包裹
                            String problemPackageOutsid = problemPackage.get(logisticsTraceInfo.getTid());
                            if (StringUtils.isNotEmpty(problemPackageOutsid)
                                && !problemPackageOutsid.equals(logisticsTraceInfo.getOutSid())) {
                                LOGGER.logInfo(sellerNick, logisticsTraceInfo.getTid(), "过滤多包裹, 直接返回空");
                                // 若一个订单已经有一个对应的包裹id,且与当前的包裹id不相同，即相当于一笔订单有两个包裹ID，则该笔订单包裹有异常，返回空
                                return new ArrayList<>();
                            } else {
                                problemPackage.put(logisticsTraceInfo.getTid(), logisticsTraceInfo.getOutSid());
                            }
                        }

                        String status = logisticsTraceInfo.getStatus();
                        if (LogisticsUtil.isTradeERP(appName)) {
                            status = ayLogisticsStatusConfig.getStatus(logisticsTraceInfo.getAction(), appName).value();
                        }

                        TransitStepDTO step = new TransitStepDTO(logisticsTraceInfo.getAction(),
                            logisticsTraceInfo.getDesc(), fastDateFormat.format(logisticsTraceInfo.getModified()), status);
                        // 这里稍作改动，将描述和时间都相同的过滤掉
                        stepList.put(logisticsTraceInfo.getDesc() + logisticsTraceInfo.getModified(), step);

                        tids.put(logisticsTraceInfo.getTid(), logisticsTraceInfo.getTid());

                        logisticsDetailDTO.setOutSid(logisticsTraceInfo.getOutSid());
                        logisticsDetailDTO.setCompanyName(logisticsTraceInfo.getCompanyName());
                        logisticsDetailDTO.setCompanyCode(logisticsTraceInfo.getCompanyCode());
                        String logisticsStoreId = logisticsTraceInfo.getLogisticsStoreId();
                        if (StringUtils.isEmpty(logisticsTraceInfo.getLogisticsStoreId())) {
                            // 兼容老数据
                            logisticsStoreId = logisticsTraceInfo.getPlatformId();
                        }
                        logisticsDetailDTO.setLogisticsStoreId(logisticsStoreId);
                    }

                    TracesDTO tracesDTO = new TracesDTO();
                    tracesDTO.setTransitStepInfo(new ArrayList<>(stepList.values()));

                    logisticsDetailDTO.setTraceList(tracesDTO);

                    if (1 == logisticsDetailDTOByOutSid.size()) {
                        // 不需要拆单
                        logisticsDetailDTO.setIsSplit(0);
                    } else {
                        // 需要拆单
                        logisticsDetailDTO.setIsSplit(1);
                    }
                    logisticsDetailDTO.setOid(new ArrayList<>(tids.values()));
                    logisticsDetailDTO.setTid(new ArrayList<>(tids.values()));

                    logisticsDetailDTOList.add(logisticsDetailDTO);
                }
            }

            // 对返回对信息进行排序，先按modify倒叙action（city在scan之后）modify正序city在scan之前
            // 如果 SEND_SCAN SEND_CITY status_time 一致，那么需要先 SEND_CITY 再 SEND_SCAN
            String sort = query.getSort();
            for (LogisticsDetailDTO logisticsDetailDTO : logisticsDetailDTOList) {
                if ("desc".equals(sort)) {
                    logisticsDetailDTO.getTraceList()
                        .setTransitStepInfo(logisticsDetailDTO.getTraceList().getTransitStepInfo().stream()
                            .sorted(Comparator.comparing(TransitStepDTO::getStatusTime)
                                .thenComparing(TransitStepDTO::getAction).reversed())
                            .collect(Collectors.toList()));

                } else if ("asc".equals(sort)) {
                    logisticsDetailDTO.getTraceList()
                        .setTransitStepInfo(logisticsDetailDTO
                            .getTraceList().getTransitStepInfo().stream().sorted(Comparator
                                .comparing(TransitStepDTO::getStatusTime).thenComparing(TransitStepDTO::getAction))
                            .collect(Collectors.toList()));
                }
            }

            return logisticsDetailDTOList;
        } finally {
            sampler.stop(saveGetInfoTimer);
        }
    }

    @Override
    public String updateLogisticsInfo(List<UpdateLogisticsParamsDTO> logisticsDatas, UserInfoDTO userInfo)
        throws DatabaseException {
        UserInfoResponse userInfoResponse = getUserInfoByNick(userInfo);
        if (userInfoResponse == null) {
            LOGGER.logError(userInfo.getNick(), "-", "getUserInfoByNick 获取不到用户信息");
            throw new DatabaseException(ExceptionEnum.UPDATE_DATA_USER_ERROR);
        } else if (vipFlagCheckEnable && (userInfoResponse.getVipflag() == null || Objects.equals(userInfoResponse.getVipflag(), VIP_FLAG_0))) {
            LOGGER.logInfo(userInfo.getNick(), "-", "非高级版用户无需更新，忽略");
            return SUCCESS;
        }

        String currentNick = userInfoResponse.getSellerNick();
        String currentUserId = userInfoResponse.getSellerId();
        LocalDateTime earliestTime = LocalDateTime.now().minusDays(ttlDays);
        Timer.Sample sampler = Timer.start();
        try {
            Map<String, List<UpdateLogisticsParamsDTO>> sellerIdAndUpdateLogisticsParamsDTOMap =
                logisticsDatas.stream().collect(Collectors.groupingBy(UpdateLogisticsParamsDTO::getSellerId));

            // 1. 多店铺物流修补
            for (String sellerId : sellerIdAndUpdateLogisticsParamsDTOMap.keySet()) {
                List<UpdateLogisticsParamsDTO> updateLogisticsParamsDTOS = sellerIdAndUpdateLogisticsParamsDTOMap.get(sellerId);
                if (CollectionUtils.isNotEmpty(updateLogisticsParamsDTOS)) {
                    UpdateLogisticsParamsDTO updateLogisticsParamsDTO = logisticsDatas.get(0);
                    String userId = updateLogisticsParamsDTO.getSellerId();
                    String nick = updateLogisticsParamsDTO.getSellerNick();
                    String platformId = updateLogisticsParamsDTO.getStoreId();
                    String appName = updateLogisticsParamsDTO.getAppName();
                    for (UpdateLogisticsParamsDTO logisticsUpdateParams : updateLogisticsParamsDTOS) {
                        if (null == logisticsUpdateParams.getTraceList()
                            || null == logisticsUpdateParams.getTraceList().getTransitStepInfo()
                            || logisticsUpdateParams.getTraceList().getTransitStepInfo().isEmpty()) {
                            LOGGER.logInfo(nick, userId, "【更新物流信息】 没有要更新的数据, 因该是无需物流的更新, 忽略");
                            break;
                        }

                        // tid集合
                        List<String> waitUpdateLogisticTidList =
                            logisticsUpdateParams.getTid() == null ? Lists.newArrayList() : logisticsUpdateParams.getTid();
                        if (CollectionUtils.isNotEmpty(waitUpdateLogisticTidList)) {
                            // 排除无效的tid
                            waitUpdateLogisticTidList = waitUpdateLogisticTidList.stream().filter(tid -> !INVALID_TID.equals(tid)).collect(Collectors.toList());
                        } else {
                            LOGGER.logInfo(nick, userId, "【更新物流信息】 没有要更新的tid, 忽略");
                            break;
                        }

                        // 2. 通过平台物流流转轨迹transitStepInfoList, 组装多个订单同一物流的待更新物流信息map
                        List<TransitStepDTO> transitStepInfoList = logisticsUpdateParams.getTraceList().getTransitStepInfo();
                        HashMap<String, List<LogisticsTraceInfo>> tidAndWaitInsertLogisticsInfoListMap = new HashMap<>();
                        for (String tid : waitUpdateLogisticTidList) {
                            List<LogisticsTraceInfo> waitInsertLogisticsInfoList = Lists.newArrayList();
                            for (TransitStepDTO transitStep : transitStepInfoList) {
                                if (Objects.equals(transitStep.getAction(), CREATE_ORDER_LOGISTICS_STATUS)) {
                                    // 淘宝物流接口会返回物流轨迹 {"action":"CREATE","status_desc":"商品已经下单","status_time":"2024-12-11 00:21:06"}
                                    // 当库中不存在时 会导致物流监控状态被刷新
                                    continue;
                                }
                                Date date = fastDateFormat.parse(transitStep.getStatusTime());
                                // 排除过期数据
                                if (DateUtil.parseDate(date).isBefore(earliestTime)) {
                                    continue;
                                }

                                LogisticsTraceInfo tidLogistic = new LogisticsTraceInfo(tid, userId,
                                    logisticsUpdateParams.getOutSid(), logisticsUpdateParams.getCompanyName(),
                                    transitStep.getAction(), transitStep.getStatusDesc(),
                                    fastDateFormat.parse(transitStep.getStatusTime()), appName, platformId);
                                waitInsertLogisticsInfoList.add(tidLogistic);
                            }

                            tidAndWaitInsertLogisticsInfoListMap.put(tid, waitInsertLogisticsInfoList);
                        }

                        try {
                            // 查出需要更新物流的所有订单的物流轨迹
                            List<LogisticsTraceInfo> logisticsTraceInfos = logisticsTraceInfoDao
                                .queryByTid(waitUpdateLogisticTidList, userId, platformId, appName, null);
                            Map<String, LogisticsTraceInfo> uniqueFieldandLogisticsTraceInfoMap = null;

                            if (CollectionUtils.isNotEmpty(logisticsTraceInfos)) {
                                // 3. 去重库中已有的数据
                                uniqueFieldandLogisticsTraceInfoMap = new HashMap<>();
                                for (LogisticsTraceInfo logisticsTraceInfo : logisticsTraceInfos) {
                                    String traceInfoAppName = logisticsTraceInfo.getAppName();
                                    String traceInfoPlatformId = logisticsTraceInfo.getPlatformId();
                                    String traceInfoOutSid = logisticsTraceInfo.getOutSid();
                                    String traceInfoAction = logisticsTraceInfo.getAction();
                                    String tid = logisticsTraceInfo.getTid();
                                    String traceInfoModifiedStr =
                                        DateUtil.convertDatetoString(logisticsTraceInfo.getModified()).trim();
                                    String key = traceInfoAppName + traceInfoPlatformId + tid + traceInfoOutSid + traceInfoAction
                                        + traceInfoModifiedStr;

                                    // 物流记录表存在重复，去除重复key
                                    if (!uniqueFieldandLogisticsTraceInfoMap.containsKey(key)) {
                                        uniqueFieldandLogisticsTraceInfoMap.put(key, logisticsTraceInfo);
                                    }
                                }
                            }

                            // 4. 获取库里没有的物流记录发送消息
                            Set<String> tidList = tidAndWaitInsertLogisticsInfoListMap.keySet();
                            for (String tid : tidList) {
                                List<LogisticsTraceInfo> logisticsTraceInfoList = tidAndWaitInsertLogisticsInfoListMap.get(tid);
                                // 需要发送物流消息的数据列表
                                List<LogisticsTraceInfo> needSendLogisticsMsgDataList = Lists.newArrayList();
                                for (LogisticsTraceInfo logisticsTraceInfo : logisticsTraceInfoList) {
                                    String outSid = logisticsTraceInfo.getOutSid();
                                    String action = logisticsTraceInfo.getAction();
                                    String logisticsPlatformId = logisticsTraceInfo.getPlatformId();
                                    String logisticsAppName = logisticsTraceInfo.getAppName();
                                    String key = logisticsAppName + logisticsPlatformId + tid + outSid + action
                                        + DateUtil.convertDatetoString(logisticsTraceInfo.getModified()).trim();

                                    if (uniqueFieldandLogisticsTraceInfoMap != null) {
                                        LogisticsTraceInfo repetitionLogisticsTraceInfo =
                                            uniqueFieldandLogisticsTraceInfoMap.get(key);
                                        if (repetitionLogisticsTraceInfo != null) {
                                            LOGGER.logInfo(sellerId, tid,
                                                "重复的物流信息忽略 outSid: " + outSid + ", action: " + action);
                                            continue;
                                        }
                                    }

                                    // 发送订单物流消息
                                    sendOrderMcLogisticMsgToOrderRouter(logisticsTraceInfo, userId, nick, platformId,
                                        appName);
                                    needSendLogisticsMsgDataList.add(logisticsTraceInfo);
                                }

                                // 发送物流入库消息
                                sendLogisticMsgToLogisticConsumer(needSendLogisticsMsgDataList, userId, nick, platformId,
                                    appName);
                            }
                        } catch (Exception ex) {
                            LOGGER.logError(nick, userId, ExceptionEnum.SQL_INSERT_ERROR.message, ex);
                            return JSON.toJSONString(ExceptionEnum.SQL_INSERT_ERROR);
                        }
                    }
                }
            }

            return SUCCESS;
        } catch (ParseException e) {
            LOGGER.logError(currentNick, currentUserId, ExceptionEnum.UPDATE_DATA_DATE_ERROR.message, e);
            return JSON.toJSONString(ExceptionEnum.UPDATE_DATA_DATE_ERROR);
        } finally {
            sampler.stop(saveGetInfoTimer);
        }
    }

    /**
     * 发送物流消息入库
     *
     * @param logisticsTraceInfoList
     * @param sellerId
     * @param sellerNick
     * @param storeId
     * @param appName
     */
    private void sendLogisticMsgToLogisticConsumer(List<LogisticsTraceInfo> logisticsTraceInfoList,
        String sellerId, String sellerNick, String storeId, String appName) {
        if (CollectionUtils.isEmpty(logisticsTraceInfoList)) {
            return;
        }

        List<LogisticsInfoDTO> LogisticsInfoDTOList = Lists.newArrayList();
        for (LogisticsTraceInfo logisticsTraceInfo : logisticsTraceInfoList) {
            LogisticsInfoDTO logisticsInfoDTO = new LogisticsInfoDTO();
            logisticsInfoDTO.setSellerNick(sellerNick);
            logisticsInfoDTO.setSellerId(sellerId);
            logisticsInfoDTO.setTid(logisticsTraceInfo.getTid());
            logisticsInfoDTO.setOutSid(logisticsTraceInfo.getOutSid());
            logisticsInfoDTO.setAction(logisticsTraceInfo.getAction());
            logisticsInfoDTO.setModified(logisticsTraceInfo.getModified());
            logisticsInfoDTO.setDesc(logisticsTraceInfo.getDesc());
            logisticsInfoDTO.setCompanyName(logisticsTraceInfo.getCompanyName());
            logisticsInfoDTO.setCompanyCode(logisticsTraceInfo.getCompanyCode());
            logisticsInfoDTO.setAppName(logisticsTraceInfo.getAppName());
            logisticsInfoDTO.setPlatformId(logisticsTraceInfo.getPlatformId());
            logisticsInfoDTO.setLogisticsStoreId(logisticsTraceInfo.getLogisticsStoreId());
            logisticsInfoDTO.setStatus(logisticsTraceInfo.getAction());
            LogisticsInfoDTOList.add(logisticsInfoDTO);
        }


        LogisticsTraceRequestProto logisticsTraceRequestProto = new LogisticsTraceRequestProto();
        logisticsTraceRequestProto.setNotifyLogistics(LogisticsInfoDTOList);
        commonRocketMqQueueHelper.push(logisticsConfig.getTopic(), "*", logisticsTraceRequestProto, logisticsProducer);
    }

    /**
     * logisticsTraceInfo
     *
     * @param logisticsTraceInfo
     * @param sellerId
     * @param sellerNick
     * @param storeId
     * @param appName
     */
    private void sendOrderMcLogisticMsgToOrderRouter(LogisticsTraceInfo logisticsTraceInfo, String sellerId,
        String sellerNick, String storeId, String appName) {
        if (CommonPlatformConstants.PLATFORM_TAO.equals(storeId)) {
            LogisticsTraceRequest logisticsTraceRequest = LogisticsTraceInfo.toLogisticsTraceRequest(logisticsTraceInfo);
            logisticsTraceRequest.setSellerNick(sellerNick);
            logisticsTraceRequest.setAppName(appName);
            logisticsTraceRequest.setPlatformId(storeId);
            logisticsTraceRequest.setTopic("logistics");
            OrderMcNotifyProto orderMcNotifyProto = new OrderMcNotifyProto();
            orderMcNotifyProto.setLogisticsTraceRequest(logisticsTraceRequest);
            commonRocketMqQueueHelper.push(logisticsConfig.getTaobaoMcRouterTopic(), "*", orderMcNotifyProto, logisticsProducer);
        } else {
            LOGGER.logInfo(sellerNick, sellerId, "非淘宝用户, 忽略订单物流消息发送");
        }

    }

    private UserInfoResponse getUserInfoByNick(UserInfoDTO userInfo) {
        UserInfoRequest userInfoRequest = new UserInfoRequest();
        userInfoRequest.setApp(userInfo.getAppName());
        userInfoRequest.setPlatformId(userInfo.getStoreId());
        userInfoRequest.setSellerNick(userInfo.getNick());
        return userInfoService.getSellerInfo(userInfoRequest);
    }

}
