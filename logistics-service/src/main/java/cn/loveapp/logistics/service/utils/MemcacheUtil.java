package cn.loveapp.logistics.service.utils;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.service.exception.ExceptionEnum;
import cn.loveapp.logistics.service.exception.SessionException;
import net.spy.memcached.AddrUtil;
import net.spy.memcached.ConnectionFactoryBuilder;
import net.spy.memcached.ConnectionFactoryBuilder.Protocol;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.auth.AuthDescriptor;
import net.spy.memcached.auth.PlainCallbackHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Created by IntelliJ IDEA. User: jason Date: 2018/10/17 Time: 11:26 PM
 */
@Component
public class MemcacheUtil {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MemcacheUtil.class);

    private final String CACHE_PREFIX = "memc.sess.key.";
    private MemcachedClient mc = null;

    @Value("${ocs.memcache.host}")
    private String host;

    @Value("${ocs.memcache.username}")
    private String userName;

    @Value("${ocs.memcache.password}")
    private String password;

    public String getCache(String cacheKey) {
        try {
            MemcachedClient memcachedClient = getMemcachedClient(false);
            String key = CACHE_PREFIX + cacheKey;
            Object value = memcachedClient.get(key);
            if (null != value) {
                return value.toString();
            } else {
                return "";
            }
        } catch (IOException ex) {
            LOGGER.logError("Couldn't create a connection,bailing out:" + ex.getMessage(), ex);
        }
        return null;
    }

    public MemcachedClient getMemcachedClient(boolean force) throws IOException {
        if (mc == null || force) {
            AuthDescriptor ad =
                new AuthDescriptor(new String[] {"PLAIN"}, new PlainCallbackHandler(userName, password));
            // 然后连接使用ConnectionFactoryBuilder，二进制是必须的
            mc = new MemcachedClient(
                new ConnectionFactoryBuilder().setProtocol(Protocol.BINARY).setAuthDescriptor(ad).build(),
                AddrUtil.getAddresses(host));
        }
        return mc;
    }

    public SerializedPhpParser getSessionParser(String sessionID) throws SessionException {

        // LOGGER.logInfo("【物流接口】"+"cokie="+sessionID);
        if (StringUtils.isEmpty(sessionID)) {
            LOGGER.logError("【物流接口】" + "SESSIONID异常");
            throw new SessionException(ExceptionEnum.SESSIONID_ERROR);
        }

        // 3.根据SESSIONID在memcache中取用户信息
        String sessionContent = this.getCache(sessionID);

        // String sessionContent =
        // "table_id|s:3:\"436\";corp_id|N;db_id|N;ismac|s:1:\"0\";tag|N;taobao_user_nick|s:14:\"浅梦19961011\";taobao_user_id|s:10:\"2231153436\";sub_taobao_user_nick|s:0:\"\";sub_taobao_user_id|s:1:\"0\";vipflag|i:1;order_cycle_end|s:19:\"2019-05-13
        // 00:00:00\";h|s:1:\"0\";m_taobao_user_nick|s:14:\"浅梦19961011\";nick|s:14:\"浅梦19961011\";sub_nick|s:0:\"\";user_id|s:10:\"2231153436\";createdate|s:19:\"2018-07-27
        // 17:31:37\";is_needauth|s:1:\"0\";roleid|s:1:\"B\";";
        LOGGER.logInfo("【物流接口】sessionID=" + sessionID + ", memcache缓存= " + sessionContent);

        if (StringUtils.isBlank(sessionContent)) {
            LOGGER.logError("【物流接口】 缓存失效sessionID=" + sessionID);
            throw new SessionException(ExceptionEnum.CACHE_INVALID);
        }

        // 4.返回解析器
        return new SerializedPhpParser(sessionContent);
    }
}
