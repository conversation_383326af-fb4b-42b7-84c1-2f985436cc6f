package cn.loveapp.logistics.service.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

public class SerializedPhpParser {

    private final String input;

    private static final String SEMICOLON_SEPARATOR = ";";
    private static final String COLON_SEPARATOR = ":";
    private static final String VERTICAL_BAR_SEPARATOR = "\\|";

    public static final String TAOBAO_USER_NICK = "taobao_user_nick";

    /**
     * 用户昵称
     */
    public static final String USER_NICK = "nick";

    public SerializedPhpParser(String input) {
        this.input = input;
    }

    public String parse(String key) {
        String[] inputSplit = input.split(SEMICOLON_SEPARATOR);
        String value = "";
        for (String is : inputSplit) {
            String[] isSplit = is.split(VERTICAL_BAR_SEPARATOR);
            String mapKey = isSplit[0];
            if (true == isSplit[1].contains(COLON_SEPARATOR) && key.equals(mapKey)) {
                String[] sParse = isSplit[1].split(COLON_SEPARATOR);
                String type = sParse[0];
                if ("N".equals(type)) {
                    value = "";
                } else {
                    value = sParse[2].replace("\"", "");
                    break;
                }
            } else {
                value = "";
            }
        }
        return value;
    }

    public String getInput() {
        return input;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }
}
