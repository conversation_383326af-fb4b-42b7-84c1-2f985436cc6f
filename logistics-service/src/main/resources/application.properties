spring.application.name=${APPLICATION_NAME:logistics-service}
spring.profiles.active=dev
## \u662F\u5426\u5141\u8BB8apollo
loveapp.apollo.enabled=true
## apollo
app.id=cn.loveapp.logistics
apollo.bootstrap.enabled=${loveapp.apollo.enabled}
## \u516C\u5171namespace\u5FC5\u987B\u653E\u540E\u9762
apollo.bootstrap.namespaces=logistics-service,logistics-mongodb,application,service-registry
env=${spring.profiles.active}

server.tomcat.max-part-count=-1

spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=10000,expireAfterAccess=5m

spring.data.mongodb.repositories.type=none
spring.data.elasticsearch.repositories.enabled=false
