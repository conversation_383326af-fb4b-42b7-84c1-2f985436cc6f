// package cn.loveapp.logistics.service.service.impl;
//
// import cn.loveapp.common.constant.CommonAppConstants;
// import cn.loveapp.common.constant.CommonPlatformConstants;
// import cn.loveapp.logistics.common.entity.LogisticsTraceInfo;
// import cn.loveapp.logistics.service.dao.logistics.LogisticsTranceInfoDao;
// import cn.loveapp.logistics.service.dto.*;
// import cn.loveapp.logistics.service.exception.DatabaseException;
// import cn.loveapp.logistics.service.exception.ExceptionEnum;
// import cn.loveapp.logistics.service.service.LogisticsTraceService;
// import org.junit.Assert;
// import org.junit.Before;
// import org.junit.Ignore;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
// import org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration;
// import org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.boot.test.mock.mockito.MockBean;
// import org.springframework.jdbc.datasource.DataSourceTransactionManager;
// import org.springframework.test.context.junit4.SpringRunner;
//
// import java.time.LocalDateTime;
// import java.time.format.DateTimeFormatter;
// import java.util.ArrayList;
// import java.util.Arrays;
// import java.util.List;
//
// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.Mockito.when;
//
// @Ignore
// @RunWith(SpringRunner.class)
// @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
// classes = {LogisticsTraceServiceImpl.class, MetricsAutoConfiguration.class,
// CompositeMeterRegistryAutoConfiguration.class,
// ConfigurationPropertiesAutoConfiguration.class})
// public class LogisticsTraceServiceImplTest {
//
// @Autowired
// private LogisticsTraceService logisticsTraceService;
//
// @MockBean
// private LogisticsTranceInfoDao logisticsTranceInfoDao;
//
// @MockBean
// private DataSourceTransactionManager dataSourceTransactionManager;
//
// private DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//
// String oid = "111,222,333,444";
// List<String> oidList = Arrays.asList(oid.split(","));
// private Integer listId = 0;
// private String sort = "asc";
//
// LogisticsTraceInfo
// lo1 = new LogisticsTraceInfo(1L,"张三","2778001","2778001",1,"111","111","EMS","SIGNED",0,
// LocalDateTime.parse("2018-01-01
// 10:00:00",df),"交易成功",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// //包裹去重测试例
// LogisticsTraceInfo lo2 = new LogisticsTraceInfo(2L,"张三","2778001","2778001",1,"222","111","EMS","TRADE_SUCCESS",0,
// LocalDateTime.parse("2017-01-01 10:00:00",df),"【宁波市】快件已到达
// 宁波江北区",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// LogisticsTraceInfo lo3 = new LogisticsTraceInfo(3L,"张三","2778001","2778001",1,"333","222","EMS","SIGNED",0,
// LocalDateTime.parse("2016-01-01 10:00:00",df),"【宁波市】快件已到达
// 宁波江北区",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// LogisticsTraceInfo lo4 = new LogisticsTraceInfo(4L,"张三","2778001","2778001",1,"444","333","EMS","SIGNED",0,
// LocalDateTime.parse("2015-01-01
// 10:00:00",df),"交易成功",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// //排序测试例
// LogisticsTraceInfo lo5 = new LogisticsTraceInfo(5L,"张三","2778001","2778001",1,"444","333","EMS","SEND_SCAN",0,
// LocalDateTime.parse("2014-01-01
// 10:00:00",df),"交易成功",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// LogisticsTraceInfo lo6 = new LogisticsTraceInfo(6L,"张三","2778001","2778001",1,"444","333","韵达","SEND_CITY",1,
// LocalDateTime.parse("2014-01-01 10:00:00",df),"【宁波市】快件已到达
// 宁波江北区",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// LogisticsTraceInfo lo7 = new LogisticsTraceInfo(7L,"张三","2778001","2778001",1,"444","333","圆通","SIGNED",1,
// LocalDateTime.parse("2013-01-01 10:00:00",df),"【永州市】快件已到达
// 蓝山县",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// //国际物流测试例
// LogisticsTraceInfo lo8 = new LogisticsTraceInfo(8L,"张三","2778001","2778001",1,"444","333","韵达","CAI_CITY",1,
// LocalDateTime.parse("2014-01-01
// 10:00:00",df),"【宁波市】快件已到达",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// //问题包裹测试例
// LogisticsTraceInfo lo9 = new LogisticsTraceInfo(9L,"张三","2778001","2778001",1,"444","333","韵达","GTMS_CITY",1,
// LocalDateTime.parse("2014-01-01
// 10:00:00",df),"【宁波市】快件已到达",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// LogisticsTraceInfo lo10 = new LogisticsTraceInfo(10L,"张三","2778001","2778001",1,"444","333","韵达","GTMS_CITY",1,
// LocalDateTime.parse("2014-01-01
// 10:00:00",df),"【宁波市】快件已到达",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// //同一单号有多个包裹
// LogisticsTraceInfo lo11 = new LogisticsTraceInfo(11L,"张三","2778001","2778001",1,"444","222","韵达","SIGNED",1,
// LocalDateTime.parse("2014-01-01
// 10:00:00",df),"【河南省】",LocalDateTime.now(),LocalDateTime.now(),LocalDateTime.now(),"111","111");
// List<LogisticsTraceInfo> logisticsTraceInfoList = new ArrayList<>();
//
// @Before
// public void setUp(){
//
// }
//
// @Test
// public void getLogisticInfo() {
// when(logisticsTranceInfoDao.queryByTidAndSort(oidList,listId,sort)).thenReturn(logisticsTraceInfoList);
//
// for (int i = 0; i < 4; i++){
// if (0 == i){
// //包裹去重测试例及排序
// logisticsTraceInfoList.add(lo1);
// logisticsTraceInfoList.add(lo2);
// logisticsTraceInfoList.add(lo3);
// logisticsTraceInfoList.add(lo4);
// logisticsTraceInfoList.add(lo5);
// logisticsTraceInfoList.add(lo6);
// logisticsTraceInfoList.add(lo7);
// }
// if (1 == i){
// //国际物流测试例
// logisticsTraceInfoList.add(lo8);
// }
// if (2 == i){
// logisticsTraceInfoList.remove(lo8);
// //同一单号有多个包裹
// logisticsTraceInfoList.add(lo11);
// }
// if (3 == i){
// logisticsTraceInfoList.remove(lo11);
// //问题包裹测试例
// logisticsTraceInfoList.add(lo9);
// logisticsTraceInfoList.add(lo10);
// }
//
// UserInfoDTO userInfoDTO = new UserInfoDTO();
// userInfoDTO.setNick("张三");
// userInfoDTO.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
// userInfoDTO.setAppName(CommonAppConstants.APP_TRADE);
// List<LogisticsDetailDTO> logisticsDetailDTOList = logisticsTraceService.getLogisticInfo(oidList,sort,userInfoDTO);
//
// if (0 == i){
// Assert.assertNotNull(logisticsDetailDTOList);
// //3个包裹
// Assert.assertEquals(3,logisticsDetailDTOList.size());
// //3个路径
// Assert.assertEquals(4,logisticsDetailDTOList.get(2).getTraceList().getTransitStepInfo().size());
// }
// if (1 == i || 2 == i || 3 == i){
// Assert.assertEquals(0,logisticsDetailDTOList.size());
// }
// }
// }
//
//
// @Test
// public void updateLogisticsInfo() {
// when(logisticsTranceInfoDao.deleteByTid(oidList,listId)).thenReturn(2);
// //when(logisticsTranceInfoDao.insertMulti(logisticsTraceInfoList,listId)).thenReturn(2);
// when(logisticsTranceInfoDao.insert(any())).thenReturn(1);
// LogisticsUpdateParamsDTO tm1 = new LogisticsUpdateParamsDTO();
// String nick = "111111";
//
// UserInfoDTO userInfoDTO = new UserInfoDTO();
// userInfoDTO.setNick(nick);
// userInfoDTO.setStoreId(CommonPlatformConstants.PLATFORM_TAO);
// userInfoDTO.setAppName(CommonAppConstants.APP_TRADE);
// //1.未查到要更新的订单的情况
// try {
// logisticsTraceService.updateLogisticsInfo(tm1,userInfoDTO);
// Assert.fail();
// } catch (DatabaseException e) {
// Assert.assertEquals(ExceptionEnum.UPDATE_DATA_IS_EMPTY,e.getExceptionEnum());
// }
//
// //2.没有要更新的数据的情况
// SubTidsDTO subTidsDTO = new SubTidsDTO(oidList);
// tm1.setSub_tids(subTidsDTO);
// try {
// logisticsTraceService.updateLogisticsInfo(tm1,userInfoDTO);
// Assert.fail();
// } catch (DatabaseException e) {
// Assert.assertEquals(ExceptionEnum.UPDATE_DATA_IS_EMPTY,e.getExceptionEnum());
// }
//
// List<TransitStepDTO> transit_step_info = Arrays.asList(new TransitStepDTO("1111","1111","2018-08-17 11:36:34"),new
// TransitStepDTO("222","222","2018-08-17 11:36:34"));
// TracesDTO tracesDTO = new TracesDTO();
// tracesDTO.setTransitStepInfo(transit_step_info);
// tm1.setTrace_list(tracesDTO);
//
// //3.数据库插入错误 -- 由于DataSourceTransactionManager.getTransaction是final方法, 无法mock
//// String result = "";
//// try {
//// result = logisticsTraceService.updateLogisticsInfo(tm1,nick,userId,listId);
//// Assert.assertEquals("success",result);
//// } catch (DatabaseException e) {
//// //Assert.fail();
//// }
//
// //doThrow().when(logisticsTranceInfoDao).insert(any());
// /*when(logisticsTranceInfoDao.insert(any())).thenThrow(new Exception());
// try {
// result = logisticsTraceService.updateLogisticsInfo(tm1,nick,userId,listId);
// } catch (DatabaseException e) {
// Assert.assertEquals(JSON.toJSONString(ExceptionEnum.SQL_INSERT_ERROR),result);
// }*/
// }
//
// }
