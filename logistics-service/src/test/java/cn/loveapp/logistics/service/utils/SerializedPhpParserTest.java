package cn.loveapp.logistics.service.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @Since 2020/6/23 10:35
 */
class SerializedPhpParserTest {

    @Test
    void parseTest() {
        String input =
            "nick|s:14:\"pdd68211975897\";pdd_name|s:14:\"pdd68211975897\";pdd_id|s:9:\"682119758\";pdd_app|s:8:\"guandian\";pdd_vipTime|s:19:\"2020-06-23 11:16:51\";pdd_create|s:19:\"2020-06-16 11:17:48\";pdd_vipFlag|i:5;user_id|s:9:\"682119758\";vipflag|i:5;table_id|s:3:\"758\";list_id|s:3:\"758\";store_id|s:3:\"PDD\";login_terminal|s:2:\"pc\";app_name|s:8:\"guanDian\"";
        SerializedPhpParser serializedPhpParser = new SerializedPhpParser(input);
        String parse = serializedPhpParser.parse(SerializedPhpParser.USER_NICK);
        Assertions.assertEquals("pdd68211975897", parse);
    }

    @Test
    void getInputTest() {
        String input =
            "nick|s:14:\"pdd68211975897\";pdd_name|s:14:\"pdd68211975897\";pdd_id|s:9:\"682119758\";pdd_app|s:8:\"guandian\";pdd_vipTime|s:19:\"2020-06-23 11:16:51\";pdd_create|s:19:\"2020-06-16 11:17:48\";pdd_vipFlag|i:5;user_id|s:9:\"682119758\";vipflag|i:5;table_id|s:3:\"758\";list_id|s:3:\"758\";store_id|s:3:\"PDD\";login_terminal|s:2:\"pc\";app_name|s:8:\"guanDian\"";
        SerializedPhpParser serializedPhpParser = new SerializedPhpParser(input);
        String input1 = serializedPhpParser.getInput();
        Assertions.assertEquals(input, input1);
    }
}
