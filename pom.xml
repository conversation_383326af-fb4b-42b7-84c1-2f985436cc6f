<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.loveapp.logistics</groupId>
    <artifactId>logistics-services-group</artifactId>
    <packaging>pom</packaging>
    <version>1.1-SNAPSHOT</version>
    <modules>
        <module>logistics-service</module>
        <module>logistics-api</module>
        <module>logistics-onsconsumer</module>
        <module>logistics-abnormal-consumer</module>
        <module>logistics-common</module>
    </modules>

    <parent>
        <groupId>cn.loveapp.common</groupId>
        <artifactId>common-spring-boot-parent</artifactId>
        <version>2.0.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <name>爱用-物流</name>
    <description>爱用-物流</description>

    <organization>
        <name>Loveapp Inc.</name>
        <url>http://www.aiyongbao.com</url>
    </organization>

    <properties>
        <logistics-api.version>2.0.0-SNAPSHOT</logistics-api.version>
        <uac-api.version>2.0.0-SNAPSHOT</uac-api.version>
        <orders-api.version>2.0.0-SNAPSHOT</orders-api.version>
        <shops-api.version>2.0.0-SNAPSHOT</shops-api.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.loveapp.logistics</groupId>
                <artifactId>logistics-api</artifactId>
                <version>${logistics-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.loveapp.common</groupId>
                        <artifactId>common-spring-boot-parent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.loveapp.uac</groupId>
                <artifactId>uac-api</artifactId>
                <version>${uac-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.loveapp.common</groupId>
                        <artifactId>common-spring-boot-parent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.loveapp.orders</groupId>
                <artifactId>orders-api</artifactId>
                <version>${orders-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.loveapp.common</groupId>
                        <artifactId>common-spring-boot-parent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.loveapp.shops</groupId>
                <artifactId>shops-api</artifactId>
                <version>${shops-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.loveapp.common</groupId>
                        <artifactId>common-spring-boot-parent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
